/**
 * Electronics Shop Template - Main CSS
 * Website bán thiết bị điện tử cũ
 */

/* ===== CSS Variables ===== */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --font-family-base: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-family-heading: 'Arial', sans-serif;
    
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    --transition: all 0.3s ease;
}

/* ===== Base Styles ===== */
body {
    font-family: var(--font-family-base);
    line-height: 1.6;
    color: var(--dark-color);
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-heading);
    font-weight: 600;
    line-height: 1.2;
}

a {
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--primary-color);
    opacity: 0.8;
}

/* ===== Layout Styles ===== */
.boxed-layout {
    background-color: #f5f5f5;
}

.boxed-layout .container {
    background-color: white;
    box-shadow: var(--box-shadow-lg);
}

/* ===== Header Styles ===== */
.site-header {
    position: sticky;
    top: 0;
    z-index: 1000;
}

.top-bar {
    font-size: 0.875rem;
}

.top-bar a {
    color: white !important;
    text-decoration: none;
}

.top-bar a:hover {
    opacity: 0.8;
}

.main-header {
    border-bottom: 1px solid #dee2e6;
}

.site-logo img {
    max-height: 60px;
    width: auto;
}

.site-title {
    font-size: 1.75rem;
    font-weight: 700;
}

.site-tagline {
    font-size: 0.875rem;
}

/* ===== Navigation Styles ===== */
.main-navigation {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
}

.main-navigation .navbar-nav .nav-link {
    color: white !important;
    font-weight: 500;
    padding: 1rem 1.5rem;
    transition: var(--transition);
}

.main-navigation .navbar-nav .nav-link:hover,
.main-navigation .navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

/* ===== Search Styles ===== */
.header-search .form-control {
    border-radius: 25px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1.25rem;
}

.header-search .btn {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
}

/* ===== Cart Styles ===== */
.header-cart {
    position: relative;
}

.cart-icon {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ===== Product Styles ===== */
.product-card {
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    transition: var(--transition);
    overflow: hidden;
    height: 100%;
}

.product-card:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-5px);
}

.product-image {
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-condition {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
}

.condition-like-new { background-color: var(--success-color); }
.condition-good { background-color: var(--info-color); }
.condition-fair { background-color: var(--warning-color); }
.condition-needs-repair { background-color: var(--danger-color); }

.product-info {
    padding: 1rem;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.product-title a {
    color: inherit;
    text-decoration: none;
}

.product-title a:hover {
    color: var(--primary-color);
}

.product-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--danger-color);
    margin-bottom: 0.5rem;
}

.product-original-price {
    font-size: 1rem;
    color: var(--secondary-color);
    text-decoration: line-through;
    margin-left: 0.5rem;
}

.product-rating {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.stars {
    color: #ffc107;
    margin-right: 0.5rem;
}

.rating-count {
    font-size: 0.875rem;
    color: var(--secondary-color);
}

.product-warranty {
    font-size: 0.875rem;
    color: var(--success-color);
    margin-bottom: 1rem;
}

.product-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-add-to-cart {
    flex: 1;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    font-weight: 600;
    transition: var(--transition);
}

.btn-add-to-cart:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    color: white;
}

.btn-wishlist {
    background-color: transparent;
    border: 2px solid var(--secondary-color);
    color: var(--secondary-color);
    width: 45px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.btn-wishlist:hover {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

/* ===== Category Styles ===== */
.category-card {
    text-align: center;
    padding: 2rem 1rem;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    transition: var(--transition);
    background-color: white;
}

.category-card:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-3px);
}

.category-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.category-name {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.category-count {
    color: var(--secondary-color);
    font-size: 0.875rem;
}

/* ===== Banner Styles ===== */
.hero-banner {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-cta {
    background-color: white;
    color: var(--primary-color);
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    transition: var(--transition);
}

.hero-cta:hover {
    background-color: var(--light-color);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

/* ===== Footer Styles ===== */
.site-footer {
    background-color: var(--dark-color) !important;
}

.footer-content h5 {
    color: white;
    margin-bottom: 1rem;
}

.footer-content ul {
    list-style: none;
    padding: 0;
}

.footer-content ul li {
    margin-bottom: 0.5rem;
}

.footer-content ul li a {
    color: #adb5bd;
    text-decoration: none;
    transition: var(--transition);
}

.footer-content ul li a:hover {
    color: white;
}

.social-links a {
    font-size: 1.25rem;
    transition: var(--transition);
}

.social-links a:hover {
    transform: translateY(-2px);
}

/* ===== Responsive Styles ===== */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .product-card {
        margin-bottom: 1rem;
    }
    
    .header-search {
        margin: 1rem 0;
    }
    
    .main-navigation .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
    }
}

/* ===== Animation Classes ===== */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

/* ===== Utility Classes ===== */
.text-primary { color: var(--primary-color) !important; }
.bg-primary { background-color: var(--primary-color) !important; }
.border-primary { border-color: var(--primary-color) !important; }

.shadow-custom {
    box-shadow: var(--box-shadow-lg);
}

.rounded-custom {
    border-radius: var(--border-radius);
}
