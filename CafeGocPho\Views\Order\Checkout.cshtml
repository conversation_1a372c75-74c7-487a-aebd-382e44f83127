@model GocPho.Models.CheckoutViewModel
@{
    ViewData["Title"] = "Thanh toán";
}

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-4">
                <h1 class="display-5 fw-bold">
                    <i class="fas fa-credit-card text-warning me-2"></i>Thanh toán đơn hàng
                </h1>
                <p class="text-muted">Vui lòng kiểm tra thông tin và hoàn tất đặt hàng</p>
            </div>

            <form asp-action="Checkout" method="post">
                <div class="row">
                    <!-- Order Information -->
                    <div class="col-lg-8">
                        <div class="card shadow-sm border-0 mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="fas fa-user me-2"></i>Thông tin giao hàng
                                </h5>
                            </div>
                            <div class="card-body">
                                <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label asp-for="CustomerName" class="form-label">Họ và tên *</label>
                                        <input asp-for="CustomerName" class="form-control" placeholder="Nhập họ và tên">
                                        <span asp-validation-for="CustomerName" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label asp-for="PhoneNumber" class="form-label">Số điện thoại *</label>
                                        <input asp-for="PhoneNumber" class="form-control" placeholder="Nhập số điện thoại">
                                        <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label asp-for="DeliveryAddress" class="form-label">Địa chỉ giao hàng *</label>
                                    <textarea asp-for="DeliveryAddress" class="form-control" rows="3" 
                                              placeholder="Nhập địa chỉ chi tiết (số nhà, tên đường, phường/xã, quận/huyện)"></textarea>
                                    <span asp-validation-for="DeliveryAddress" class="text-danger"></span>
                                </div>
                                
                                <div class="mb-3">
                                    <label asp-for="Notes" class="form-label">Ghi chú (tùy chọn)</label>
                                    <textarea asp-for="Notes" class="form-control" rows="2" 
                                              placeholder="Ghi chú thêm cho đơn hàng (ví dụ: ít đường, nhiều đá...)"></textarea>
                                    <span asp-validation-for="Notes" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Delivery Info -->
                        <div class="card shadow-sm border-0">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-shipping-fast me-2"></i>Thông tin giao hàng
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-clock text-warning me-2"></i>Thời gian giao hàng</h6>
                                        <p class="text-muted mb-3">30-60 phút (tùy theo khu vực)</p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-money-bill text-success me-2"></i>Phương thức thanh toán</h6>
                                        <p class="text-muted mb-3">Thanh toán khi nhận hàng (COD)</p>
                                    </div>
                                </div>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Lưu ý:</strong> Chúng tôi sẽ gọi điện xác nhận đơn hàng trong vòng 5-10 phút. 
                                    Vui lòng giữ máy để nhận cuộc gọi.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="col-lg-4">
                        <div class="card shadow-sm border-0">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="fas fa-receipt me-2"></i>Tóm tắt đơn hàng
                                </h5>
                            </div>
                            <div class="card-body">
                                @if (Model.CartItems.Any())
                                {
                                    @foreach (var item in Model.CartItems)
                                    {
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div class="flex-grow-1">
                                                <h6 class="mb-0">@item.Product?.Name</h6>
                                                <small class="text-muted">@item.Quantity x @item.Product?.Price.ToString("N0") VNĐ</small>
                                            </div>
                                            <span class="fw-bold">@item.TotalPrice.ToString("N0") VNĐ</span>
                                        </div>
                                    }
                                    <hr>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Tạm tính:</span>
                                        <span>@Model.TotalAmount.ToString("N0") VNĐ</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-3">
                                        <span>Phí giao hàng:</span>
                                        <span class="text-success">Miễn phí</span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between mb-4">
                                        <span class="h5">Tổng cộng:</span>
                                        <span class="h5 text-warning fw-bold">@Model.TotalAmount.ToString("N0") VNĐ</span>
                                    </div>
                                }

                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-warning btn-lg">
                                        <i class="fas fa-check me-2"></i>Xác nhận đặt hàng
                                    </button>
                                    <a href="@Url.Action("Index", "Cart")" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Quay lại giỏ hàng
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Security Info -->
                        <div class="card shadow-sm border-0 mt-3">
                            <div class="card-body text-center">
                                <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                                <h6>Đặt hàng an toàn</h6>
                                <small class="text-muted">Thông tin của bạn được bảo mật tuyệt đối</small>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
