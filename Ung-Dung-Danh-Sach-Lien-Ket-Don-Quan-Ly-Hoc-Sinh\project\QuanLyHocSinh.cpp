#include <iostream>
#include <cstring>
#include <iomanip>
using namespace std;

// Cấu trúc dữ liệu học sinh
struct HocSinh {
    char <PERSON><PERSON>[10];           // M<PERSON> số học sinh
    char HoTen[50];          // Họ tên học sinh
    int NamSinh;             // Năm sinh
    int Khoi;                // Kh<PERSON>i (10, 11, 12)
    char Lop[10];            // Lớp
    float DiemTrungBinh;     // Điểm trung bình
};

// Cấu trúc nút trong danh sách liên kết đơn
struct Node {
    HocSinh data;    // Dữ liệu học sinh
    Node* next;      // Con trỏ trỏ đến nút tiếp theo
    
    // Constructor
    Node(HocSinh hs) {
        data = hs;
        next = NULL;
    }
};

// Hàm hiển thị menu
void displayMenu() {
    cout << "\n========== QUẢN LÝ DANH SÁCH HỌC SINH ==========" << endl;
    cout << "1. Thêm học sinh mới" << endl;
    cout << "2. Hiển thị danh sách học sinh" << endl;
    cout << "3. Tìm kiếm học sinh" << endl;
    cout << "4. Xóa học sinh" << endl;
    cout << "5. Cập nhật thông tin học sinh" << endl;
    cout << "6. Sắp xếp danh sách" << endl;
    cout << "7. Thống kê dữ liệu" << endl;
    cout << "8. Trích lọc dữ liệu" << endl;
    cout << "0. Thoát chương trình" << endl;
    cout << "===============================================" << endl;
    cout << "Nhập lựa chọn của bạn: ";
}

// Hàm nhập thông tin học sinh
HocSinh nhapThongTinHocSinh() {
    HocSinh hs;
    
    cout << "Nhập mã số học sinh: ";
    cin >> hs.MaSo;
    cin.ignore(); // Xóa ký tự newline
    
    cout << "Nhập họ tên: ";
    cin.getline(hs.HoTen, 50);
    
    cout << "Nhập năm sinh: ";
    cin >> hs.NamSinh;
    
    cout << "Nhập khối (10/11/12): ";
    cin >> hs.Khoi;
    
    cout << "Nhập lớp: ";
    cin >> hs.Lop;
    
    cout << "Nhập điểm trung bình: ";
    cin >> hs.DiemTrungBinh;
    
    return hs;
}

// Hàm hiển thị thông tin một học sinh
void displayStudent(const HocSinh& hs) {
    cout << left << setw(8) << hs.MaSo 
         << setw(25) << hs.HoTen
         << setw(10) << hs.NamSinh
         << setw(6) << hs.Khoi
         << setw(8) << hs.Lop
         << setw(8) << fixed << setprecision(2) << hs.DiemTrungBinh << endl;
}

// Hàm hiển thị header cho bảng
void displayHeader() {
    cout << "\n" << string(75, '=') << endl;
    cout << left << setw(8) << "Mã số" 
         << setw(25) << "Họ tên"
         << setw(10) << "Năm sinh"
         << setw(6) << "Khối"
         << setw(8) << "Lớp"
         << setw(8) << "Điểm TB" << endl;
    cout << string(75, '-') << endl;
}

// Thêm học sinh vào cuối danh sách
void insertAtEnd(Node*& head, HocSinh hs) {
    Node* newNode = new Node(hs);
    
    if (head == NULL) {
        head = newNode;
        return;
    }
    
    Node* temp = head;
    while (temp->next != NULL) {
        temp = temp->next;
    }
    temp->next = newNode;
}

// Hiển thị tất cả học sinh
void displayAllStudents(Node* head) {
    if (head == NULL) {
        cout << "Danh sách rỗng!" << endl;
        return;
    }
    
    displayHeader();
    Node* temp = head;
    int count = 0;
    while (temp != NULL) {
        displayStudent(temp->data);
        temp = temp->next;
        count++;
    }
    cout << string(75, '=') << endl;
    cout << "Tổng số học sinh: " << count << endl;
}

// Tìm kiếm học sinh theo mã số
Node* searchByMaSo(Node* head, const char* maSo) {
    Node* temp = head;
    while (temp != NULL) {
        if (strcmp(temp->data.MaSo, maSo) == 0) {
            return temp;
        }
        temp = temp->next;
    }
    return NULL;
}

// Xóa học sinh theo mã số
void deleteByMaSo(Node*& head, const char* maSo) {
    if (head == NULL) {
        cout << "Danh sách rỗng!" << endl;
        return;
    }
    
    // Nếu nút đầu chứa mã số cần xóa
    if (strcmp(head->data.MaSo, maSo) == 0) {
        Node* temp = head;
        head = head->next;
        delete temp;
        cout << "Xóa học sinh thành công!" << endl;
        return;
    }
    
    // Tìm nút chứa mã số cần xóa
    Node* temp = head;
    while (temp->next != NULL && strcmp(temp->next->data.MaSo, maSo) != 0) {
        temp = temp->next;
    }
    
    if (temp->next == NULL) {
        cout << "Không tìm thấy học sinh có mã số: " << maSo << endl;
        return;
    }
    
    Node* nodeToDelete = temp->next;
    temp->next = nodeToDelete->next;
    delete nodeToDelete;
    cout << "Xóa học sinh thành công!" << endl;
}

// Cập nhật thông tin học sinh
void updateStudent(Node* head, const char* maSo) {
    Node* node = searchByMaSo(head, maSo);
    if (node == NULL) {
        cout << "Không tìm thấy học sinh có mã số: " << maSo << endl;
        return;
    }
    
    cout << "Thông tin hiện tại:" << endl;
    displayHeader();
    displayStudent(node->data);
    
    cout << "\nNhập thông tin mới:" << endl;
    cout << "Họ tên: ";
    cin.ignore();
    cin.getline(node->data.HoTen, 50);
    cout << "Năm sinh: ";
    cin >> node->data.NamSinh;
    cout << "Khối: ";
    cin >> node->data.Khoi;
    cout << "Lớp: ";
    cin >> node->data.Lop;
    cout << "Điểm trung bình: ";
    cin >> node->data.DiemTrungBinh;
    
    cout << "Cập nhật thông tin thành công!" << endl;
}

// Sắp xếp theo mã số học sinh
void sortByMaSo(Node*& head) {
    if (head == NULL || head->next == NULL) return;
    
    bool swapped;
    do {
        swapped = false;
        Node* current = head;
        
        while (current->next != NULL) {
            if (strcmp(current->data.MaSo, current->next->data.MaSo) > 0) {
                // Hoán đổi dữ liệu
                HocSinh temp = current->data;
                current->data = current->next->data;
                current->next->data = temp;
                swapped = true;
            }
            current = current->next;
        }
    } while (swapped);
}

// Sắp xếp theo điểm trung bình (giảm dần)
void sortByDiemTB(Node*& head) {
    if (head == NULL || head->next == NULL) return;
    
    bool swapped;
    do {
        swapped = false;
        Node* current = head;
        
        while (current->next != NULL) {
            if (current->data.DiemTrungBinh < current->next->data.DiemTrungBinh) {
                // Hoán đổi dữ liệu
                HocSinh temp = current->data;
                current->data = current->next->data;
                current->next->data = temp;
                swapped = true;
            }
            current = current->next;
        }
    } while (swapped);
}

// Thống kê số lượng học sinh theo khối
void thongKeTheoKhoi(Node* head) {
    int count10 = 0, count11 = 0, count12 = 0;
    Node* temp = head;
    
    while (temp != NULL) {
        switch (temp->data.Khoi) {
            case 10: count10++; break;
            case 11: count11++; break;
            case 12: count12++; break;
        }
        temp = temp->next;
    }
    
    cout << "\nThống kê số lượng học sinh theo khối:" << endl;
    cout << "Khối 10: " << count10 << " học sinh" << endl;
    cout << "Khối 11: " << count11 << " học sinh" << endl;
    cout << "Khối 12: " << count12 << " học sinh" << endl;
}

// Thống kê học sinh có điểm cao nhất/thấp nhất
void thongKeDiemCaoThapNhat(Node* head) {
    if (head == NULL) {
        cout << "Danh sách rỗng!" << endl;
        return;
    }
    
    Node* temp = head;
    float maxDiem = temp->data.DiemTrungBinh;
    float minDiem = temp->data.DiemTrungBinh;
    Node* maxNode = temp;
    Node* minNode = temp;
    
    while (temp != NULL) {
        if (temp->data.DiemTrungBinh > maxDiem) {
            maxDiem = temp->data.DiemTrungBinh;
            maxNode = temp;
        }
        if (temp->data.DiemTrungBinh < minDiem) {
            minDiem = temp->data.DiemTrungBinh;
            minNode = temp;
        }
        temp = temp->next;
    }
    
    cout << "\nHọc sinh có điểm TB cao nhất:" << endl;
    displayHeader();
    displayStudent(maxNode->data);
    
    cout << "\nHọc sinh có điểm TB thấp nhất:" << endl;
    displayHeader();
    displayStudent(minNode->data);
}

// Thống kê tỉ lệ đạt/không đạt
void thongKeTiLeDat(Node* head, float diemChuan = 5.0) {
    int tongSo = 0, soDat = 0;
    Node* temp = head;
    
    while (temp != NULL) {
        tongSo++;
        if (temp->data.DiemTrungBinh >= diemChuan) {
            soDat++;
        }
        temp = temp->next;
    }
    
    if (tongSo > 0) {
        float tiLeDat = (float)soDat / tongSo * 100;
        float tiLeKhongDat = 100 - tiLeDat;
        
        cout << "\nThống kê tỉ lệ đạt/không đạt (điểm chuẩn: " << diemChuan << "):" << endl;
        cout << "Số học sinh đạt: " << soDat << "/" << tongSo 
             << " (" << fixed << setprecision(1) << tiLeDat << "%)" << endl;
        cout << "Số học sinh không đạt: " << (tongSo - soDat) << "/" << tongSo 
             << " (" << tiLeKhongDat << "%)" << endl;
    }
}

// Trích lọc học sinh theo khối
void filterByKhoi(Node* head, int khoi) {
    Node* temp = head;
    cout << "\nDanh sách học sinh khối " << khoi << ":" << endl;
    displayHeader();

    int count = 0;
    while (temp != NULL) {
        if (temp->data.Khoi == khoi) {
            displayStudent(temp->data);
            count++;
        }
        temp = temp->next;
    }

    if (count == 0) {
        cout << "Không có học sinh nào trong khối " << khoi << endl;
    } else {
        cout << string(75, '=') << endl;
        cout << "Tổng số học sinh khối " << khoi << ": " << count << endl;
    }
}

// Trích lọc học sinh theo lớp
void filterByLop(Node* head, const char* lop) {
    Node* temp = head;
    cout << "\nDanh sách học sinh lớp " << lop << ":" << endl;
    displayHeader();

    int count = 0;
    while (temp != NULL) {
        if (strcmp(temp->data.Lop, lop) == 0) {
            displayStudent(temp->data);
            count++;
        }
        temp = temp->next;
    }

    if (count == 0) {
        cout << "Không có học sinh nào trong lớp " << lop << endl;
    } else {
        cout << string(75, '=') << endl;
        cout << "Tổng số học sinh lớp " << lop << ": " << count << endl;
    }
}

// Trích lọc học sinh theo điểm trung bình
void filterByDiemTB(Node* head, float nguong, bool isHigher) {
    Node* temp = head;
    cout << "\nDanh sách học sinh có điểm TB ";
    cout << (isHigher ? ">= " : "< ") << nguong << ":" << endl;
    displayHeader();

    int count = 0;
    while (temp != NULL) {
        if ((isHigher && temp->data.DiemTrungBinh >= nguong) ||
            (!isHigher && temp->data.DiemTrungBinh < nguong)) {
            displayStudent(temp->data);
            count++;
        }
        temp = temp->next;
    }

    if (count == 0) {
        cout << "Không có học sinh nào thỏa mãn điều kiện" << endl;
    } else {
        cout << string(75, '=') << endl;
        cout << "Tổng số học sinh thỏa mãn: " << count << endl;
    }
}

// Giải phóng bộ nhớ
void deleteList(Node*& head) {
    while (head != NULL) {
        Node* temp = head;
        head = head->next;
        delete temp;
    }
}

// Hàm chính
int main() {
    Node* head = NULL;
    int choice;

    cout << "CHƯƠNG TRÌNH QUẢN LÝ DANH SÁCH HỌC SINH" << endl;
    cout << "Sử dụng cấu trúc dữ liệu Danh Sách Liên Kết Đơn" << endl;

    do {
        displayMenu();
        cin >> choice;

        switch (choice) {
            case 1: {
                cout << "\n--- THÊM HỌC SINH MỚI ---" << endl;
                HocSinh hs = nhapThongTinHocSinh();

                // Kiểm tra trùng mã số
                if (searchByMaSo(head, hs.MaSo) != NULL) {
                    cout << "Mã số học sinh đã tồn tại!" << endl;
                } else {
                    insertAtEnd(head, hs);
                    cout << "Thêm học sinh thành công!" << endl;
                }
                break;
            }
            case 2: {
                cout << "\n--- DANH SÁCH HỌC SINH ---" << endl;
                displayAllStudents(head);
                break;
            }
            case 3: {
                cout << "\n--- TÌM KIẾM HỌC SINH ---" << endl;
                cout << "Nhập mã số cần tìm: ";
                char maSo[10];
                cin >> maSo;
                Node* found = searchByMaSo(head, maSo);
                if (found != NULL) {
                    cout << "Tìm thấy học sinh:" << endl;
                    displayHeader();
                    displayStudent(found->data);
                } else {
                    cout << "Không tìm thấy học sinh có mã số: " << maSo << endl;
                }
                break;
            }
            case 4: {
                cout << "\n--- XÓA HỌC SINH ---" << endl;
                cout << "Nhập mã số cần xóa: ";
                char maSo[10];
                cin >> maSo;
                deleteByMaSo(head, maSo);
                break;
            }
            case 5: {
                cout << "\n--- CẬP NHẬT THÔNG TIN HỌC SINH ---" << endl;
                cout << "Nhập mã số cần cập nhật: ";
                char maSo[10];
                cin >> maSo;
                updateStudent(head, maSo);
                break;
            }
            case 6: {
                cout << "\n--- SẮP XẾP DANH SÁCH ---" << endl;
                cout << "1. Sắp xếp theo mã số" << endl;
                cout << "2. Sắp xếp theo điểm TB (giảm dần)" << endl;
                cout << "Chọn: ";
                int sortChoice;
                cin >> sortChoice;
                if (sortChoice == 1) {
                    sortByMaSo(head);
                    cout << "Sắp xếp theo mã số hoàn tất!" << endl;
                } else if (sortChoice == 2) {
                    sortByDiemTB(head);
                    cout << "Sắp xếp theo điểm TB hoàn tất!" << endl;
                } else {
                    cout << "Lựa chọn không hợp lệ!" << endl;
                }
                break;
            }
            case 7: {
                cout << "\n--- THỐNG KÊ DỮ LIỆU ---" << endl;
                thongKeTheoKhoi(head);
                thongKeDiemCaoThapNhat(head);
                thongKeTiLeDat(head);
                break;
            }
            case 8: {
                cout << "\n--- TRÍCH LỌC DỮ LIỆU ---" << endl;
                cout << "1. Lọc theo khối" << endl;
                cout << "2. Lọc theo lớp" << endl;
                cout << "3. Lọc theo điểm TB" << endl;
                cout << "Chọn: ";
                int filterChoice;
                cin >> filterChoice;

                if (filterChoice == 1) {
                    cout << "Nhập khối (10/11/12): ";
                    int khoi;
                    cin >> khoi;
                    filterByKhoi(head, khoi);
                } else if (filterChoice == 2) {
                    cout << "Nhập lớp: ";
                    char lop[10];
                    cin >> lop;
                    filterByLop(head, lop);
                } else if (filterChoice == 3) {
                    cout << "Nhập điểm ngưỡng: ";
                    float nguong;
                    cin >> nguong;
                    cout << "1. >= ngưỡng, 2. < ngưỡng: ";
                    int option;
                    cin >> option;
                    filterByDiemTB(head, nguong, option == 1);
                } else {
                    cout << "Lựa chọn không hợp lệ!" << endl;
                }
                break;
            }
            case 0:
                cout << "\nCảm ơn bạn đã sử dụng chương trình!" << endl;
                cout << "Đang giải phóng bộ nhớ..." << endl;
                deleteList(head);
                cout << "Thoát chương trình thành công!" << endl;
                break;
            default:
                cout << "Lựa chọn không hợp lệ! Vui lòng chọn lại." << endl;
        }

        if (choice != 0) {
            cout << "\nNhấn Enter để tiếp tục...";
            cin.ignore();
            cin.get();
        }

    } while (choice != 0);

    return 0;
}
