# Requirements for Python GUI Demo
# Ứng Dụng <PERSON>n <PERSON>

# Core Python libraries (built-in, no installation needed)
# tkinter - GUI framework (included with Python)
# json - JSON data handling (built-in)
# datetime - Date and time utilities (built-in)

# Optional enhancements (can be installed if needed)
# pillow>=8.0.0  # For advanced image handling in GUI
# matplotlib>=3.0.0  # For creating charts and graphs
# pandas>=1.0.0  # For advanced data manipulation

# Note: This application uses only built-in Python libraries
# No additional installations required for basic functionality
