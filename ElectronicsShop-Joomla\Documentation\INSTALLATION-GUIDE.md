# Hướng Dẫn Cài Đặt Website Bán Thiết Bị Điện Tử Cũ

## Tổng quan
Hướng dẫn này sẽ giúp bạn cài đặt website bán thiết bị điện tử cũ sử dụng Joomla và VirtueMart từ đầu.

## Yêu cầu hệ thống
- **Hệ điều hành**: Windows 10/11
- **RAM**: <PERSON><PERSON><PERSON> thiểu 4GB (khuyến nghị 8GB+)
- **Dung lượng**: Tối thiểu 2GB trống
- **Kết nối internet**: Để tải các package cần thiết

## Bước 1: Cài đặt XAMPP

### Tự động (Khuyến nghị)
```bash
# Chạy với quyền Administrator
install-xampp.bat
```

### Thủ công
1. Tải XAMPP từ https://www.apachefriends.org/
2. Chọn phiên bản PHP 8.1 hoặc 8.2
3. <PERSON>ài đặt với các component:
   - Apache
   - MySQL
   - PHP
   - phpMyAdmin
4. Khởi động XAMPP Control Panel
5. Start Apache và MySQL services

## Bước 2: Tải và cài đặt Joomla

### Tự động (Khuyến nghị)
```powershell
# Chạy PowerShell với quyền Administrator
.\download-joomla.ps1
```

### Thủ công
1. Tải Joomla 4.x từ https://www.joomla.org/download.html
2. Giải nén vào `C:\xampp\htdocs\electronics-shop`
3. Mở trình duyệt và truy cập `http://localhost/electronics-shop`

## Bước 3: Cấu hình Database

### Tạo Database
1. Truy cập http://localhost/phpmyadmin
2. Đăng nhập (username: root, password: để trống)
3. Tạo database mới:
   - Tên database: `electronics_shop`
   - Collation: `utf8mb4_unicode_ci`

### Tạo User Database (Khuyến nghị)
```sql
CREATE USER 'electronics_user'@'localhost' IDENTIFIED BY 'strong_password_123';
GRANT ALL PRIVILEGES ON electronics_shop.* TO 'electronics_user'@'localhost';
FLUSH PRIVILEGES;
```

## Bước 4: Cài đặt Joomla

### Thông tin cấu hình
1. **Cấu hình Site**:
   - Site Name: `Electronics Shop - Thiết Bị Điện Tử Cũ`
   - Description: `Website bán thiết bị điện tử cũ uy tín`
   - Admin Email: `<EMAIL>`
   - Admin Username: `admin`
   - Admin Password: `[Mật khẩu mạnh]`

2. **Cấu hình Database**:
   - Database Type: `MySQLi`
   - Host Name: `localhost`
   - Username: `electronics_user` (hoặc `root`)
   - Password: `strong_password_123` (hoặc để trống nếu dùng root)
   - Database Name: `electronics_shop`
   - Table Prefix: `jos_`

3. **Cấu hình Overview**:
   - Install Sample Data: `Blog Sample Data` (để có nội dung mẫu)
   - Email Configuration: `Yes`

## Bước 5: Cài đặt VirtueMart

### Tải VirtueMart
1. Truy cập https://virtuemart.net/downloads
2. Tải VirtueMart 4.x (tương thích Joomla 4)
3. Tải các plugin cần thiết:
   - VM Payment - Standard Payment Methods
   - VM Shipment - Standard Shipment Methods

### Cài đặt Extension
1. Đăng nhập Joomla Admin Panel
2. Vào `System > Install > Extensions`
3. Upload và cài đặt VirtueMart package
4. Cài đặt các plugin payment và shipment

### Cấu hình cơ bản VirtueMart
1. Vào `Components > VirtueMart`
2. Chạy VirtueMart Installation Tool
3. Cấu hình:
   - Shop Name: `Electronics Shop`
   - Currency: `VND` (Vietnamese Dong)
   - Country: `Vietnam`
   - State: `Ho Chi Minh City`

## Bước 6: Cấu hình ngôn ngữ tiếng Việt

### Cài đặt Language Pack
1. Vào `System > Install > Languages`
2. Tìm và cài đặt `Vietnamese (vi-VN)`
3. Vào `System > Manage > Languages`
4. Set Vietnamese làm default language

### Cấu hình VirtueMart tiếng Việt
1. Tải VirtueMart Vietnamese Language Pack
2. Cài đặt qua Extension Manager
3. Cấu hình trong VirtueMart Configuration

## Bước 7: Cấu hình bảo mật cơ bản

### Thay đổi mật khẩu mặc định
```sql
-- Thay đổi mật khẩu admin trong database nếu cần
UPDATE jos_users SET password = MD5('new_password') WHERE username = 'admin';
```

### Cấu hình .htaccess
```apache
# Thêm vào .htaccess trong thư mục gốc
RewriteEngine On
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^(.*)$ http://%1/$1 [R=301,L]

# Bảo vệ các file quan trọng
<Files "configuration.php">
    Order Allow,Deny
    Deny from all
</Files>
```

## Bước 8: Kiểm tra cài đặt

### Checklist
- [ ] Joomla frontend hiển thị bình thường
- [ ] Joomla admin panel truy cập được
- [ ] VirtueMart component hoạt động
- [ ] Database kết nối thành công
- [ ] Ngôn ngữ tiếng Việt hiển thị đúng
- [ ] Email configuration hoạt động

### Test URLs
- Frontend: http://localhost/electronics-shop
- Admin Panel: http://localhost/electronics-shop/administrator
- VirtueMart: http://localhost/electronics-shop/index.php?option=com_virtuemart

## Xử lý lỗi thường gặp

### Lỗi Database Connection
```
Error: Could not connect to database
```
**Giải pháp**: Kiểm tra MySQL service đã start, thông tin database đúng

### Lỗi Permission
```
Error: Cannot write to configuration.php
```
**Giải pháp**: Set quyền write cho thư mục Joomla

### Lỗi Memory Limit
```
Fatal error: Allowed memory size exhausted
```
**Giải pháp**: Tăng memory_limit trong php.ini

## Liên hệ hỗ trợ
Nếu gặp vấn đề trong quá trình cài đặt, vui lòng tạo issue trong repository này.
