using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using ShortestPathProject.Models;

namespace ShortestPathProject.Services
{
    public class BellmanFordService
    {
        public AlgorithmResult FindShortestPath(Graph graph, Vertex source, Vertex target)
        {
            var result = new AlgorithmResult("Bellman-Ford");
            var stopwatch = Stopwatch.StartNew();
            
            var distances = new Dictionary<Vertex, double>();
            var predecessors = new Dictionary<Vertex, Vertex>();
            
            // Khởi tạo
            foreach (var vertex in graph.Vertices)
            {
                distances[vertex] = double.PositiveInfinity;
            }
            distances[source] = 0;
            
            result.Steps.Add($"Khởi tạo: dist[{source.Name}] = 0, tất cả đỉnh khác = ∞");
            
            // Thực hiện V-1 lần lặp
            int vertexCount = graph.VertexCount;
            
            for (int i = 0; i < vertexCount - 1; i++)
            {
                result.Steps.Add($"--- Lần lặp {i + 1}/{vertexCount - 1} ---");
                bool hasUpdate = false;
                
                foreach (var edge in graph.Edges)
                {
                    var u = edge.From;
                    var v = edge.To;
                    double weight = edge.Weight;
                    
                    if (!double.IsPositiveInfinity(distances[u]))
                    {
                        double newDistance = distances[u] + weight;
                        
                        if (newDistance < distances[v])
                        {
                            distances[v] = newDistance;
                            predecessors[v] = u;
                            hasUpdate = true;
                            
                            result.Steps.Add(
                                $"Cập nhật: dist[{v.Name}] = {newDistance:F2} " +
                                $"(từ {u.Name} qua cạnh trọng số {weight})"
                            );
                        }
                    }
                }
                
                // Nếu không có cập nhật nào, có thể dừng sớm
                if (!hasUpdate)
                {
                    result.Steps.Add("Không có cập nhật nào trong lần lặp này, dừng sớm");
                    break;
                }
            }
            
            // Kiểm tra chu trình âm
            result.Steps.Add("--- Kiểm tra chu trình âm ---");
            foreach (var edge in graph.Edges)
            {
                var u = edge.From;
                var v = edge.To;
                double weight = edge.Weight;
                
                if (!double.IsPositiveInfinity(distances[u]))
                {
                    if (distances[u] + weight < distances[v])
                    {
                        result.HasNegativeCycle = true;
                        result.Steps.Add($"Phát hiện chu trình âm qua cạnh {u.Name} -> {v.Name}!");
                        break;
                    }
                }
            }
            
            if (!result.HasNegativeCycle)
            {
                result.Steps.Add("Không phát hiện chu trình âm");
            }
            
            stopwatch.Stop();
            
            result.Distances = distances;
            result.Predecessors = predecessors;
            result.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;
            
            if (!result.HasNegativeCycle)
            {
                result.BuildPath(source, target);
                
                if (result.Path.Count > 0)
                {
                    result.Steps.Add($"Đường đi ngắn nhất: {string.Join(" -> ", result.Path.Select(v => v.Name))}");
                    result.Steps.Add($"Độ dài: {result.PathLength:F2}");
                }
                else
                {
                    result.Steps.Add($"Không có đường đi từ {source.Name} đến {target.Name}");
                }
            }
            else
            {
                result.Steps.Add("Không thể tính đường đi ngắn nhất do có chu trình âm");
            }
            
            result.Steps.Add($"Thời gian thực thi: {result.ExecutionTimeMs} ms");
            
            return result;
        }
        
        // Phát hiện và trả về chu trình âm
        public List<Vertex> DetectNegativeCycle(Graph graph, Vertex source)
        {
            var distances = new Dictionary<Vertex, double>();
            var predecessors = new Dictionary<Vertex, Vertex>();
            
            // Khởi tạo
            foreach (var vertex in graph.Vertices)
            {
                distances[vertex] = double.PositiveInfinity;
            }
            distances[source] = 0;
            
            // V-1 lần lặp
            for (int i = 0; i < graph.VertexCount - 1; i++)
            {
                foreach (var edge in graph.Edges)
                {
                    var u = edge.From;
                    var v = edge.To;
                    
                    if (!double.IsPositiveInfinity(distances[u]) &&
                        distances[u] + edge.Weight < distances[v])
                    {
                        distances[v] = distances[u] + edge.Weight;
                        predecessors[v] = u;
                    }
                }
            }
            
            // Tìm đỉnh trong chu trình âm
            Vertex cycleVertex = null;
            foreach (var edge in graph.Edges)
            {
                var u = edge.From;
                var v = edge.To;
                
                if (!double.IsPositiveInfinity(distances[u]) &&
                    distances[u] + edge.Weight < distances[v])
                {
                    cycleVertex = v;
                    break;
                }
            }
            
            if (cycleVertex == null)
                return new List<Vertex>(); // Không có chu trình âm
            
            // Truy vết chu trình
            var cycle = new List<Vertex>();
            var visited = new HashSet<Vertex>();
            var current = cycleVertex;
            
            // Di chuyển V lần để đảm bảo vào chu trình
            for (int i = 0; i < graph.VertexCount; i++)
            {
                if (predecessors.ContainsKey(current))
                    current = predecessors[current];
                else
                    break;
            }
            
            // Xây dựng chu trình
            var start = current;
            do
            {
                cycle.Add(current);
                if (predecessors.ContainsKey(current))
                    current = predecessors[current];
                else
                    break;
            } while (!current.Equals(start) && cycle.Count < graph.VertexCount);
            
            if (current.Equals(start))
                cycle.Add(start); // Đóng chu trình
            
            return cycle;
        }

        // Tìm tất cả đường đi ngắn nhất từ một nguồn
        public AlgorithmResult FindAllShortestPaths(Graph graph, Vertex source)
        {
            var result = new AlgorithmResult("Bellman-Ford - All Paths");
            var stopwatch = Stopwatch.StartNew();
            
            var distances = new Dictionary<Vertex, double>();
            var predecessors = new Dictionary<Vertex, Vertex>();
            
            // Khởi tạo
            foreach (var vertex in graph.Vertices)
            {
                distances[vertex] = double.PositiveInfinity;
            }
            distances[source] = 0;
            
            result.Steps.Add($"Tìm đường đi ngắn nhất từ {source.Name} đến tất cả đỉnh");
            
            // V-1 lần lặp
            for (int i = 0; i < graph.VertexCount - 1; i++)
            {
                bool hasUpdate = false;
                
                foreach (var edge in graph.Edges)
                {
                    var u = edge.From;
                    var v = edge.To;
                    
                    if (!double.IsPositiveInfinity(distances[u]) &&
                        distances[u] + edge.Weight < distances[v])
                    {
                        distances[v] = distances[u] + edge.Weight;
                        predecessors[v] = u;
                        hasUpdate = true;
                    }
                }
                
                if (!hasUpdate) break;
            }
            
            // Kiểm tra chu trình âm
            foreach (var edge in graph.Edges)
            {
                var u = edge.From;
                var v = edge.To;
                
                if (!double.IsPositiveInfinity(distances[u]) &&
                    distances[u] + edge.Weight < distances[v])
                {
                    result.HasNegativeCycle = true;
                    break;
                }
            }
            
            stopwatch.Stop();
            
            result.Distances = distances;
            result.Predecessors = predecessors;
            result.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;
            
            // Thêm thông tin về kết quả
            if (!result.HasNegativeCycle)
            {
                foreach (var vertex in graph.Vertices)
                {
                    if (!vertex.Equals(source))
                    {
                        if (double.IsPositiveInfinity(distances[vertex]))
                        {
                            result.Steps.Add($"Không có đường đi đến {vertex.Name}");
                        }
                        else
                        {
                            result.Steps.Add($"Khoảng cách đến {vertex.Name}: {distances[vertex]:F2}");
                        }
                    }
                }
            }
            else
            {
                result.Steps.Add("Phát hiện chu trình âm - không thể tính đường đi ngắn nhất");
            }
            
            result.Steps.Add($"Thời gian thực thi: {result.ExecutionTimeMs} ms");
            
            return result;
        }

        // Kiểm tra xem đồ thị có chu trình âm không
        public bool HasNegativeCycle(Graph graph)
        {
            if (graph.VertexCount == 0) return false;
            
            var source = graph.Vertices.First();
            var distances = new Dictionary<Vertex, double>();
            
            // Khởi tạo
            foreach (var vertex in graph.Vertices)
            {
                distances[vertex] = double.PositiveInfinity;
            }
            distances[source] = 0;
            
            // V-1 lần lặp
            for (int i = 0; i < graph.VertexCount - 1; i++)
            {
                foreach (var edge in graph.Edges)
                {
                    var u = edge.From;
                    var v = edge.To;
                    
                    if (!double.IsPositiveInfinity(distances[u]) &&
                        distances[u] + edge.Weight < distances[v])
                    {
                        distances[v] = distances[u] + edge.Weight;
                    }
                }
            }
            
            // Kiểm tra chu trình âm
            foreach (var edge in graph.Edges)
            {
                var u = edge.From;
                var v = edge.To;
                
                if (!double.IsPositiveInfinity(distances[u]) &&
                    distances[u] + edge.Weight < distances[v])
                {
                    return true;
                }
            }
            
            return false;
        }

        // Lấy thống kê về quá trình thực thi
        public Dictionary<string, object> GetExecutionStats(AlgorithmResult result, Graph graph)
        {
            return new Dictionary<string, object>
            {
                ["algorithm"] = result.AlgorithmName,
                ["executionTime"] = result.ExecutionTimeMs,
                ["vertexCount"] = graph.VertexCount,
                ["edgeCount"] = graph.EdgeCount,
                ["pathFound"] = result.Path.Count > 0,
                ["pathLength"] = result.PathLength,
                ["hasNegativeCycle"] = result.HasNegativeCycle,
                ["stepsCount"] = result.Steps.Count,
                ["memoryComplexity"] = $"O({graph.VertexCount})",
                ["timeComplexity"] = $"O({graph.VertexCount} × {graph.EdgeCount})",
                ["canHandleNegativeWeights"] = true,
                ["canDetectNegativeCycles"] = true
            };
        }
    }
}
