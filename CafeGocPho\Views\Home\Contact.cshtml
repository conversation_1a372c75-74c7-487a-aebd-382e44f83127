@{
    ViewData["Title"] = "<PERSON>ê<PERSON> hệ";
}

<div class="container py-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="text-center mb-5">
                <h1 class="display-4 fw-bold"><PERSON><PERSON><PERSON> hệ với chúng tôi</h1>
                <p class="lead text-muted">Hãy liên hệ để đặt hàng hoặc góp ý cho chúng tôi</p>
            </div>

            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h4 class="card-title mb-4">
                                <i class="fas fa-info-circle text-warning me-2"></i>Thông tin liên hệ
                            </h4>
                            
                            <div class="mb-3">
                                <h6><i class="fas fa-map-marker-alt text-warning me-2"></i>Đ<PERSON><PERSON> chỉ:</h6>
                                <p class="text-muted">123 Đường ABC, Quận 1, TP.HCM</p>
                            </div>
                            
                            <div class="mb-3">
                                <h6><i class="fas fa-phone text-warning me-2"></i>Số điện thoại:</h6>
                                <p class="text-muted">
                                    <a href="tel:**********" class="text-decoration-none">0123 456 789</a>
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <h6><i class="fas fa-envelope text-warning me-2"></i>Email:</h6>
                                <p class="text-muted">
                                    <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <h6><i class="fas fa-clock text-warning me-2"></i>Giờ hoạt động:</h6>
                                <p class="text-muted">
                                    Thứ 2 - Chủ nhật: 6:00 - 22:00<br>
                                    Giao hàng: 7:00 - 21:00
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h4 class="card-title mb-4">
                                <i class="fas fa-shopping-cart text-warning me-2"></i>Đặt hàng nhanh
                            </h4>
                            
                            <div class="mb-4">
                                <h6>Cách đặt hàng:</h6>
                                <ol class="text-muted">
                                    <li>Đăng ký tài khoản hoặc đăng nhập</li>
                                    <li>Chọn sản phẩm yêu thích</li>
                                    <li>Thêm vào giỏ hàng</li>
                                    <li>Điền thông tin giao hàng</li>
                                    <li>Xác nhận đơn hàng</li>
                                </ol>
                            </div>
                            
                            <div class="mb-4">
                                <h6>Khu vực giao hàng:</h6>
                                <ul class="text-muted">
                                    <li>Quận 1, 3, 5, 10 (30 phút)</li>
                                    <li>Các quận khác trong TP.HCM (45-60 phút)</li>
                                </ul>
                            </div>
                            
                            <div class="d-grid gap-2">
                                @if (User.Identity?.IsAuthenticated == true)
                                {
                                    <a href="@Url.Action("Menu", "Product")" class="btn btn-warning btn-lg">
                                        <i class="fas fa-coffee me-2"></i>Đặt hàng ngay
                                    </a>
                                }
                                else
                                {
                                    <a href="@Url.Action("Register", "Account")" class="btn btn-warning btn-lg">
                                        <i class="fas fa-user-plus me-2"></i>Đăng ký để đặt hàng
                                    </a>
                                }
                                <a href="tel:**********" class="btn btn-outline-warning">
                                    <i class="fas fa-phone me-2"></i>Gọi điện đặt hàng
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
