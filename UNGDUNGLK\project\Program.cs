using DoublyLinkedListApp.Models;
using DoublyLinkedListApp.Services;
using DoublyLinkedListApp.Utils;
using System;
using System.Collections.Generic;

namespace DoublyLinkedListApp
{
    class Program
    {
        private static HangHoaService hangHoaService = new HangHoaService();

        static void Main(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.InputEncoding = System.Text.Encoding.UTF8;

            while (true)
            {
                try
                {
                    MenuHelper.HienThiMenuChinh();
                    int luaChon = MenuHelper.DocSoNguyen("", 0, 9);

                    switch (luaChon)
                    {
                        case 1:
                            ThemHangHoa();
                            break;
                        case 2:
                            XoaHangHoa();
                            break;
                        case 3:
                            MenuTim<PERSON>iem();
                            break;
                        case 4:
                            HienThiDanhSach();
                            break;
                        case 5:
                            MenuSapXep();
                            break;
                        case 6:
                            MenuLoc();
                            break;
                        case 7:
                            MenuThong<PERSON>e();
                            break;
                        case 8:
                            CapNhatThongTin();
                            break;
                        case 9:
                            TaoDu<PERSON>ieuMau();
                            break;
                        case 0:
                            Console.WriteLine("Cảm ơn bạn đã sử dụng chương trình!");
                            return;
                        default:
                            Console.WriteLine("Lựa chọn không hợp lệ!");
                            break;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Lỗi: {ex.Message}");
                    MenuHelper.TamDung();
                }
            }
        }

        static void ThemHangHoa()
        {
            var hangHoa = DataHelper.NhapThongTinHangHoa();
            hangHoaService.ThemHangHoa(hangHoa);
            MenuHelper.TamDung();
        }

        static void XoaHangHoa()
        {
            MenuHelper.HienThiTieuDe("XÓA HÀNG HÓA");
            
            if (hangHoaService.DemSoLuong() == 0)
            {
                Console.WriteLine("Danh sách trống!");
                MenuHelper.TamDung();
                return;
            }

            string maSo = MenuHelper.DocChuoiKhongRong("Nhập mã số cần xóa: ");
            
            var hangHoa = hangHoaService.TimKiemTheoMaSo(maSo);
            if (hangHoa != null)
            {
                DataHelper.HienThiChiTiet(hangHoa);
                Console.Write("Bạn có chắc chắn muốn xóa? (y/n): ");
                string xacNhan = Console.ReadLine()?.ToLower() ?? "";
                
                if (xacNhan == "y" || xacNhan == "yes")
                {
                    hangHoaService.XoaHangHoa(maSo);
                }
                else
                {
                    Console.WriteLine("Đã hủy thao tác xóa!");
                }
            }
            else
            {
                Console.WriteLine($"Không tìm thấy hàng hóa có mã số: {maSo}");
            }
            
            MenuHelper.TamDung();
        }

        static void MenuTimKiem()
        {
            while (true)
            {
                MenuHelper.HienThiMenuTimKiem();
                int luaChon = MenuHelper.DocSoNguyen("", 0, 2);

                switch (luaChon)
                {
                    case 1:
                        TimKiemTheoMaSo();
                        break;
                    case 2:
                        TimKiemTheoHoTen();
                        break;
                    case 0:
                        return;
                }
            }
        }

        static void TimKiemTheoMaSo()
        {
            MenuHelper.HienThiTieuDe("TÌM KIẾM THEO MÃ SỐ");
            string maSo = MenuHelper.DocChuoiKhongRong("Nhập mã số cần tìm: ");
            
            var hangHoa = hangHoaService.TimKiemTheoMaSo(maSo);
            if (hangHoa != null)
            {
                DataHelper.HienThiChiTiet(hangHoa);
            }
            else
            {
                Console.WriteLine($"Không tìm thấy hàng hóa có mã số: {maSo}");
            }
            
            MenuHelper.TamDung();
        }

        static void TimKiemTheoHoTen()
        {
            MenuHelper.HienThiTieuDe("TÌM KIẾM THEO TÊN HÀNG");
            string tenHang = MenuHelper.DocChuoiKhongRong("Nhập tên hàng cần tìm: ");

            var ketQua = hangHoaService.TimKiemTheoTenHang(tenHang);
            DataHelper.HienThiKetQuaTimKiem(ketQua, tenHang);

            MenuHelper.TamDung();
        }

        static void HienThiDanhSach()
        {
            var danhSach = hangHoaService.LayTatCaHangHoa();
            DataHelper.HienThiDanhSach(danhSach);
            MenuHelper.TamDung();
        }

        static void MenuSapXep()
        {
            while (true)
            {
                MenuHelper.HienThiMenuSapXep();
                int luaChon = MenuHelper.DocSoNguyen("", 0, 6);

                List<HangHoa> ketQua = new List<HangHoa>();
                string tieuDe = "";

                switch (luaChon)
                {
                    case 1:
                        ketQua = hangHoaService.SapXepTheoDonGia(true);
                        tieuDe = "SẮP XẾP THEO ĐƠN GIÁ (TĂNG DẦN)";
                        break;
                    case 2:
                        ketQua = hangHoaService.SapXepTheoDonGia(false);
                        tieuDe = "SẮP XẾP THEO ĐƠN GIÁ (GIẢM DẦN)";
                        break;
                    case 3:
                        ketQua = hangHoaService.SapXepTheoTenHang(true);
                        tieuDe = "SẮP XẾP THEO TÊN HÀNG (A-Z)";
                        break;
                    case 4:
                        ketQua = hangHoaService.SapXepTheoTenHang(false);
                        tieuDe = "SẮP XẾP THEO TÊN HÀNG (Z-A)";
                        break;
                    case 5:
                        ketQua = hangHoaService.SapXepTheoSoLuong(true);
                        tieuDe = "SẮP XẾP THEO SỐ LƯỢNG (TĂNG DẦN)";
                        break;
                    case 6:
                        ketQua = hangHoaService.SapXepTheoSoLuong(false);
                        tieuDe = "SẮP XẾP THEO SỐ LƯỢNG (GIẢM DẦN)";
                        break;
                    case 0:
                        return;
                }

                if (ketQua.Count > 0)
                {
                    DataHelper.HienThiDanhSach(ketQua, tieuDe);
                    MenuHelper.TamDung();
                }
            }
        }

        static void MenuLoc()
        {
            while (true)
            {
                MenuHelper.HienThiMenuLoc();
                int luaChon = MenuHelper.DocSoNguyen("", 0, 4);

                List<HangHoa> ketQua = new List<HangHoa>();
                string tieuDe = "";

                switch (luaChon)
                {
                    case 1:
                        LocTheoDonViTinh(out ketQua, out tieuDe);
                        break;
                    case 2:
                        LocTheoKhoangGia(out ketQua, out tieuDe);
                        break;
                    case 3:
                        LocTheoTrangThai(out ketQua, out tieuDe);
                        break;
                    case 4:
                        LocTheoPhanLoaiGia(out ketQua, out tieuDe);
                        break;
                    case 0:
                        return;
                }

                if (ketQua.Count > 0)
                {
                    DataHelper.HienThiDanhSach(ketQua, tieuDe);
                }
                else
                {
                    Console.WriteLine("Không có dữ liệu phù hợp với điều kiện lọc!");
                }
                MenuHelper.TamDung();
            }
        }

        static void LocTheoDonViTinh(out List<HangHoa> ketQua, out string tieuDe)
        {
            MenuHelper.HienThiTieuDe("LỌC THEO ĐƠN VỊ TÍNH");
            string donViTinh = MenuHelper.DocChuoiKhongRong("Nhập đơn vị tính: ");
            ketQua = hangHoaService.LocTheoDonViTinh(donViTinh);
            tieuDe = $"LỌC THEO ĐƠN VỊ TÍNH: {donViTinh}";
        }

        static void LocTheoKhoangGia(out List<HangHoa> ketQua, out string tieuDe)
        {
            MenuHelper.HienThiTieuDe("LỌC THEO KHOẢNG GIÁ");
            decimal giaMin = (decimal)MenuHelper.DocSoThuc("Nhập giá tối thiểu: ", 0, double.MaxValue);
            decimal giaMax = (decimal)MenuHelper.DocSoThuc("Nhập giá tối đa: ", (double)giaMin, double.MaxValue);
            ketQua = hangHoaService.LocTheoKhoangGia(giaMin, giaMax);
            tieuDe = $"LỌC THEO GIÁ: {giaMin:N0} - {giaMax:N0}";
        }

        static void LocTheoTrangThai(out List<HangHoa> ketQua, out string tieuDe)
        {
            MenuHelper.HienThiTieuDe("LỌC THEO TRẠNG THÁI");
            Console.WriteLine("Các trạng thái: Hết hàng, Sắp hết, Còn hàng");
            string trangThai = MenuHelper.DocChuoiKhongRong("Nhập trạng thái: ");
            ketQua = hangHoaService.LocTheoTrangThai(trangThai);
            tieuDe = $"LỌC THEO TRẠNG THÁI: {trangThai}";
        }

        static void LocTheoPhanLoaiGia(out List<HangHoa> ketQua, out string tieuDe)
        {
            MenuHelper.HienThiTieuDe("LỌC THEO PHÂN LOẠI GIÁ");
            Console.WriteLine("Các phân loại: Cao cấp, Trung cấp, Bình dân, Giá rẻ");
            string phanLoai = MenuHelper.DocChuoiKhongRong("Nhập phân loại: ");
            ketQua = hangHoaService.LocTheoPhanLoaiGia(phanLoai);
            tieuDe = $"LỌC THEO PHÂN LOẠI GIÁ: {phanLoai}";
        }

        static void MenuThongKe()
        {
            while (true)
            {
                MenuHelper.HienThiMenuThongKe();
                int luaChon = MenuHelper.DocSoNguyen("", 0, 6);

                switch (luaChon)
                {
                    case 1:
                        ThongKeSoLuongTheoDonViTinh();
                        break;
                    case 2:
                        ThongKeSoLuongTheoTrangThai();
                        break;
                    case 3:
                        TinhTongGiaTriHangHoa();
                        break;
                    case 4:
                        TimHangHoaGiaCaoNhat();
                        break;
                    case 5:
                        TimHangHoaGiaThapNhat();
                        break;
                    case 6:
                        HienThiTongSoHangHoa();
                        break;
                    case 0:
                        return;
                }
            }
        }

        static void ThongKeSoLuongTheoDonViTinh()
        {
            var thongKe = hangHoaService.ThongKeSoLuongTheoDonViTinh();
            DataHelper.HienThiThongKe(thongKe, "THỐNG KÊ SỐ LƯỢNG THEO ĐƠN VỊ TÍNH", "Đơn vị tính");
            MenuHelper.TamDung();
        }

        static void ThongKeSoLuongTheoTrangThai()
        {
            var thongKe = hangHoaService.ThongKeSoLuongTheoTrangThai();
            DataHelper.HienThiThongKe(thongKe, "THỐNG KÊ SỐ LƯỢNG THEO TRẠNG THÁI", "Trạng thái");
            MenuHelper.TamDung();
        }

        static void TinhTongGiaTriHangHoa()
        {
            MenuHelper.HienThiTieuDe("TỔNG GIÁ TRỊ HÀNG HÓA");
            decimal tongGiaTri = hangHoaService.TinhTongGiaTriHangHoa();
            Console.WriteLine($"Tổng giá trị hàng hóa: {tongGiaTri:C}");
            MenuHelper.TamDung();
        }

        static void TimHangHoaGiaCaoNhat()
        {
            MenuHelper.HienThiTieuDe("HÀNG HÓA GIÁ CAO NHẤT");
            var hangHoa = hangHoaService.TimHangHoaGiaCaoNhat();
            if (hangHoa != null)
            {
                DataHelper.HienThiChiTiet(hangHoa);
            }
            else
            {
                Console.WriteLine("Không có dữ liệu!");
            }
            MenuHelper.TamDung();
        }

        static void TimHangHoaGiaThapNhat()
        {
            MenuHelper.HienThiTieuDe("HÀNG HÓA GIÁ THẤP NHẤT");
            var hangHoa = hangHoaService.TimHangHoaGiaThapNhat();
            if (hangHoa != null)
            {
                DataHelper.HienThiChiTiet(hangHoa);
            }
            else
            {
                Console.WriteLine("Không có dữ liệu!");
            }
            MenuHelper.TamDung();
        }

        static void HienThiTongSoHangHoa()
        {
            MenuHelper.HienThiTieuDe("TỔNG SỐ HÀNG HÓA");
            int tongSo = hangHoaService.DemSoLuong();
            Console.WriteLine($"Tổng số mặt hàng trong danh sách: {tongSo}");
            MenuHelper.TamDung();
        }

        static void CapNhatThongTin()
        {
            MenuHelper.HienThiTieuDe("CẬP NHẬT THÔNG TIN");

            if (hangHoaService.DemSoLuong() == 0)
            {
                Console.WriteLine("Danh sách trống!");
                MenuHelper.TamDung();
                return;
            }

            string maSo = MenuHelper.DocChuoiKhongRong("Nhập mã số cần cập nhật: ");

            var hangHoaCu = hangHoaService.TimKiemTheoMaSo(maSo);
            if (hangHoaCu != null)
            {
                Console.WriteLine("Thông tin hiện tại:");
                DataHelper.HienThiChiTiet(hangHoaCu);

                Console.WriteLine("\nNhập thông tin mới:");
                var hangHoaMoi = DataHelper.NhapThongTinHangHoa();

                if (hangHoaService.CapNhatHangHoa(maSo, hangHoaMoi))
                {
                    Console.WriteLine("Cập nhật thành công!");
                }
                else
                {
                    Console.WriteLine("Cập nhật thất bại!");
                }
            }
            else
            {
                Console.WriteLine($"Không tìm thấy hàng hóa có mã số: {maSo}");
            }

            MenuHelper.TamDung();
        }

        static void TaoDuLieuMau()
        {
            MenuHelper.HienThiTieuDe("TẠO DỮ LIỆU MẪU");

            Console.Write("Bạn có chắc chắn muốn tạo dữ liệu mẫu? (y/n): ");
            string xacNhan = Console.ReadLine()?.ToLower() ?? "";

            if (xacNhan == "y" || xacNhan == "yes")
            {
                DataHelper.TaoDuLieuMau(hangHoaService);
            }
            else
            {
                Console.WriteLine("Đã hủy thao tác!");
            }

            MenuHelper.TamDung();
        }
    }
}
