# PowerShell script to download and setup Jo<PERSON><PERSON> for Electronics Shop
Write-Host "========================================" -ForegroundColor Green
Write-Host "   Joomla Download & Setup Script" -ForegroundColor Green
Write-Host "   Electronics Shop Project" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: Please run this script as Administrator" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Variables
$joomlaVersion = "4.4.2"
$joomlaUrl = "https://github.com/joomla/joomla-cms/releases/download/$joomlaVersion/Joomla_$joomlaVersion-Stable-Full_Package.zip"
$xamppPath = "C:\xampp\htdocs"
$projectName = "electronics-shop"
$projectPath = "$xamppPath\$projectName"
$tempPath = "$env:TEMP\joomla-download"

# Check if XAMPP is installed
if (-not (Test-Path "C:\xampp\xampp-control.exe")) {
    Write-Host "ERROR: XAMPP not found!" -ForegroundColor Red
    Write-Host "Please install XAMPP first using install-xampp.bat" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "XAMPP found - OK" -ForegroundColor Green

# Create temp directory
if (-not (Test-Path $tempPath)) {
    New-Item -ItemType Directory -Path $tempPath -Force | Out-Null
}

Write-Host ""
Write-Host "Downloading Joomla $joomlaVersion..." -ForegroundColor Yellow
Write-Host "URL: $joomlaUrl" -ForegroundColor Gray

try {
    # Download Joomla
    $zipFile = "$tempPath\joomla-$joomlaVersion.zip"
    Invoke-WebRequest -Uri $joomlaUrl -OutFile $zipFile -UseBasicParsing
    
    if (-not (Test-Path $zipFile)) {
        throw "Download failed"
    }
    
    Write-Host "Download completed successfully!" -ForegroundColor Green
    
    # Create project directory
    if (Test-Path $projectPath) {
        Write-Host "Project directory already exists. Backing up..." -ForegroundColor Yellow
        $backupPath = "$projectPath-backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
        Move-Item $projectPath $backupPath
        Write-Host "Backup created at: $backupPath" -ForegroundColor Green
    }
    
    New-Item -ItemType Directory -Path $projectPath -Force | Out-Null
    Write-Host "Created project directory: $projectPath" -ForegroundColor Green
    
    # Extract Joomla
    Write-Host "Extracting Joomla files..." -ForegroundColor Yellow
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::ExtractToDirectory($zipFile, $projectPath)
    
    Write-Host "Joomla extracted successfully!" -ForegroundColor Green
    
    # Set permissions (if needed)
    Write-Host "Setting directory permissions..." -ForegroundColor Yellow
    icacls $projectPath /grant "IIS_IUSRS:(OI)(CI)F" /T | Out-Null
    icacls $projectPath /grant "IUSR:(OI)(CI)F" /T | Out-Null
    
    # Clean up
    Remove-Item $tempPath -Recurse -Force
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "   Joomla Setup Complete!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Start XAMPP (Apache + MySQL)" -ForegroundColor White
    Write-Host "2. Open http://localhost/$projectName in your browser" -ForegroundColor White
    Write-Host "3. Follow Joomla installation wizard" -ForegroundColor White
    Write-Host "4. Create database 'electronics_shop' in phpMyAdmin" -ForegroundColor White
    Write-Host ""
    Write-Host "Project location: $projectPath" -ForegroundColor Cyan
    Write-Host ""
    
    # Ask if user wants to open browser
    $openBrowser = Read-Host "Do you want to open the installation page now? (y/n)"
    if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
        Start-Process "http://localhost/$projectName"
    }
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please check your internet connection and try again." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Script completed!" -ForegroundColor Green
Read-Host "Press Enter to exit"
