"""
Cài đặt giải thuật Dijkstra tìm đường đi ngắn nhất
Author: Student
Date: 2025
"""

import heapq
import sys
from typing import List, Tuple, Dict

class Graph:
    """Lớp biểu diễn đồ thị có trọng số"""
    
    def __init__(self, vertices: int):
        """
        Khởi tạo đồ thị
        Args:
            vertices: Số lượng đỉnh
        """
        self.V = vertices
        self.graph = [[] for _ in range(vertices)]
    
    def add_edge(self, u: int, v: int, weight: int):
        """
        Thêm cạnh vào đồ thị
        Args:
            u: Đỉnh nguồn
            v: Đỉnh đích
            weight: Trọng số của cạnh
        """
        self.graph[u].append((v, weight))
        # Nếu là đồ thị vô hướng, thêm cạnh ngược lại
        # self.graph[v].append((u, weight))
    
    def dijkstra(self, src: int) -> <PERSON>ple[List[int], List[int]]:
        """
        G<PERSON><PERSON><PERSON> thuật Dijkstra tìm đường đi ngắn nhất từ đỉnh nguồn
        Args:
            src: Đỉnh nguồn
        Returns:
            <PERSON><PERSON> chứa mảng khoảng cách và mảng đỉnh cha
        """
        # Khởi tạo khoảng cách ban đầu
        dist = [sys.maxsize] * self.V
        dist[src] = 0
        
        # Mảng lưu đỉnh cha để truy vết đường đi
        parent = [-1] * self.V
        
        # Heap ưu tiên để chọn đỉnh có khoảng cách nhỏ nhất
        pq = [(0, src)]
        
        # Tập các đỉnh đã được xử lý
        visited = [False] * self.V
        
        while pq:
            # Lấy đỉnh có khoảng cách nhỏ nhất
            current_dist, u = heapq.heappop(pq)
            
            # Bỏ qua nếu đỉnh đã được xử lý
            if visited[u]:
                continue
                
            visited[u] = True
            
            # Duyệt tất cả các đỉnh kề của u
            for v, weight in self.graph[u]:
                # Tính khoảng cách mới qua u
                new_dist = dist[u] + weight
                
                # Nếu tìm được đường đi ngắn hơn
                if new_dist < dist[v]:
                    dist[v] = new_dist
                    parent[v] = u
                    heapq.heappush(pq, (new_dist, v))
        
        return dist, parent
    
    def print_solution(self, dist: List[int], parent: List[int], src: int):
        """
        In kết quả đường đi ngắn nhất
        Args:
            dist: Mảng khoảng cách
            parent: Mảng đỉnh cha
            src: Đỉnh nguồn
        """
        print(f"Khoảng cách ngắn nhất từ đỉnh {src}:")
        print("Đỉnh\tKhoảng cách\tĐường đi")
        
        for i in range(self.V):
            if dist[i] == sys.maxsize:
                print(f"{i}\t∞\t\tKhông có đường đi")
            else:
                path = self.get_path(parent, i)
                print(f"{i}\t{dist[i]}\t\t{' -> '.join(map(str, path))}")
    
    def get_path(self, parent: List[int], target: int) -> List[int]:
        """
        Truy vết đường đi từ nguồn đến đích
        Args:
            parent: Mảng đỉnh cha
            target: Đỉnh đích
        Returns:
            Danh sách các đỉnh trong đường đi
        """
        path = []
        current = target
        
        while current != -1:
            path.append(current)
            current = parent[current]
        
        path.reverse()
        return path

def create_example_graph() -> Graph:
    """Tạo đồ thị ví dụ để minh họa"""
    # Tạo đồ thị có 6 đỉnh
    g = Graph(6)
    
    # Thêm các cạnh (u, v, trọng số)
    g.add_edge(0, 1, 4)
    g.add_edge(0, 2, 2)
    g.add_edge(1, 2, 1)
    g.add_edge(1, 3, 5)
    g.add_edge(2, 3, 8)
    g.add_edge(2, 4, 10)
    g.add_edge(3, 4, 2)
    g.add_edge(3, 5, 6)
    g.add_edge(4, 5, 3)
    
    return g

def main():
    """Hàm chính để chạy chương trình"""
    print("=== GIẢI THUẬT DIJKSTRA - TÌM ĐƯỜNG ĐI NGẮN NHẤT ===\n")
    
    # Tạo đồ thị ví dụ
    graph = create_example_graph()
    
    print("Đồ thị ví dụ:")
    print("Các cạnh: (0,1,4), (0,2,2), (1,2,1), (1,3,5), (2,3,8), (2,4,10), (3,4,2), (3,5,6), (4,5,3)")
    print()
    
    # Chọn đỉnh nguồn
    source = 0
    print(f"Tìm đường đi ngắn nhất từ đỉnh {source}:")
    print()
    
    # Chạy giải thuật Dijkstra
    distances, parents = graph.dijkstra(source)
    
    # In kết quả
    graph.print_solution(distances, parents, source)
    
    print("\n=== PHÂN TÍCH KẾT QUẢ ===")
    print("- Giải thuật Dijkstra đã tìm được đường đi ngắn nhất từ đỉnh 0 đến tất cả các đỉnh khác")
    print("- Độ phức tạp thời gian: O((V + E) log V)")
    print("- Độ phức tạp không gian: O(V)")
    print("- Giải thuật chỉ áp dụng được với đồ thị có trọng số không âm")

if __name__ == "__main__":
    main()
