# BÁO CÁO KỸ THUẬT CHI TIẾT
## WEBSITE TRAO ĐỔI SÁCH, TẠP CHÍ TRÊN WORDPRESS

---

## 1. KIẾN TRÚC HỆ THỐNG

### 1.1 Tổng quan kiến trúc
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │◄──►│   Apache Web    │◄──►│   MySQL DB      │
│   (Frontend)    │    │   Server        │    │   (Backend)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   PHP Engine    │
                       │   (WordPress)   │
                       └─────────────────┘
```

### 1.2 Thành phần hệ thống
| Thành phần | Công nghệ | Phiên bản | Vai trò |
|------------|-----------|-----------|---------|
| **Web Server** | Apache HTTP Server | 2.4.58 | Xử lý HTTP requests |
| **Database** | MySQL/MariaDB | 8.0.35 | Lưu trữ dữ liệu |
| **Backend** | PHP | 8.2.12 | Xử lý logic nghiệp vụ |
| **CMS** | WordPress | 6.4.2 | Quản lý nội dung |
| **Frontend** | HTML5/CSS3/JS | Latest | Giao diện người dùng |

---

## 2. CẤU HÌNH CHI TIẾT

### 2.1 Cấu hình Apache (httpd.conf)
```apache
# Document Root
DocumentRoot "C:/xampp/htdocs"

# Directory Index
DirectoryIndex index.php index.html index.htm

# URL Rewriting
LoadModule rewrite_module modules/mod_rewrite.so

# Virtual Host cho book-exchange
<VirtualHost *:80>
    DocumentRoot "C:/xampp/htdocs/book-exchange"
    ServerName book-exchange.local
    <Directory "C:/xampp/htdocs/book-exchange">
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

### 2.2 Cấu hình MySQL (my.ini)
```ini
[mysql]
default-character-set = utf8mb4

[mysqld]
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
max_connections = 200
innodb_buffer_pool_size = 256M
```

### 2.3 Cấu hình PHP (php.ini)
```ini
; Memory và Time Limits
memory_limit = 256M
max_execution_time = 300
max_input_time = 300

; File Upload
upload_max_filesize = 64M
post_max_size = 64M
max_file_uploads = 20

; Extensions
extension=mysqli
extension=pdo_mysql
extension=gd
extension=curl
extension=zip
```

---

## 3. CẤU TRÚC CƠ SỞ DỮ LIỆU

### 3.1 Database Schema
```sql
-- Database: book_exchange
CREATE DATABASE book_exchange 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Bảng chính của WordPress (prefix: be_)
be_posts          -- Bài viết và sách
be_postmeta       -- Metadata của posts
be_users          -- Người dùng
be_usermeta       -- Metadata của users
be_terms          -- Taxonomy terms
be_term_taxonomy  -- Taxonomy definitions
be_term_relationships -- Liên kết posts với terms
```

### 3.2 Custom Tables cho Books
```sql
-- Bảng thông tin sách chi tiết
CREATE TABLE be_book_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    post_id BIGINT(20) NOT NULL,
    author VARCHAR(255),
    isbn VARCHAR(20),
    publish_year YEAR,
    condition_rating TINYINT,
    exchange_type ENUM('trade', 'sell', 'free'),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (post_id) REFERENCES be_posts(ID)
);

-- Index cho tối ưu truy vấn
CREATE INDEX idx_book_author ON be_book_details(author);
CREATE INDEX idx_book_year ON be_book_details(publish_year);
CREATE INDEX idx_book_condition ON be_book_details(condition_rating);
```

---

## 4. CUSTOM POST TYPES VÀ TAXONOMIES

### 4.1 Custom Post Type "Book"
```php
function create_book_post_type() {
    $labels = array(
        'name' => 'Sách',
        'singular_name' => 'Sách',
        'menu_name' => 'Quản lý Sách',
        'add_new' => 'Thêm sách mới',
        'add_new_item' => 'Thêm sách mới',
        'edit_item' => 'Chỉnh sửa sách',
        'new_item' => 'Sách mới',
        'view_item' => 'Xem sách',
        'search_items' => 'Tìm sách',
        'not_found' => 'Không tìm thấy sách nào',
        'not_found_in_trash' => 'Không có sách trong thùng rác'
    );

    $args = array(
        'labels' => $labels,
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'sach'),
        'capability_type' => 'post',
        'has_archive' => true,
        'hierarchical' => false,
        'menu_position' => 5,
        'menu_icon' => 'dashicons-book-alt',
        'supports' => array(
            'title', 
            'editor', 
            'thumbnail', 
            'excerpt', 
            'custom-fields', 
            'author'
        ),
        'show_in_rest' => true
    );

    register_post_type('book', $args);
}
add_action('init', 'create_book_post_type');
```

### 4.2 Custom Taxonomies
```php
// Thể loại sách
register_taxonomy('book_category', array('book'), array(
    'hierarchical' => true,
    'labels' => array(
        'name' => 'Thể loại sách',
        'singular_name' => 'Thể loại'
    ),
    'show_ui' => true,
    'show_admin_column' => true,
    'query_var' => true,
    'rewrite' => array('slug' => 'the-loai'),
    'show_in_rest' => true
));

// Tình trạng sách
register_taxonomy('book_condition', array('book'), array(
    'hierarchical' => false,
    'labels' => array(
        'name' => 'Tình trạng sách',
        'singular_name' => 'Tình trạng'
    ),
    'show_ui' => true,
    'show_admin_column' => true,
    'query_var' => true,
    'rewrite' => array('slug' => 'tinh-trang'),
    'show_in_rest' => true
));

// Khu vực
register_taxonomy('book_location', array('book'), array(
    'hierarchical' => true,
    'labels' => array(
        'name' => 'Khu vực',
        'singular_name' => 'Khu vực'
    ),
    'show_ui' => true,
    'show_admin_column' => true,
    'query_var' => true,
    'rewrite' => array('slug' => 'khu-vuc'),
    'show_in_rest' => true
));
```

---

## 5. CUSTOM FIELDS VÀ META BOXES

### 5.1 Meta Box cho thông tin sách
```php
function add_book_meta_boxes() {
    add_meta_box(
        'book-details',
        'Thông tin chi tiết sách',
        'book_details_callback',
        'book',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_book_meta_boxes');

function book_details_callback($post) {
    wp_nonce_field('save_book_details', 'book_details_nonce');
    
    $author = get_post_meta($post->ID, 'book_author', true);
    $year = get_post_meta($post->ID, 'publish_year', true);
    $isbn = get_post_meta($post->ID, 'isbn', true);
    $exchange_type = get_post_meta($post->ID, 'exchange_type', true);
    $contact_phone = get_post_meta($post->ID, 'contact_phone', true);
    $contact_email = get_post_meta($post->ID, 'contact_email', true);
    
    // HTML form fields...
}
```

### 5.2 Lưu Custom Fields
```php
function save_book_details($post_id) {
    if (!isset($_POST['book_details_nonce']) || 
        !wp_verify_nonce($_POST['book_details_nonce'], 'save_book_details')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    $fields = array(
        'book_author', 
        'publish_year', 
        'isbn', 
        'exchange_type', 
        'contact_phone', 
        'contact_email'
    );
    
    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post', 'save_book_details');
```

---

## 6. HỆ THỐNG BACKUP VÀ RESTORE

### 6.1 Script Backup Database
```batch
@echo off
REM backup-database.bat
echo Dang sao luu co so du lieu...

REM Tao thu muc backup
if not exist "backups" mkdir backups

REM Lay timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "timestamp=%dt:~0,4%-%dt:~4,2%-%dt:~6,2%_%dt:~8,2%-%dt:~10,2%-%dt:~12,2%"

REM Backup database
"C:\xampp\mysql\bin\mysqldump.exe" -u root book_exchange > "backups\book_exchange_backup_%timestamp%.sql"

if %errorlevel% equ 0 (
    echo Sao luu thanh cong: backups\book_exchange_backup_%timestamp%.sql
) else (
    echo Sao luu that bai!
)
pause
```

### 6.2 Script Restore Database
```batch
@echo off
REM restore-database.bat
echo Dang khoi phuc co so du lieu...

set /p backup_file="Nhap ten file backup: "

if not exist "backups\%backup_file%" (
    echo File backup khong ton tai!
    pause
    exit
)

echo CANH BAO: Thao tac nay se ghi de toan bo du lieu hien tai!
set /p confirm="Ban co chac chan? (Y/N): "

if /i not "%confirm%"=="Y" (
    echo Da huy thao tac.
    pause
    exit
)

REM Drop va tao lai database
"C:\xampp\mysql\bin\mysql.exe" -u root -e "DROP DATABASE IF EXISTS book_exchange; CREATE DATABASE book_exchange CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

REM Restore data
"C:\xampp\mysql\bin\mysql.exe" -u root book_exchange < "backups\%backup_file%"

if %errorlevel% equ 0 (
    echo Khoi phuc thanh cong!
) else (
    echo Khoi phuc that bai!
)
pause
```

---

## 7. BẢO MẬT VÀ TỐI ƯU

### 7.1 Cấu hình bảo mật WordPress
```php
// wp-config.php security settings

// Ẩn thông tin phiên bản WordPress
remove_action('wp_head', 'wp_generator');

// Vô hiệu hóa XML-RPC
add_filter('xmlrpc_enabled', '__return_false');

// Giới hạn login attempts
function limit_login_attempts() {
    // Implementation...
}

// Thay đổi table prefix
$table_prefix = 'be_';

// Security keys (generated)
define('AUTH_KEY',         'book-exchange-auth-key-2025-unique-phrase');
define('SECURE_AUTH_KEY',  'book-exchange-secure-auth-key-2025-unique');
// ... other keys
```

### 7.2 Tối ưu hiệu năng
```php
// Caching
define('WP_CACHE', true);

// Compression
define('COMPRESS_CSS', true);
define('COMPRESS_SCRIPTS', true);

// Database optimization
define('WP_POST_REVISIONS', 3);
define('AUTOSAVE_INTERVAL', 300);
define('WP_CRON_LOCK_TIMEOUT', 60);
```

---

## 8. TESTING VÀ DEBUGGING

### 8.1 Test Cases
```
1. Functional Testing:
   - User registration/login
   - Book posting workflow
   - Search and filter functionality
   - Contact form submission
   - Admin panel operations

2. Performance Testing:
   - Page load times
   - Database query optimization
   - Image loading optimization
   - Concurrent user handling

3. Security Testing:
   - SQL injection prevention
   - XSS protection
   - CSRF token validation
   - File upload security

4. Backup/Restore Testing:
   - Database backup integrity
   - Full restore verification
   - Incremental backup testing
```

### 8.2 Debug Configuration
```php
// wp-config.php debug settings
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('SCRIPT_DEBUG', true);
```

---

## 9. DEPLOYMENT CHECKLIST

### 9.1 Pre-deployment
- [ ] Code review completed
- [ ] All tests passed
- [ ] Database optimized
- [ ] Security audit completed
- [ ] Backup system tested
- [ ] Documentation updated

### 9.2 Production Configuration
```php
// Production wp-config.php
define('WP_DEBUG', false);
define('DISALLOW_FILE_EDIT', true);
define('AUTOMATIC_UPDATER_DISABLED', true);
define('WP_POST_REVISIONS', 3);
```

---

**Báo cáo kỹ thuật được cập nhật:** [Ngày/Tháng/Năm]
