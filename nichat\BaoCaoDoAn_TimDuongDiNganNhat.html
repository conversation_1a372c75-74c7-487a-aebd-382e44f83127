<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header h2 {
            font-size: 1.8em;
            margin-bottom: 20px;
            font-weight: 300;
        }

        .student-info {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .student-info p {
            margin: 5px 0;
            font-size: 1.1em;
        }

        .content {
            padding: 40px;
        }

        .toc {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 40px;
            border-left: 5px solid #3498db;
        }

        .toc h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .toc ul {
            list-style: none;
        }

        .toc li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }

        .toc li:before {
            content: "▶";
            position: absolute;
            left: 0;
            color: #3498db;
        }

        .toc a {
            text-decoration: none;
            color: #2c3e50;
            font-weight: 500;
            transition: color 0.3s;
        }

        .toc a:hover {
            color: #3498db;
        }

        .chapter {
            margin-bottom: 50px;
            padding: 30px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .chapter h2 {
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #3498db;
        }

        .chapter h3 {
            color: #34495e;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
            padding-left: 15px;
            border-left: 4px solid #e74c3c;
        }

        .chapter h4 {
            color: #7f8c8d;
            font-size: 1.2em;
            margin: 20px 0 10px 0;
        }

        .chapter p {
            margin-bottom: 15px;
            text-align: justify;
            font-size: 1.1em;
        }

        .algorithm-box {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 5px solid #e74c3c;
        }

        .algorithm-box h4 {
            color: #e74c3c;
            margin-bottom: 15px;
        }

        .pros-cons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .pros, .cons {
            padding: 20px;
            border-radius: 8px;
        }

        .pros {
            background: #d5f4e6;
            border-left: 5px solid #27ae60;
        }

        .cons {
            background: #fadbd8;
            border-left: 5px solid #e74c3c;
        }

        .pros h5, .cons h5 {
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .pros h5 {
            color: #27ae60;
        }

        .cons h5 {
            color: #e74c3c;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            position: relative;
        }

        .code-block:before {
            content: "Python";
            position: absolute;
            top: 10px;
            right: 15px;
            background: #3498db;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.8em;
        }

        .graph-visualization {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .graph-svg {
            max-width: 100%;
            height: auto;
        }

        .complexity-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .complexity-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }

        .complexity-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #ecf0f1;
        }

        .complexity-table tr:hover {
            background: #f8f9fa;
        }

        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            border-left: 5px solid #ffc107;
            margin: 20px 0;
        }

        .conclusion {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 10px;
            margin-top: 40px;
            text-align: center;
        }

        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 40px;
        }

        @media (max-width: 768px) {
            .pros-cons {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>BÁO CÁO ĐỒ ÁN</h1>
            <h2>TÌM ĐƯỜNG ĐI NGẮN NHẤT TRÊN ĐỒ THỊ CÓ TRỌNG SỐ</h2>
            
            <div class="student-info">
                <p><strong>Sinh viên thực hiện:</strong> [Tên sinh viên]</p>
                <p><strong>Mã số sinh viên:</strong> [MSSV]</p>
                <p><strong>Lớp:</strong> [Tên lớp]</p>
                <p><strong>Giảng viên hướng dẫn:</strong> [Tên giảng viên]</p>
                <p><strong>Năm học:</strong> 2024-2025</p>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Mục lục -->
            <div class="toc">
                <h3>📋 MỤC LỤC</h3>
                <ul>
                    <li><a href="#loi-cam-on">Lời cảm ơn</a></li>
                    <li><a href="#chuong-1">Chương 1: Tổng quan đề tài</a>
                        <ul style="margin-left: 20px;">
                            <li><a href="#gioi-thieu">1.1 Giới thiệu chung</a></li>
                            <li><a href="#dat-van-de">1.2 Đặt vấn đề</a></li>
                            <li><a href="#muc-tieu">1.3 Mục tiêu và phạm vi</a></li>
                        </ul>
                    </li>
                    <li><a href="#chuong-2">Chương 2: Nghiên cứu lý thuyết</a>
                        <ul style="margin-left: 20px;">
                            <li><a href="#khai-niem">2.1 Khái niệm cơ bản</a></li>
                            <li><a href="#bai-toan">2.2 Bài toán đường đi ngắn nhất</a></li>
                            <li><a href="#thuat-toan">2.3 Các thuật toán</a></li>
                        </ul>
                    </li>
                    <li><a href="#chuong-3">Chương 3: Xây dựng chương trình</a></li>
                    <li><a href="#chuong-4">Chương 4: Kết quả và hướng phát triển</a></li>
                    <li><a href="#ket-luan">Kết luận</a></li>
                    <li><a href="#tai-lieu">Tài liệu tham khảo</a></li>
                </ul>
            </div>

            <!-- Lời cảm ơn -->
            <div class="chapter" id="loi-cam-on">
                <h2>🙏 LỜI CẢM ƠN</h2>
                <p>Em xin chân thành cảm ơn thầy/cô giáo đã tận tình hướng dẫn và giúp đỡ em trong quá trình thực hiện đồ án này. Qua đồ án, em đã có cơ hội tìm hiểu sâu hơn về các thuật toán tìm đường đi ngắn nhất trên đồ thị và ứng dụng thực tế của chúng.</p>
                
                <p>Em cũng xin cảm ơn gia đình, bạn bè đã động viên và hỗ trợ em trong suốt quá trình học tập và nghiên cứu. Những kiến thức thu được từ đồ án này sẽ là nền tảng quan trọng cho việc học tập và nghiên cứu của em trong tương lai.</p>
            </div>

            <!-- Chương 1 -->
            <div class="chapter" id="chuong-1">
                <h2>📖 CHƯƠNG 1: TỔNG QUAN ĐỀ TÀI</h2>
                
                <h3 id="gioi-thieu">1.1 Giới thiệu chung</h3>
                <p>Bài toán tìm đường đi ngắn nhất trên đồ thị là một trong những bài toán cơ bản và quan trọng nhất trong lý thuyết đồ thị và khoa học máy tính. Bài toán này có ứng dụng rộng rãi trong nhiều lĩnh vực:</p>
                
                <div class="highlight">
                    <strong>🚗 Giao thông vận tải:</strong> Tìm tuyến đường ngắn nhất giữa hai địa điểm<br>
                    <strong>🌐 Mạng máy tính:</strong> Định tuyến gói tin trong mạng<br>
                    <strong>📦 Logistics:</strong> Tối ưu hóa vận chuyển hàng hóa<br>
                    <strong>🎮 Game development:</strong> AI pathfinding<br>
                    <strong>💰 Kinh tế:</strong> Tối ưu hóa chi phí trong chuỗi cung ứng
                </div>

                <h3 id="dat-van-de">1.2 Đặt vấn đề</h3>
                <p>Trong thực tế, chúng ta thường gặp phải các bài toán cần tìm đường đi tối ưu giữa các điểm. Ví dụ:</p>
                <ul>
                    <li>Một người muốn đi từ nhà đến công ty bằng đường ngắn nhất</li>
                    <li>Một công ty vận chuyển muốn tối ưu hóa chi phí giao hàng</li>
                    <li>Một hệ thống GPS cần tính toán tuyến đường tối ưu</li>
                </ul>

                <div class="graph-visualization">
                    <h4>Ví dụ minh họa bài toán</h4>
                    <svg class="graph-svg" width="500" height="300" viewBox="0 0 500 300">
                        <!-- Định nghĩa gradient -->
                        <defs>
                            <linearGradient id="nodeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="pathGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        
                        <!-- Các cạnh -->
                        <line x1="50" y1="50" x2="150" y2="50" stroke="#bdc3c7" stroke-width="2"/>
                        <line x1="50" y1="50" x2="100" y2="150" stroke="#bdc3c7" stroke-width="2"/>
                        <line x1="150" y1="50" x2="250" y2="50" stroke="#bdc3c7" stroke-width="2"/>
                        <line x1="150" y1="50" x2="200" y2="150" stroke="#bdc3c7" stroke-width="2"/>
                        <line x1="100" y1="150" x2="200" y2="150" stroke="#bdc3c7" stroke-width="2"/>
                        <line x1="250" y1="50" x2="350" y2="50" stroke="#bdc3c7" stroke-width="2"/>
                        <line x1="250" y1="50" x2="300" y2="150" stroke="#bdc3c7" stroke-width="2"/>
                        <line x1="200" y1="150" x2="300" y2="150" stroke="#bdc3c7" stroke-width="2"/>
                        <line x1="350" y1="50" x2="450" y2="50" stroke="#bdc3c7" stroke-width="2"/>
                        <line x1="350" y1="50" x2="400" y2="150" stroke="#bdc3c7" stroke-width="2"/>
                        <line x1="300" y1="150" x2="400" y2="150" stroke="#bdc3c7" stroke-width="2"/>
                        <line x1="400" y1="150" x2="450" y2="50" stroke="#bdc3c7" stroke-width="2"/>
                        
                        <!-- Đường đi ngắn nhất (màu đỏ) -->
                        <line x1="50" y1="50" x2="100" y2="150" stroke="url(#pathGradient)" stroke-width="4"/>
                        <line x1="100" y1="150" x2="200" y2="150" stroke="url(#pathGradient)" stroke-width="4"/>
                        <line x1="200" y1="150" x2="300" y2="150" stroke="url(#pathGradient)" stroke-width="4"/>
                        <line x1="300" y1="150" x2="400" y2="150" stroke="url(#pathGradient)" stroke-width="4"/>
                        <line x1="400" y1="150" x2="450" y2="50" stroke="url(#pathGradient)" stroke-width="4"/>
                        
                        <!-- Các đỉnh -->
                        <circle cx="50" cy="50" r="20" fill="url(#nodeGradient)" stroke="#2c3e50" stroke-width="2"/>
                        <circle cx="150" cy="50" r="15" fill="url(#nodeGradient)" stroke="#2c3e50" stroke-width="2"/>
                        <circle cx="250" cy="50" r="15" fill="url(#nodeGradient)" stroke="#2c3e50" stroke-width="2"/>
                        <circle cx="350" cy="50" r="15" fill="url(#nodeGradient)" stroke="#2c3e50" stroke-width="2"/>
                        <circle cx="450" cy="50" r="20" fill="url(#nodeGradient)" stroke="#2c3e50" stroke-width="2"/>
                        <circle cx="100" cy="150" r="15" fill="url(#nodeGradient)" stroke="#2c3e50" stroke-width="2"/>
                        <circle cx="200" cy="150" r="15" fill="url(#nodeGradient)" stroke="#2c3e50" stroke-width="2"/>
                        <circle cx="300" cy="150" r="15" fill="url(#nodeGradient)" stroke="#2c3e50" stroke-width="2"/>
                        <circle cx="400" cy="150" r="15" fill="url(#nodeGradient)" stroke="#2c3e50" stroke-width="2"/>
                        
                        <!-- Nhãn đỉnh -->
                        <text x="50" y="55" text-anchor="middle" fill="white" font-weight="bold" font-size="12">A</text>
                        <text x="150" y="55" text-anchor="middle" fill="white" font-weight="bold" font-size="12">B</text>
                        <text x="250" y="55" text-anchor="middle" fill="white" font-weight="bold" font-size="12">C</text>
                        <text x="350" y="55" text-anchor="middle" fill="white" font-weight="bold" font-size="12">D</text>
                        <text x="450" y="55" text-anchor="middle" fill="white" font-weight="bold" font-size="12">E</text>
                        <text x="100" y="155" text-anchor="middle" fill="white" font-weight="bold" font-size="12">F</text>
                        <text x="200" y="155" text-anchor="middle" fill="white" font-weight="bold" font-size="12">G</text>
                        <text x="300" y="155" text-anchor="middle" fill="white" font-weight="bold" font-size="12">H</text>
                        <text x="400" y="155" text-anchor="middle" fill="white" font-weight="bold" font-size="12">I</text>
                        
                        <!-- Trọng số cạnh -->
                        <text x="100" y="35" text-anchor="middle" fill="#2c3e50" font-size="10">4</text>
                        <text x="75" y="105" text-anchor="middle" fill="#2c3e50" font-size="10">2</text>
                        <text x="200" y="35" text-anchor="middle" fill="#2c3e50" font-size="10">3</text>
                        <text x="175" y="105" text-anchor="middle" fill="#2c3e50" font-size="10">5</text>
                        <text x="150" y="165" text-anchor="middle" fill="#2c3e50" font-size="10">1</text>
                        
                        <!-- Chú thích -->
                        <text x="250" y="250" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">
                            Đường đi ngắn nhất từ A đến E (màu đỏ)
                        </text>
                    </svg>
                </div>

                <h3 id="muc-tieu">1.3 Mục tiêu và phạm vi đề tài</h3>
                
                <div class="pros-cons">
                    <div class="pros">
                        <h5>🎯 Mục tiêu</h5>
                        <ul>
                            <li>Nghiên cứu và hiểu rõ các thuật toán tìm đường đi ngắn nhất cơ bản</li>
                            <li>Cài đặt và minh họa hoạt động của các thuật toán bằng Python</li>
                            <li>So sánh hiệu quả và ứng dụng của từng thuật toán</li>
                            <li>Xây dựng chương trình demo có giao diện trực quan</li>
                        </ul>
                    </div>
                    
                    <div class="cons">
                        <h5>📋 Phạm vi</h5>
                        <ul>
                            <li>Tập trung vào 4 thuật toán chính: Dijkstra, Bellman-Ford, Floyd-Warshall, SPFA</li>
                            <li>Cài đặt trên đồ thị có hướng và vô hướng</li>
                            <li>Xử lý đồ thị có trọng số dương và âm</li>
                            <li>Xây dựng giao diện đơn giản để minh họa</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Chương 2 -->
            <div class="chapter" id="chuong-2">
                <h2>🔬 CHƯƠNG 2: NGHIÊN CỨU LÝ THUYẾT</h2>

                <h3 id="khai-niem">2.1 Khái niệm cơ bản về đồ thị</h3>

                <div class="algorithm-box">
                    <h4>📚 Định nghĩa đồ thị</h4>
                    <p><strong>Đồ thị G = (V, E)</strong> bao gồm:</p>
                    <ul>
                        <li><strong>V:</strong> Tập hợp các đỉnh (vertices)</li>
                        <li><strong>E:</strong> Tập hợp các cạnh (edges)</li>
                    </ul>

                    <p><strong>Đồ thị có trọng số:</strong> Là đồ thị mà mỗi cạnh được gán một giá trị số gọi là trọng số, thường ký hiệu là w(u,v) cho cạnh từ đỉnh u đến đỉnh v.</p>
                </div>

                <div class="graph-visualization">
                    <h4>Minh họa các loại đồ thị</h4>
                    <svg class="graph-svg" width="600" height="200" viewBox="0 0 600 200">
                        <!-- Đồ thị vô hướng -->
                        <g>
                            <text x="100" y="20" text-anchor="middle" fill="#2c3e50" font-weight="bold">Đồ thị vô hướng</text>
                            <line x1="50" y1="50" x2="150" y2="50" stroke="#3498db" stroke-width="3"/>
                            <line x1="50" y1="50" x2="100" y2="120" stroke="#3498db" stroke-width="3"/>
                            <line x1="150" y1="50" x2="100" y2="120" stroke="#3498db" stroke-width="3"/>

                            <circle cx="50" cy="50" r="15" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                            <circle cx="150" cy="50" r="15" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                            <circle cx="100" cy="120" r="15" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>

                            <text x="50" y="55" text-anchor="middle" fill="white" font-weight="bold">A</text>
                            <text x="150" y="55" text-anchor="middle" fill="white" font-weight="bold">B</text>
                            <text x="100" y="125" text-anchor="middle" fill="white" font-weight="bold">C</text>

                            <!-- Trọng số -->
                            <text x="100" y="40" text-anchor="middle" fill="#2c3e50" font-size="12">5</text>
                            <text x="70" y="90" text-anchor="middle" fill="#2c3e50" font-size="12">3</text>
                            <text x="130" y="90" text-anchor="middle" fill="#2c3e50" font-size="12">2</text>
                        </g>

                        <!-- Đồ thị có hướng -->
                        <g>
                            <text x="400" y="20" text-anchor="middle" fill="#2c3e50" font-weight="bold">Đồ thị có hướng</text>

                            <!-- Mũi tên -->
                            <defs>
                                <marker id="arrowhead" markerWidth="10" markerHeight="7"
                                        refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
                                </marker>
                            </defs>

                            <line x1="350" y1="50" x2="435" y2="50" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead)"/>
                            <line x1="350" y1="50" x2="385" y2="115" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead)"/>
                            <line x1="450" y1="50" x2="415" y2="115" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead)"/>

                            <circle cx="350" cy="50" r="15" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                            <circle cx="450" cy="50" r="15" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                            <circle cx="400" cy="120" r="15" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>

                            <text x="350" y="55" text-anchor="middle" fill="white" font-weight="bold">A</text>
                            <text x="450" y="55" text-anchor="middle" fill="white" font-weight="bold">B</text>
                            <text x="400" y="125" text-anchor="middle" fill="white" font-weight="bold">C</text>

                            <!-- Trọng số -->
                            <text x="400" y="40" text-anchor="middle" fill="#2c3e50" font-size="12">4</text>
                            <text x="370" y="90" text-anchor="middle" fill="#2c3e50" font-size="12">1</text>
                            <text x="430" y="90" text-anchor="middle" fill="#2c3e50" font-size="12">6</text>
                        </g>
                    </svg>
                </div>

                <h3 id="bai-toan">2.2 Bài toán đường đi ngắn nhất</h3>

                <div class="algorithm-box">
                    <h4>🎯 Phát biểu bài toán</h4>
                    <p>Cho đồ thị có trọng số G = (V, E) và hai đỉnh s, t ∈ V. Tìm đường đi từ s đến t có tổng trọng số nhỏ nhất.</p>
                </div>

                <div class="highlight">
                    <h4>🔄 Các biến thể của bài toán:</h4>
                    <ol>
                        <li><strong>Single-source shortest path:</strong> Tìm đường đi ngắn nhất từ một đỉnh nguồn đến tất cả các đỉnh khác</li>
                        <li><strong>Single-destination shortest path:</strong> Tìm đường đi ngắn nhất từ tất cả các đỉnh đến một đỉnh đích</li>
                        <li><strong>Single-pair shortest path:</strong> Tìm đường đi ngắn nhất giữa hai đỉnh cụ thể</li>
                        <li><strong>All-pairs shortest path:</strong> Tìm đường đi ngắn nhất giữa mọi cặp đỉnh</li>
                    </ol>
                </div>

                <h3 id="thuat-toan">2.3 Các thuật toán tìm đường đi ngắn nhất</h3>

                <!-- Thuật toán Dijkstra -->
                <div class="algorithm-box">
                    <h4>🚀 2.3.1 Thuật toán Dijkstra</h4>

                    <p><strong>Ý tưởng:</strong> Sử dụng phương pháp tham lam (greedy) để tìm đường đi ngắn nhất từ một đỉnh nguồn đến tất cả các đỉnh khác trong đồ thị có trọng số không âm.</p>

                    <p><strong>Nguyên lý hoạt động:</strong></p>
                    <ol>
                        <li>Khởi tạo khoảng cách từ đỉnh nguồn đến chính nó bằng 0, các đỉnh khác bằng vô cùng</li>
                        <li>Chọn đỉnh chưa được xử lý có khoảng cách nhỏ nhất</li>
                        <li>Cập nhật khoảng cách đến các đỉnh kề</li>
                        <li>Đánh dấu đỉnh đã được xử lý</li>
                        <li>Lặp lại cho đến khi xử lý hết tất cả đỉnh</li>
                    </ol>

                    <div class="pros-cons">
                        <div class="pros">
                            <h5>✅ Ưu điểm</h5>
                            <ul>
                                <li>Hiệu quả với đồ thị có trọng số không âm</li>
                                <li>Dễ hiểu và cài đặt</li>
                                <li>Cho kết quả tối ưu</li>
                                <li>Được sử dụng rộng rãi trong thực tế</li>
                            </ul>
                        </div>

                        <div class="cons">
                            <h5>❌ Nhược điểm</h5>
                            <ul>
                                <li>Không xử lý được trọng số âm</li>
                                <li>Phải xử lý toàn bộ đồ thị</li>
                                <li>Độ phức tạp cao với đồ thị dày đặc</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Thuật toán Bellman-Ford -->
                <div class="algorithm-box">
                    <h4>⚡ 2.3.2 Thuật toán Bellman-Ford</h4>

                    <p><strong>Ý tưởng:</strong> Có thể xử lý đồ thị có trọng số âm và phát hiện chu trình âm.</p>

                    <p><strong>Nguyên lý hoạt động:</strong></p>
                    <ol>
                        <li>Khởi tạo khoảng cách từ đỉnh nguồn đến chính nó bằng 0, các đỉnh khác bằng vô cùng</li>
                        <li>Lặp V-1 lần, mỗi lần duyệt qua tất cả các cạnh và thực hiện phép relaxation</li>
                        <li>Kiểm tra chu trình âm bằng cách thực hiện thêm một lần nữa phép relaxation</li>
                    </ol>

                    <div class="pros-cons">
                        <div class="pros">
                            <h5>✅ Ưu điểm</h5>
                            <ul>
                                <li>Xử lý được trọng số âm</li>
                                <li>Phát hiện được chu trình âm</li>
                                <li>Đơn giản để cài đặt</li>
                                <li>Luôn cho kết quả chính xác</li>
                            </ul>
                        </div>

                        <div class="cons">
                            <h5>❌ Nhược điểm</h5>
                            <ul>
                                <li>Chậm hơn Dijkstra với trọng số không âm</li>
                                <li>Độ phức tạp thời gian cao O(VE)</li>
                                <li>Không hiệu quả với đồ thị lớn</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Bảng so sánh độ phức tạp -->
                <table class="complexity-table">
                    <thead>
                        <tr>
                            <th>Thuật toán</th>
                            <th>Độ phức tạp thời gian</th>
                            <th>Độ phức tạp không gian</th>
                            <th>Trọng số âm</th>
                            <th>Phát hiện chu trình âm</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Dijkstra</strong></td>
                            <td>O((V + E)logV)</td>
                            <td>O(V)</td>
                            <td>❌</td>
                            <td>❌</td>
                        </tr>
                        <tr>
                            <td><strong>Bellman-Ford</strong></td>
                            <td>O(VE)</td>
                            <td>O(V)</td>
                            <td>✅</td>
                            <td>✅</td>
                        </tr>
                        <tr>
                            <td><strong>Floyd-Warshall</strong></td>
                            <td>O(V³)</td>
                            <td>O(V²)</td>
                            <td>✅</td>
                            <td>✅</td>
                        </tr>
                        <tr>
                            <td><strong>SPFA</strong></td>
                            <td>O(VE) worst, O(E) avg</td>
                            <td>O(V)</td>
                            <td>✅</td>
                            <td>✅</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Chương 3 -->
            <div class="chapter" id="chuong-3">
                <h2>💻 CHƯƠNG 3: XÂY DỰNG CHƯƠNG TRÌNH THUẬT TOÁN</h2>

                <h3>3.1 Lựa chọn thuật toán</h3>
                <p>Trong đồ án này, em chọn cài đặt thuật toán <strong>Dijkstra</strong> làm thuật toán chính vì những lý do sau:</p>

                <div class="highlight">
                    <ul>
                        <li><strong>🌟 Tính phổ biến:</strong> Dijkstra là thuật toán được sử dụng rộng rãi nhất trong thực tế</li>
                        <li><strong>⚡ Hiệu quả:</strong> Có độ phức tạp thời gian tốt với đồ thị có trọng số không âm</li>
                        <li><strong>📚 Dễ hiểu:</strong> Thuật toán có logic rõ ràng, dễ cài đặt và debug</li>
                        <li><strong>🔧 Ứng dụng thực tế:</strong> Được sử dụng trong nhiều hệ thống định tuyến thực tế</li>
                    </ul>
                </div>

                <h3>3.2 Cấu trúc dữ liệu sử dụng</h3>

                <div class="algorithm-box">
                    <h4>📊 Biểu diễn đồ thị</h4>
                    <ul>
                        <li><strong>Danh sách kề (Adjacency List):</strong> Để biểu diễn đồ thị hiệu quả</li>
                        <li><strong>Priority Queue (heapq):</strong> Để chọn đỉnh có khoảng cách nhỏ nhất</li>
                        <li><strong>Dictionary:</strong> Để lưu khoảng cách và đường đi</li>
                        <li><strong>Set:</strong> Để đánh dấu các đỉnh đã được xử lý</li>
                    </ul>
                </div>

                <h3>3.3 Chi tiết cài đặt thuật toán</h3>

                <div class="code-block">
import heapq
from collections import defaultdict

class Graph:
    def __init__(self):
        self.vertices = set()
        self.edges = defaultdict(list)

    def add_edge(self, u, v, weight):
        """Thêm cạnh có trọng số vào đồ thị"""
        self.vertices.add(u)
        self.vertices.add(v)
        self.edges[u].append((v, weight))

    def dijkstra(self, start, end=None):
        """Thuật toán Dijkstra"""
        distances = {vertex: float('infinity') for vertex in self.vertices}
        previous = {vertex: None for vertex in self.vertices}
        distances[start] = 0

        pq = [(0, start)]
        visited = set()

        while pq:
            current_distance, current_vertex = heapq.heappop(pq)

            if current_vertex in visited:
                continue

            visited.add(current_vertex)

            if end and current_vertex == end:
                break

            for neighbor, weight in self.edges[current_vertex]:
                if neighbor not in visited:
                    new_distance = current_distance + weight

                    if new_distance < distances[neighbor]:
                        distances[neighbor] = new_distance
                        previous[neighbor] = current_vertex
                        heapq.heappush(pq, (new_distance, neighbor))

        return distances, previous
                </div>

                <h3>3.4 Giao diện chương trình và hướng dẫn sử dụng</h3>

                <div class="graph-visualization">
                    <h4>Giao diện demo chương trình</h4>
                    <div style="background: #2c3e50; color: #ecf0f1; padding: 20px; border-radius: 8px; font-family: monospace;">
                        <div style="color: #3498db;">============================================================</div>
                        <div style="color: #e74c3c; font-weight: bold;">    DEMO THUẬT TOÁN TÌM ĐƯỜNG ĐI NGẮN NHẤT</div>
                        <div style="color: #3498db;">============================================================</div>
                        <br>
                        <div>📊 Đồ thị mẫu đã được tạo!</div>
                        <div>Các đỉnh: ['A', 'B', 'C', 'D', 'E', 'F']</div>
                        <br>
                        <div>Các cạnh trong đồ thị:</div>
                        <div>  A ↔ B: 4    A ↔ C: 2</div>
                        <div>  B ↔ C: 1    B ↔ D: 5</div>
                        <div>  C ↔ D: 8    C ↔ E: 10</div>
                        <div>  D ↔ E: 2    D ↔ F: 6</div>
                        <div>  E ↔ F: 3</div>
                        <br>
                        <div style="color: #f39c12;">🚀 Nhập đỉnh bắt đầu: A</div>
                        <div style="color: #f39c12;">🎯 Nhập đỉnh kết thúc: F</div>
                        <br>
                        <div style="color: #27ae60;">✅ Đường đi ngắn nhất từ A đến F:</div>
                        <div style="color: #27ae60;">   A → C → B → D → E → F</div>
                        <div style="color: #27ae60;">📏 Độ dài đường đi: 12</div>
                    </div>
                </div>

                <h3>3.5 Kiểm thử và đánh giá</h3>

                <div class="algorithm-box">
                    <h4>🧪 Test Cases</h4>

                    <p><strong>Test case 1: Đồ thị cơ bản</strong></p>
                    <ul>
                        <li>Đỉnh: A, B, C, D, E, F</li>
                        <li>Tìm đường đi từ A đến F</li>
                        <li>Kết quả mong đợi: A → C → B → D → E → F (độ dài: 12)</li>
                    </ul>

                    <p><strong>Test case 2: Đồ thị có trọng số âm</strong></p>
                    <ul>
                        <li>Kiểm tra khả năng xử lý của Bellman-Ford</li>
                        <li>Phát hiện chu trình âm</li>
                    </ul>
                </div>

                <div class="highlight">
                    <h4>📊 Kết quả kiểm thử:</h4>
                    <ul>
                        <li>✅ Thuật toán Dijkstra hoạt động chính xác với đồ thị có trọng số không âm</li>
                        <li>✅ Thuật toán Bellman-Ford xử lý được cả trọng số âm và phát hiện chu trình âm</li>
                        <li>✅ Giao diện trực quan giúp người dùng dễ hiểu kết quả</li>
                        <li>✅ Thời gian chạy phù hợp với đồ thị có kích thước vừa phải</li>
                    </ul>
                </div>
            </div>

            <!-- Chương 4 -->
            <div class="chapter" id="chuong-4">
                <h2>🎯 CHƯƠNG 4: KẾT QUẢ VÀ HƯỚNG PHÁT TRIỂN</h2>

                <h3>4.1 Kết quả đạt được</h3>

                <div class="pros-cons">
                    <div class="pros">
                        <h5>📚 Về mặt lý thuyết</h5>
                        <ul>
                            <li>Nghiên cứu và hiểu rõ 4 thuật toán tìm đường đi ngắn nhất cơ bản</li>
                            <li>Phân tích ưu nhược điểm và độ phức tạp của từng thuật toán</li>
                            <li>So sánh hiệu quả và phạm vi ứng dụng của các thuật toán</li>
                            <li>Nắm vững các khái niệm cơ bản về lý thuyết đồ thị</li>
                        </ul>
                    </div>

                    <div class="cons">
                        <h5>💻 Về mặt thực hành</h5>
                        <ul>
                            <li>Cài đặt thành công thuật toán Dijkstra và Bellman-Ford bằng Python</li>
                            <li>Xây dựng được chương trình demo với giao diện trực quan</li>
                            <li>Kiểm thử và xác minh tính đúng đắn của thuật toán</li>
                            <li>Tạo được các test case để đánh giá hiệu quả</li>
                        </ul>
                    </div>
                </div>

                <div class="highlight">
                    <h4>🏆 Kết quả cụ thể:</h4>
                    <ul>
                        <li>✅ Chương trình chạy ổn định với các đồ thị có kích thước vừa phải</li>
                        <li>✅ Giao diện đồ họa giúp người dùng dễ hiểu thuật toán</li>
                        <li>✅ Có thể xử lý cả đồ thị có hướng và vô hướng</li>
                        <li>✅ Phát hiện và xử lý được các trường hợp đặc biệt</li>
                        <li>✅ Kết quả chính xác và phù hợp với lý thuyết</li>
                    </ul>
                </div>

                <h3>4.2 Hạn chế của đề tài</h3>

                <div class="algorithm-box">
                    <h4>⚠️ Hạn chế về thuật toán</h4>
                    <ul>
                        <li>Chưa cài đặt thuật toán Floyd-Warshall và SPFA</li>
                        <li>Chưa tối ưu hóa hiệu suất cho đồ thị lớn</li>
                        <li>Chưa xử lý các trường hợp đặc biệt phức tạp</li>
                        <li>Chưa có thuật toán heuristic như A*</li>
                    </ul>

                    <h4>🖥️ Hạn chế về giao diện</h4>
                    <ul>
                        <li>Giao diện còn đơn giản, chưa có tính tương tác cao</li>
                        <li>Chưa có chức năng nhập đồ thị từ file</li>
                        <li>Chưa có chức năng lưu kết quả</li>
                        <li>Chưa có animation để minh họa quá trình thực hiện</li>
                    </ul>
                </div>

                <h3>4.3 Hướng phát triển</h3>

                <div class="pros-cons">
                    <div class="pros">
                        <h5>📅 Ngắn hạn</h5>
                        <ul>
                            <li><strong>Hoàn thiện thuật toán:</strong> Cài đặt thêm Floyd-Warshall và SPFA</li>
                            <li><strong>Tối ưu hiệu suất:</strong> Sử dụng cấu trúc dữ liệu tốt hơn</li>
                            <li><strong>Cải thiện giao diện:</strong> Xây dựng GUI với tkinter hoặc PyQt</li>
                            <li><strong>Thêm tính năng:</strong> Nhập/xuất đồ thị từ file, animation</li>
                        </ul>
                    </div>

                    <div class="cons">
                        <h5>🚀 Dài hạn</h5>
                        <ul>
                            <li><strong>Mở rộng ứng dụng:</strong> GPS, game, mạng máy tính</li>
                            <li><strong>Xử lý dữ liệu thực:</strong> Tích hợp với API bản đồ</li>
                            <li><strong>Nghiên cứu nâng cao:</strong> Thuật toán song song, ML</li>
                            <li><strong>Phát triển sản phẩm:</strong> Web app, mobile app</li>
                        </ul>
                    </div>
                </div>

                <div class="graph-visualization">
                    <h4>Roadmap phát triển</h4>
                    <svg class="graph-svg" width="600" height="300" viewBox="0 0 600 300">
                        <!-- Timeline -->
                        <line x1="50" y1="150" x2="550" y2="150" stroke="#3498db" stroke-width="4"/>

                        <!-- Milestones -->
                        <circle cx="100" cy="150" r="8" fill="#e74c3c"/>
                        <circle cx="200" cy="150" r="8" fill="#f39c12"/>
                        <circle cx="300" cy="150" r="8" fill="#27ae60"/>
                        <circle cx="400" cy="150" r="8" fill="#9b59b6"/>
                        <circle cx="500" cy="150" r="8" fill="#3498db"/>

                        <!-- Labels -->
                        <text x="100" y="130" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Hiện tại</text>
                        <text x="200" y="130" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Tháng 1-2</text>
                        <text x="300" y="130" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Tháng 3-4</text>
                        <text x="400" y="130" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Tháng 5-6</text>
                        <text x="500" y="130" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Tương lai</text>

                        <!-- Descriptions -->
                        <text x="100" y="180" text-anchor="middle" fill="#2c3e50" font-size="10">Dijkstra +</text>
                        <text x="100" y="195" text-anchor="middle" fill="#2c3e50" font-size="10">Bellman-Ford</text>

                        <text x="200" y="180" text-anchor="middle" fill="#2c3e50" font-size="10">Floyd-Warshall</text>
                        <text x="200" y="195" text-anchor="middle" fill="#2c3e50" font-size="10">+ SPFA</text>

                        <text x="300" y="180" text-anchor="middle" fill="#2c3e50" font-size="10">GUI Interface</text>
                        <text x="300" y="195" text-anchor="middle" fill="#2c3e50" font-size="10">+ Animation</text>

                        <text x="400" y="180" text-anchor="middle" fill="#2c3e50" font-size="10">Web App</text>
                        <text x="400" y="195" text-anchor="middle" fill="#2c3e50" font-size="10">+ Real Data</text>

                        <text x="500" y="180" text-anchor="middle" fill="#2c3e50" font-size="10">AI Integration</text>
                        <text x="500" y="195" text-anchor="middle" fill="#2c3e50" font-size="10">+ Mobile App</text>
                    </svg>
                </div>
            </div>

            <!-- Kết luận -->
            <div class="conclusion" id="ket-luan">
                <h2>🎓 KẾT LUẬN</h2>

                <p>Qua quá trình thực hiện đồ án <strong>"Tìm đường đi ngắn nhất trên đồ thị có trọng số"</strong>, em đã đạt được những kết quả quan trọng:</p>

                <div style="text-align: left; margin: 20px 0;">
                    <h3 style="color: #fff; margin-bottom: 15px;">📖 Về kiến thức:</h3>
                    <ul style="margin-left: 20px;">
                        <li>Hiểu sâu về lý thuyết đồ thị và các thuật toán tìm đường đi ngắn nhất</li>
                        <li>Nắm vững ưu nhược điểm và phạm vi ứng dụng của từng thuật toán</li>
                        <li>Có khả năng phân tích và so sánh hiệu quả các thuật toán</li>
                    </ul>

                    <h3 style="color: #fff; margin: 20px 0 15px 0;">🛠️ Về kỹ năng:</h3>
                    <ul style="margin-left: 20px;">
                        <li>Rèn luyện kỹ năng lập trình Python và sử dụng các thư viện</li>
                        <li>Phát triển khả năng thiết kế và cài đặt thuật toán</li>
                        <li>Học cách tạo giao diện trực quan để minh họa thuật toán</li>
                    </ul>

                    <h3 style="color: #fff; margin: 20px 0 15px 0;">🌟 Về ứng dụng:</h3>
                    <ul style="margin-left: 20px;">
                        <li>Nhận thức được tầm quan trọng của bài toán trong thực tế</li>
                        <li>Hiểu được cách áp dụng lý thuyết vào giải quyết vấn đề cụ thể</li>
                        <li>Có nền tảng để phát triển các ứng dụng phức tạp hơn</li>
                    </ul>
                </div>

                <p style="font-size: 1.2em; margin-top: 30px;">Đồ án đã giúp em củng cố kiến thức về cấu trúc dữ liệu và giải thuật, đồng thời phát triển kỹ năng lập trình và tư duy giải quyết vấn đề. Đây là nền tảng quan trọng cho việc học tập và nghiên cứu các chủ đề nâng cao hơn trong tương lai.</p>
            </div>

            <!-- Tài liệu tham khảo -->
            <div class="chapter" id="tai-lieu">
                <h2>📚 TÀI LIỆU THAM KHẢO</h2>

                <div class="algorithm-box">
                    <h4>📖 Sách giáo khoa</h4>
                    <ol>
                        <li>Cormen, T. H., Leiserson, C. E., Rivest, R. L., & Stein, C. (2009). <em>Introduction to Algorithms</em> (3rd ed.). MIT Press.</li>
                        <li>Sedgewick, R., & Wayne, K. (2011). <em>Algorithms</em> (4th ed.). Addison-Wesley.</li>
                        <li>Kleinberg, J., & Tardos, E. (2005). <em>Algorithm Design</em>. Addison-Wesley.</li>
                    </ol>
                </div>

                <div class="algorithm-box">
                    <h4>🌐 Tài liệu trực tuyến</h4>
                    <ol>
                        <li>GeeksforGeeks. "Dijkstra's shortest path algorithm". <br>
                            <a href="https://www.geeksforgeeks.org/dijkstras-shortest-path-algorithm-greedy-algo-7/" target="_blank">
                                https://www.geeksforgeeks.org/dijkstras-shortest-path-algorithm-greedy-algo-7/
                            </a>
                        </li>
                        <li>Wikipedia. "Shortest path problem". <br>
                            <a href="https://en.wikipedia.org/wiki/Shortest_path_problem" target="_blank">
                                https://en.wikipedia.org/wiki/Shortest_path_problem
                            </a>
                        </li>
                        <li>Visualgo. "Graph Traversal". <br>
                            <a href="https://visualgo.net/en/graphtraversal" target="_blank">
                                https://visualgo.net/en/graphtraversal
                            </a>
                        </li>
                    </ol>
                </div>

                <div class="algorithm-box">
                    <h4>🎓 Khóa học</h4>
                    <ol>
                        <li>MIT OpenCourseWare. "Introduction to Algorithms"</li>
                        <li>Coursera. "Algorithms Specialization" by Stanford University</li>
                        <li>edX. "Algorithm Design and Analysis" by University of Pennsylvania</li>
                    </ol>
                </div>

                <div class="algorithm-box">
                    <h4>🐍 Thư viện Python</h4>
                    <ol>
                        <li>NetworkX Documentation. <a href="https://networkx.org/" target="_blank">https://networkx.org/</a></li>
                        <li>Matplotlib Documentation. <a href="https://matplotlib.org/" target="_blank">https://matplotlib.org/</a></li>
                        <li>Python heapq module. <a href="https://docs.python.org/3/library/heapq.html" target="_blank">https://docs.python.org/3/library/heapq.html</a></li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>&copy; 2024 - Báo cáo đồ án: Tìm đường đi ngắn nhất trên đồ thị có trọng số</p>
            <p>Được thực hiện với ❤️ bằng HTML, CSS và SVG</p>
        </div>
    </div>
</body>
</html>
