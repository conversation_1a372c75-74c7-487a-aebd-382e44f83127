using System;
using System.Collections.Generic;

namespace StudentManagement
{
    /// <summary>
    /// Danh sách liên kết kép để quản lý sinh viên
    /// </summary>
    public class DoublyLinkedList
    {
        private DoublyStudentNode head;
        private DoublyStudentNode tail;
        private int count;

        public int Count => count;

        public DoublyLinkedList()
        {
            head = null;
            tail = null;
            count = 0;
        }

        /// <summary>
        /// Thêm sinh viên vào đầu danh sách
        /// </summary>
        public void AddFirst(Student student)
        {
            DoublyStudentNode newNode = new DoublyStudentNode(student);
            
            if (head == null)
            {
                head = tail = newNode;
            }
            else
            {
                newNode.Next = head;
                head.Previous = newNode;
                head = newNode;
            }
            count++;
        }

        /// <summary>
        /// Thêm sinh viên vào cuối danh sách
        /// </summary>
        public void AddLast(Student student)
        {
            DoublyStudentNode newNode = new DoublyStudentNode(student);
            
            if (tail == null)
            {
                head = tail = newNode;
            }
            else
            {
                tail.Next = newNode;
                newNode.Previous = tail;
                tail = newNode;
            }
            count++;
        }

        /// <summary>
        /// Xóa sinh viên theo mã sinh viên
        /// </summary>
        public bool Remove(string maSinhVien)
        {
            DoublyStudentNode current = head;
            
            while (current != null)
            {
                if (current.Data.MaSinhVien == maSinhVien)
                {
                    if (current.Previous != null)
                        current.Previous.Next = current.Next;
                    else
                        head = current.Next;

                    if (current.Next != null)
                        current.Next.Previous = current.Previous;
                    else
                        tail = current.Previous;

                    count--;
                    return true;
                }
                current = current.Next;
            }
            return false;
        }

        /// <summary>
        /// Tìm kiếm sinh viên theo mã sinh viên
        /// </summary>
        public Student Find(string maSinhVien)
        {
            DoublyStudentNode current = head;
            while (current != null)
            {
                if (current.Data.MaSinhVien == maSinhVien)
                    return current.Data;
                current = current.Next;
            }
            return null;
        }

        /// <summary>
        /// Lấy tất cả sinh viên (từ đầu đến cuối)
        /// </summary>
        public List<Student> GetAll()
        {
            List<Student> result = new List<Student>();
            DoublyStudentNode current = head;
            
            while (current != null)
            {
                result.Add(current.Data);
                current = current.Next;
            }
            return result;
        }

        /// <summary>
        /// Lấy tất cả sinh viên (từ cuối đến đầu)
        /// </summary>
        public List<Student> GetAllReverse()
        {
            List<Student> result = new List<Student>();
            DoublyStudentNode current = tail;
            
            while (current != null)
            {
                result.Add(current.Data);
                current = current.Previous;
            }
            return result;
        }

        /// <summary>
        /// Xóa tất cả sinh viên
        /// </summary>
        public void Clear()
        {
            head = tail = null;
            count = 0;
        }

        /// <summary>
        /// Kiểm tra danh sách có rỗng không
        /// </summary>
        public bool IsEmpty()
        {
            return head == null;
        }

        /// <summary>
        /// Tìm kiếm sinh viên theo tên
        /// </summary>
        public List<Student> FindByName(string hoTen)
        {
            List<Student> result = new List<Student>();
            DoublyStudentNode current = head;
            
            while (current != null)
            {
                if (current.Data.HoTen.ToLower().Contains(hoTen.ToLower()))
                    result.Add(current.Data);
                current = current.Next;
            }
            return result;
        }

        /// <summary>
        /// Sắp xếp danh sách theo điểm trung bình (giảm dần)
        /// </summary>
        public void SortByGPA()
        {
            if (head == null || head.Next == null) return;

            bool swapped;
            do
            {
                swapped = false;
                DoublyStudentNode current = head;
                
                while (current.Next != null)
                {
                    if (current.Data.DiemTrungBinh < current.Next.Data.DiemTrungBinh)
                    {
                        // Hoán đổi dữ liệu
                        Student temp = current.Data;
                        current.Data = current.Next.Data;
                        current.Next.Data = temp;
                        swapped = true;
                    }
                    current = current.Next;
                }
            } while (swapped);
        }
    }
}
