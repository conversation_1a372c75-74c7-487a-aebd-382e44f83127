using System;
using System.Collections.Generic;
using System.Linq;

namespace ShortestPathProject.Models
{
    public class Vertex
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public double X { get; set; } // T<PERSON><PERSON> độ X cho hiển thị
        public double Y { get; set; } // Tọa độ Y cho hiển thị
        
        public Vertex(int id, string name, double x = 0, double y = 0)
        {
            Id = id;
            Name = name;
            X = x;
            Y = y;
        }
        
        public override bool Equals(object obj)
        {
            return obj is Vertex vertex && Id == vertex.Id;
        }
        
        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }
        
        public override string ToString()
        {
            return $"Vertex {Id}: {Name}";
        }
    }

    public class Edge
    {
        public Vertex From { get; set; }
        public Vertex To { get; set; }
        public double Weight { get; set; }
        public bool IsDirected { get; set; }
        
        public Edge(Vertex from, Vertex to, double weight, bool isDirected = true)
        {
            From = from;
            To = to;
            Weight = weight;
            IsDirected = isDirected;
        }
        
        public Edge Reverse()
        {
            return new Edge(To, From, Weight, IsDirected);
        }
        
        public override string ToString()
        {
            string arrow = IsDirected ? "->" : "<->";
            return $"{From.Name} {arrow} {To.Name} (w: {Weight})";
        }
    }

    public class Graph
    {
        private Dictionary<Vertex, List<Edge>> adjacencyList;
        private List<Vertex> vertices;
        private List<Edge> edges;
        public bool IsDirected { get; private set; }
        
        public Graph(bool isDirected = true)
        {
            adjacencyList = new Dictionary<Vertex, List<Edge>>();
            vertices = new List<Vertex>();
            edges = new List<Edge>();
            IsDirected = isDirected;
        }
        
        // Thêm đỉnh
        public void AddVertex(Vertex vertex)
        {
            if (!vertices.Contains(vertex))
            {
                vertices.Add(vertex);
                adjacencyList[vertex] = new List<Edge>();
            }
        }
        
        // Thêm cạnh
        public void AddEdge(Vertex from, Vertex to, double weight)
        {
            AddVertex(from);
            AddVertex(to);
            
            Edge edge = new Edge(from, to, weight, IsDirected);
            edges.Add(edge);
            adjacencyList[from].Add(edge);
            
            // Nếu là đồ thị vô hướng, thêm cạnh ngược
            if (!IsDirected)
            {
                Edge reverseEdge = new Edge(to, from, weight, false);
                edges.Add(reverseEdge);
                adjacencyList[to].Add(reverseEdge);
            }
        }
        
        // Lấy danh sách đỉnh kề
        public List<Edge> GetAdjacentEdges(Vertex vertex)
        {
            return adjacencyList.ContainsKey(vertex) ? 
                   adjacencyList[vertex] : new List<Edge>();
        }
        
        // Properties
        public IReadOnlyList<Vertex> Vertices => vertices.AsReadOnly();
        public IReadOnlyList<Edge> Edges => edges.AsReadOnly();
        public int VertexCount => vertices.Count;
        public int EdgeCount => edges.Count;
        
        // Tạo ma trận kề
        public double[,] ToAdjacencyMatrix()
        {
            int n = vertices.Count;
            double[,] matrix = new double[n, n];
            
            // Khởi tạo ma trận với giá trị vô cực
            for (int i = 0; i < n; i++)
            {
                for (int j = 0; j < n; j++)
                {
                    matrix[i, j] = i == j ? 0 : double.PositiveInfinity;
                }
            }
            
            // Điền trọng số các cạnh
            foreach (var edge in edges)
            {
                int fromIndex = vertices.IndexOf(edge.From);
                int toIndex = vertices.IndexOf(edge.To);
                matrix[fromIndex, toIndex] = edge.Weight;
            }
            
            return matrix;
        }
        
        // Tạo đồ thị mẫu cho test
        public static Graph CreateSampleGraph()
        {
            Graph graph = new Graph(true);
            
            // Tạo các đỉnh
            var vertexA = new Vertex(0, "A", 100, 100);
            var vertexB = new Vertex(1, "B", 300, 100);
            var vertexC = new Vertex(2, "C", 100, 300);
            var vertexD = new Vertex(3, "D", 300, 300);
            var vertexE = new Vertex(4, "E", 500, 200);
            
            // Thêm các cạnh
            graph.AddEdge(vertexA, vertexB, 2);
            graph.AddEdge(vertexA, vertexC, 1);
            graph.AddEdge(vertexB, vertexD, 3);
            graph.AddEdge(vertexC, vertexD, 1);
            graph.AddEdge(vertexD, vertexE, 1);
            graph.AddEdge(vertexB, vertexE, 5);
            
            return graph;
        }

        // Tạo đồ thị có trọng số âm
        public static Graph CreateNegativeWeightGraph()
        {
            Graph graph = new Graph(true);
            
            var vertexA = new Vertex(0, "A", 100, 200);
            var vertexB = new Vertex(1, "B", 300, 100);
            var vertexC = new Vertex(2, "C", 300, 300);
            var vertexD = new Vertex(3, "D", 500, 200);
            
            graph.AddEdge(vertexA, vertexB, 1);
            graph.AddEdge(vertexA, vertexC, 2);
            graph.AddEdge(vertexB, vertexD, -3);
            graph.AddEdge(vertexC, vertexD, 2);
            
            return graph;
        }

        // Tạo đồ thị lớn cho test hiệu năng
        public static Graph CreateLargeGraph(int vertexCount, double edgeDensity, bool hasNegativeWeights = false)
        {
            Graph graph = new Graph(true);
            Random random = new Random(42); // Seed cố định để reproducible
            
            // Tạo các đỉnh
            for (int i = 0; i < vertexCount; i++)
            {
                double x = random.NextDouble() * 800;
                double y = random.NextDouble() * 600;
                graph.AddVertex(new Vertex(i, $"V{i}", x, y));
            }
            
            // Tạo các cạnh
            int maxEdges = (int)(vertexCount * (vertexCount - 1) * edgeDensity);
            int edgeCount = 0;
            
            while (edgeCount < maxEdges)
            {
                int fromId = random.Next(vertexCount);
                int toId = random.Next(vertexCount);
                
                if (fromId != toId)
                {
                    var from = graph.Vertices[fromId];
                    var to = graph.Vertices[toId];
                    
                    // Kiểm tra xem cạnh đã tồn tại chưa
                    bool edgeExists = graph.GetAdjacentEdges(from).Any(e => e.To.Equals(to));
                    
                    if (!edgeExists)
                    {
                        double weight;
                        if (hasNegativeWeights && random.NextDouble() < 0.1) // 10% cạnh âm
                        {
                            weight = random.Next(-50, 0);
                        }
                        else
                        {
                            weight = random.Next(1, 100);
                        }
                        
                        graph.AddEdge(from, to, weight);
                        edgeCount++;
                    }
                }
            }
            
            return graph;
        }
    }
}
