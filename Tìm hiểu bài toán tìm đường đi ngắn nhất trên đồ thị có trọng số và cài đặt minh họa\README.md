# Tìm hiểu bài toán tìm đường đi ngắn nhất trên đồ thị có trọng số và cài đặt minh họa

## Mô tả dự án
Dự án nghiên cứu và cài đặt minh họa các giải thuật tìm đường đi ngắn nhất trên đồ thị có trọng số.

## Mục tiêu
- Tìm hiểu các giải thuật tìm đường đi ngắn nhất (Dijkstra, Bellman-Ford, Floyd-Warshall)
- Cài đặt minh họa một giải thuật bằng Python/C++
- Phân tích và so sánh các giải thuật

## Cấu trúc thư mục
```
├── docs/                   # Tài liệu báo cáo
├── src/                    # Mã nguồn cài đặt
├── examples/               # Ví dụ minh họa
├── tests/                  # Test cases
└── references/             # Tài liệu tham khảo
```

## Công cụ sử dụng
- Python (ch<PERSON>h)
- C++ (t<PERSON><PERSON> ch<PERSON>)
- <PERSON><PERSON><PERSON><PERSON><PERSON> (để vẽ đồ thị)

## Hướng dẫn sử dụng
1. Cài đặt dependencies: `pip install -r requirements.txt`
2. Chạy ví dụ: `python src/main.py`
3. Xem báo cáo trong thư mục `docs/`
