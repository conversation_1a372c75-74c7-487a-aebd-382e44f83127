/**
 * Cài đặt các giải thuật tìm đường đi ngắn nhất
 * Author: Student
 * Date: 2025
 */

// Lớp Graph cho <PERSON>
class DijkstraGraph {
    constructor(vertices) {
        this.V = vertices;
        this.graph = Array(vertices).fill().map(() => []);
        this.positions = {}; // Vị trí đỉnh để vẽ
    }

    addEdge(u, v, weight) {
        this.graph[u].push({ vertex: v, weight: weight });
    }

    setPosition(vertex, x, y) {
        this.positions[vertex] = { x, y };
    }

    dijkstra(src) {
        const dist = Array(this.V).fill(Infinity);
        const parent = Array(this.V).fill(-1);
        const visited = Array(this.V).fill(false);
        const steps = [];

        dist[src] = 0;
        
        // Priority queue simulation với array
        const pq = [{ vertex: src, distance: 0 }];
        
        steps.push({
            type: 'init',
            message: `Khởi tạo: dist[${src}] = 0, các đỉnh khác = ∞`,
            distances: [...dist],
            current: src
        });

        while (pq.length > 0) {
            // Tìm đỉnh có khoảng cách nhỏ nhất
            pq.sort((a, b) => a.distance - b.distance);
            const { vertex: u } = pq.shift();

            if (visited[u]) continue;
            visited[u] = true;

            steps.push({
                type: 'visit',
                message: `Xử lý đỉnh ${u} với khoảng cách ${dist[u]}`,
                distances: [...dist],
                current: u,
                visited: [...visited]
            });

            // Duyệt các đỉnh kề
            for (const edge of this.graph[u]) {
                const v = edge.vertex;
                const weight = edge.weight;
                const newDist = dist[u] + weight;

                if (newDist < dist[v]) {
                    dist[v] = newDist;
                    parent[v] = u;
                    pq.push({ vertex: v, distance: newDist });

                    steps.push({
                        type: 'relax',
                        message: `Thư giãn cạnh (${u}, ${v}): dist[${v}] = ${newDist}`,
                        distances: [...dist],
                        current: u,
                        relaxed: v,
                        edge: { from: u, to: v, weight }
                    });
                }
            }
        }

        return { distances: dist, parents: parent, steps };
    }

    getPath(parents, target) {
        const path = [];
        let current = target;
        
        while (current !== -1) {
            path.unshift(current);
            current = parents[current];
        }
        
        return path;
    }
}

// Lớp Graph cho Bellman-Ford
class BellmanFordGraph {
    constructor(vertices) {
        this.V = vertices;
        this.edges = [];
        this.positions = {};
    }

    addEdge(u, v, weight) {
        this.edges.push({ src: u, dest: v, weight: weight });
    }

    setPosition(vertex, x, y) {
        this.positions[vertex] = { x, y };
    }

    bellmanFord(src) {
        const dist = Array(this.V).fill(Infinity);
        const parent = Array(this.V).fill(-1);
        const iterations = [];

        dist[src] = 0;

        iterations.push({
            iteration: 0,
            message: `Khởi tạo: dist[${src}] = 0, các đỉnh khác = ∞`,
            distances: [...dist]
        });

        // Thực hiện V-1 lần lặp
        for (let i = 1; i < this.V; i++) {
            let updated = false;
            const iterationUpdates = [];

            for (const edge of this.edges) {
                const { src: u, dest: v, weight } = edge;
                
                if (dist[u] !== Infinity && dist[u] + weight < dist[v]) {
                    dist[v] = dist[u] + weight;
                    parent[v] = u;
                    updated = true;
                    
                    iterationUpdates.push({
                        edge: { from: u, to: v, weight },
                        oldDist: dist[v] - (dist[u] + weight - dist[v]),
                        newDist: dist[v]
                    });
                }
            }

            iterations.push({
                iteration: i,
                message: updated ? 
                    `Lần lặp ${i}: Cập nhật ${iterationUpdates.length} đỉnh` :
                    `Lần lặp ${i}: Không có cập nhật nào`,
                distances: [...dist],
                updates: iterationUpdates,
                hasUpdates: updated
            });

            if (!updated) break; // Early termination
        }

        // Kiểm tra chu trình âm
        let hasNegativeCycle = false;
        const negativeEdges = [];

        for (const edge of this.edges) {
            const { src: u, dest: v, weight } = edge;
            
            if (dist[u] !== Infinity && dist[u] + weight < dist[v]) {
                hasNegativeCycle = true;
                negativeEdges.push(edge);
            }
        }

        return {
            distances: dist,
            parents: parent,
            iterations,
            hasNegativeCycle,
            negativeEdges
        };
    }

    getPath(parents, target) {
        const path = [];
        let current = target;
        
        while (current !== -1) {
            path.unshift(current);
            current = parents[current];
        }
        
        return path;
    }
}

// Lớp Graph cho Floyd-Warshall
class FloydWarshallGraph {
    constructor(vertices) {
        this.V = vertices;
        this.dist = Array(vertices).fill().map(() => Array(vertices).fill(Infinity));
        this.next = Array(vertices).fill().map(() => Array(vertices).fill(-1));
        
        // Khởi tạo khoảng cách từ đỉnh đến chính nó = 0
        for (let i = 0; i < vertices; i++) {
            this.dist[i][i] = 0;
        }
    }

    addEdge(u, v, weight) {
        this.dist[u][v] = weight;
        this.next[u][v] = v;
    }

    floydWarshall() {
        const steps = [];
        const matrices = [];
        
        // Ma trận ban đầu
        matrices.push({
            k: -1,
            matrix: this.dist.map(row => [...row]),
            message: "Ma trận khoảng cách ban đầu"
        });

        // Thuật toán Floyd-Warshall
        for (let k = 0; k < this.V; k++) {
            const updates = [];
            
            for (let i = 0; i < this.V; i++) {
                for (let j = 0; j < this.V; j++) {
                    const oldDist = this.dist[i][j];
                    const newDist = this.dist[i][k] + this.dist[k][j];
                    
                    if (this.dist[i][k] !== Infinity && 
                        this.dist[k][j] !== Infinity && 
                        newDist < this.dist[i][j]) {
                        
                        this.dist[i][j] = newDist;
                        this.next[i][j] = this.next[i][k];
                        
                        updates.push({
                            i, j, k,
                            oldDist: oldDist === Infinity ? '∞' : oldDist,
                            newDist,
                            calculation: `${this.dist[i][k]} + ${this.dist[k][j]} = ${newDist}`
                        });
                    }
                }
            }

            matrices.push({
                k,
                matrix: this.dist.map(row => [...row]),
                message: `Sau khi xét đỉnh trung gian ${k}`,
                updates
            });

            steps.push({
                k,
                updates,
                message: updates.length > 0 ? 
                    `Đỉnh trung gian ${k}: Cập nhật ${updates.length} ô` :
                    `Đỉnh trung gian ${k}: Không có cập nhật`
            });
        }

        // Kiểm tra chu trình âm
        const hasNegativeCycle = this.hasNegativeCycle();

        return {
            finalMatrix: this.dist.map(row => [...row]),
            matrices,
            steps,
            hasNegativeCycle
        };
    }

    hasNegativeCycle() {
        for (let i = 0; i < this.V; i++) {
            if (this.dist[i][i] < 0) {
                return true;
            }
        }
        return false;
    }

    getPath(i, j) {
        if (this.next[i][j] === -1) {
            return [];
        }

        const path = [i];
        let current = i;
        
        while (current !== j) {
            current = this.next[current][j];
            path.push(current);
        }
        
        return path;
    }

    getDistance(i, j) {
        return this.dist[i][j] === Infinity ? '∞' : this.dist[i][j];
    }
}

// Hàm tạo đồ thị mẫu cho Dijkstra
function createSampleDijkstraGraph() {
    const graph = new DijkstraGraph(6);
    
    // Thêm các cạnh
    graph.addEdge(0, 1, 4);
    graph.addEdge(0, 2, 2);
    graph.addEdge(1, 2, 1);
    graph.addEdge(1, 3, 5);
    graph.addEdge(2, 3, 8);
    graph.addEdge(2, 4, 10);
    graph.addEdge(3, 4, 2);
    graph.addEdge(3, 5, 6);
    graph.addEdge(4, 5, 3);

    // Đặt vị trí các đỉnh để vẽ
    graph.setPosition(0, 100, 100);
    graph.setPosition(1, 300, 50);
    graph.setPosition(2, 200, 200);
    graph.setPosition(3, 400, 150);
    graph.setPosition(4, 350, 300);
    graph.setPosition(5, 500, 250);

    return graph;
}

// Hàm tạo đồ thị mẫu cho Bellman-Ford
function createSampleBellmanFordGraph() {
    const graph = new BellmanFordGraph(5);
    
    // Thêm các cạnh (có trọng số âm)
    graph.addEdge(0, 1, -1);
    graph.addEdge(0, 2, 4);
    graph.addEdge(1, 2, 3);
    graph.addEdge(1, 3, 2);
    graph.addEdge(1, 4, 2);
    graph.addEdge(3, 2, 5);
    graph.addEdge(3, 1, 1);
    graph.addEdge(4, 3, -3);

    // Đặt vị trí các đỉnh
    graph.setPosition(0, 100, 150);
    graph.setPosition(1, 250, 100);
    graph.setPosition(2, 250, 250);
    graph.setPosition(3, 400, 150);
    graph.setPosition(4, 350, 300);

    return graph;
}

// Hàm tạo đồ thị có chu trình âm
function createNegativeCycleGraph() {
    const graph = new BellmanFordGraph(5);
    
    // Thêm các cạnh tạo chu trình âm
    graph.addEdge(0, 1, 1);
    graph.addEdge(1, 2, -3);
    graph.addEdge(2, 3, 2);
    graph.addEdge(3, 1, -1); // Tạo chu trình âm: 1 -> 2 -> 3 -> 1
    graph.addEdge(0, 4, 5);

    // Đặt vị trí các đỉnh
    graph.setPosition(0, 100, 200);
    graph.setPosition(1, 250, 100);
    graph.setPosition(2, 400, 100);
    graph.setPosition(3, 400, 250);
    graph.setPosition(4, 250, 300);

    return graph;
}

// Hàm tạo đồ thị mẫu cho Floyd-Warshall
function createSampleFloydWarshallGraph() {
    const graph = new FloydWarshallGraph(4);
    
    // Thêm các cạnh
    graph.addEdge(0, 1, 5);
    graph.addEdge(0, 3, 10);
    graph.addEdge(1, 2, 3);
    graph.addEdge(2, 3, 1);

    return graph;
}

// Hàm tiện ích để format số
function formatNumber(num) {
    if (num === Infinity) return '∞';
    if (num === -Infinity) return '-∞';
    return num.toString();
}

// Hàm tiện ích để tạo delay
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Hàm so sánh hiệu suất các giải thuật
function compareAlgorithms() {
    const results = {
        dijkstra: {},
        bellmanFord: {},
        floydWarshall: {}
    };

    // Test với đồ thị kích thước khác nhau
    const sizes = [10, 50, 100, 200, 500];

    sizes.forEach(size => {
        // Tạo đồ thị ngẫu nhiên
        const graph = generateRandomGraph(size, size * 2);

        // Test Dijkstra
        const dijkstraStart = performance.now();
        const dijkstraResult = graph.dijkstra(0);
        const dijkstraTime = performance.now() - dijkstraStart;

        results.dijkstra[size] = {
            time: dijkstraTime,
            memory: estimateMemoryUsage(size, size * 2, 'dijkstra')
        };

        // Test Bellman-Ford
        const bellmanGraph = convertToBellmanFord(graph);
        const bellmanStart = performance.now();
        const bellmanResult = bellmanGraph.bellmanFord(0);
        const bellmanTime = performance.now() - bellmanStart;

        results.bellmanFord[size] = {
            time: bellmanTime,
            memory: estimateMemoryUsage(size, size * 2, 'bellman')
        };

        // Test Floyd-Warshall (chỉ với đồ thị nhỏ)
        if (size <= 100) {
            const floydGraph = convertToFloydWarshall(graph);
            const floydStart = performance.now();
            const floydResult = floydGraph.floydWarshall();
            const floydTime = performance.now() - floydStart;

            results.floydWarshall[size] = {
                time: floydTime,
                memory: estimateMemoryUsage(size, size * 2, 'floyd')
            };
        }
    });

    return results;
}

// Hàm tạo đồ thị ngẫu nhiên
function generateRandomGraph(vertices, edges) {
    const graph = new DijkstraGraph(vertices);
    const addedEdges = new Set();

    for (let i = 0; i < edges; i++) {
        let u, v;
        do {
            u = Math.floor(Math.random() * vertices);
            v = Math.floor(Math.random() * vertices);
        } while (u === v || addedEdges.has(`${u}-${v}`));

        const weight = Math.floor(Math.random() * 20) + 1;
        graph.addEdge(u, v, weight);
        addedEdges.add(`${u}-${v}`);
    }

    return graph;
}

// Hàm ước tính sử dụng bộ nhớ
function estimateMemoryUsage(vertices, edges, algorithm) {
    switch (algorithm) {
        case 'dijkstra':
            return vertices * 4 + edges * 2; // Ước tính KB
        case 'bellman':
            return vertices * 4 + edges * 3;
        case 'floyd':
            return vertices * vertices * 4;
        default:
            return 0;
    }
}

// Hàm chuyển đổi đồ thị
function convertToBellmanFord(dijkstraGraph) {
    const graph = new BellmanFordGraph(dijkstraGraph.V);

    for (let u = 0; u < dijkstraGraph.V; u++) {
        for (const edge of dijkstraGraph.graph[u]) {
            graph.addEdge(u, edge.vertex, edge.weight);
        }
    }

    return graph;
}

function convertToFloydWarshall(dijkstraGraph) {
    const graph = new FloydWarshallGraph(dijkstraGraph.V);

    for (let u = 0; u < dijkstraGraph.V; u++) {
        for (const edge of dijkstraGraph.graph[u]) {
            graph.addEdge(u, edge.vertex, edge.weight);
        }
    }

    return graph;
}

// Export các class và hàm để sử dụng
window.DijkstraGraph = DijkstraGraph;
window.BellmanFordGraph = BellmanFordGraph;
window.FloydWarshallGraph = FloydWarshallGraph;
window.createSampleDijkstraGraph = createSampleDijkstraGraph;
window.createSampleBellmanFordGraph = createSampleBellmanFordGraph;
window.createNegativeCycleGraph = createNegativeCycleGraph;
window.createSampleFloydWarshallGraph = createSampleFloydWarshallGraph;
window.formatNumber = formatNumber;
window.delay = delay;
window.compareAlgorithms = compareAlgorithms;
