<?php
/**
 * @package     Product Condition Module
 * @subpackage  mod_product_condition
 * @copyright   Copyright (C) 2025 Electronics Shop. All rights reserved.
 * @license     GNU General Public License version 2 or later
 */

defined('_JEXEC') or die;

use <PERSON><PERSON><PERSON>\CMS\Factory;
use Jo<PERSON><PERSON>\CMS\Helper\ModuleHelper;
use <PERSON><PERSON><PERSON>\CMS\HTML\HTMLHelper;
use <PERSON><PERSON><PERSON>\CMS\Language\Text;

// Include the helper file
require_once dirname(__FILE__) . '/helper.php';

// Get module parameters
$displayMode = $params->get('display_mode', 'filter');
$showPercentage = $params->get('show_percentage', 1);
$showDescription = $params->get('show_description', 1);
$showColorIndicator = $params->get('show_color_indicator', 1);
$enableAjaxFilter = $params->get('enable_ajax_filter', 1);
$moduleClassSfx = htmlspecialchars($params->get('moduleclass_sfx', ''), ENT_COMPAT, 'UTF-8');

// Get helper instance
$helper = new ModProductConditionHelper($params);

// Get product conditions
$conditions = $helper->getProductConditions();

// Get current condition filter (if any)
$app = Factory::getApplication();
$currentCondition = $app->input->getInt('condition_id', 0);

// Load CSS and JS
$wa = Factory::getApplication()->getDocument()->getWebAssetManager();
$wa->addInlineStyle('
.product-condition-module {
    margin-bottom: 1rem;
}

.condition-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.condition-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
    cursor: pointer;
}

.condition-item:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
    text-decoration: none;
}

.condition-item.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.condition-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
    border: 1px solid rgba(0,0,0,0.1);
}

.condition-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: white;
    margin: 0.25rem;
}

.condition-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.condition-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
}

.condition-list li:last-child {
    border-bottom: none;
}

.condition-percentage {
    font-weight: 600;
    margin-left: 0.5rem;
}

.condition-description {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

@media (max-width: 768px) {
    .condition-filter {
        flex-direction: column;
    }
    
    .condition-item {
        justify-content: center;
        text-align: center;
    }
}
');

if ($enableAjaxFilter) {
    $wa->addInlineScript('
document.addEventListener("DOMContentLoaded", function() {
    const conditionFilters = document.querySelectorAll(".condition-filter-link");
    
    conditionFilters.forEach(function(filter) {
        filter.addEventListener("click", function(e) {
            e.preventDefault();
            
            const conditionId = this.dataset.conditionId;
            const currentUrl = new URL(window.location);
            
            if (conditionId === "0") {
                currentUrl.searchParams.delete("condition_id");
            } else {
                currentUrl.searchParams.set("condition_id", conditionId);
            }
            
            // Update URL without page reload
            window.history.pushState({}, "", currentUrl);
            
            // Update active state
            document.querySelectorAll(".condition-item").forEach(function(item) {
                item.classList.remove("active");
            });
            this.classList.add("active");
            
            // Trigger product filter update (if VirtueMart supports it)
            if (typeof updateProductFilter === "function") {
                updateProductFilter();
            } else {
                // Fallback: reload page
                window.location.href = currentUrl;
            }
        });
    });
});
    ');
}

// Include the template
require ModuleHelper::getLayoutPath('mod_product_condition', $params->get('layout', 'default'));
?>
