/* Reset và Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem 0;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.header h1 i {
    color: #667eea;
    margin-right: 0.5rem;
}

.header p {
    font-size: 1.1rem;
    color: #718096;
    font-weight: 400;
}

/* Navigation */
.nav {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.nav-tab {
    background: transparent;
    border: 2px solid #e2e8f0;
    color: #4a5568;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-tab:hover {
    background: #f7fafc;
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-2px);
}

.nav-tab.active {
    background: #667eea;
    border-color: #667eea;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

/* Main Content */
.main {
    padding: 2rem 0;
    min-height: calc(100vh - 200px);
}

.tab-content {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Algorithm Section */
.algorithm-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.section-header {
    text-align: center;
    margin-bottom: 2rem;
}

.section-header h2 {
    font-size: 2rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.section-header h2 i {
    color: #667eea;
    margin-right: 0.5rem;
}

.section-header p {
    color: #718096;
    font-size: 1.1rem;
}

/* Demo Container */
.demo-container {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 2rem;
    margin-bottom: 2rem;
}

@media (max-width: 1200px) {
    .demo-container {
        grid-template-columns: 1fr;
    }
}

/* Graph Panel */
.graph-panel {
    background: #f8fafc;
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.panel-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
}

.controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
    transform: translateY(-2px);
}

.btn-warning {
    background: #ed8936;
    color: white;
}

.btn-warning:hover {
    background: #dd6b20;
    transform: translateY(-2px);
}

.btn-info {
    background: #4299e1;
    color: white;
}

.btn-info:hover {
    background: #3182ce;
    transform: translateY(-2px);
}

.btn-success {
    background: #48bb78;
    color: white;
}

.btn-success:hover {
    background: #38a169;
    transform: translateY(-2px);
}

/* Select */
.source-select, .path-select {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #4a5568;
    font-weight: 500;
}

/* Canvas */
canvas {
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    background: white;
    width: 100%;
    height: auto;
    max-width: 600px;
}

/* Info Panel */
.info-panel {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.algorithm-info, .results-panel, .steps-panel, .iterations-panel, .progress-panel, .path-finder {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.algorithm-info h3, .results-panel h3, .steps-panel h3, .iterations-panel h3, .progress-panel h3, .path-finder h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
}

/* Info Grid */
.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: #f8fafc;
    border-radius: 8px;
}

.info-item .label {
    font-weight: 500;
    color: #4a5568;
}

.info-item .value {
    font-weight: 600;
    color: #2d3748;
    font-family: 'Courier New', monospace;
}

/* Results Table */
.results-table {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e2e8f0;
}

.table-header {
    display: grid;
    grid-template-columns: 1fr 1fr 2fr;
    background: #667eea;
    color: white;
    font-weight: 600;
    padding: 0.75rem;
}

.table-body {
    max-height: 200px;
    overflow-y: auto;
}

.table-row {
    display: grid;
    grid-template-columns: 1fr 1fr 2fr;
    padding: 0.75rem;
    border-bottom: 1px solid #e2e8f0;
    transition: background-color 0.2s ease;
}

.table-row:hover {
    background: #f8fafc;
}

.table-row:last-child {
    border-bottom: none;
}

/* Steps List */
.steps-list, .iterations-list {
    max-height: 250px;
    overflow-y: auto;
}

.step-item, .iteration-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    font-size: 0.9rem;
    line-height: 1.4;
}

.step-item.active, .iteration-item.active {
    background: #e6fffa;
    border-left-color: #38b2ac;
}

/* Status Indicator */
.status-indicator {
    padding: 0.75rem;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    margin-bottom: 1rem;
}

.status-indicator.success {
    background: #c6f6d5;
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.status-indicator.error {
    background: #fed7d7;
    color: #742a2a;
    border: 1px solid #fc8181;
}

.status-indicator.warning {
    background: #fefcbf;
    color: #744210;
    border: 1px solid #f6e05e;
}

/* Matrix Container */
.matrix-container {
    display: flex;
    justify-content: center;
    padding: 1rem;
}

.distance-matrix {
    display: grid;
    gap: 2px;
    background: #e2e8f0;
    border-radius: 8px;
    padding: 2px;
}

.matrix-cell {
    background: white;
    padding: 0.75rem;
    text-align: center;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    min-width: 60px;
    transition: all 0.3s ease;
}

.matrix-cell.header {
    background: #667eea;
    color: white;
}

.matrix-cell.infinity {
    color: #a0aec0;
}

.matrix-cell.updated {
    background: #bee3f8;
    color: #2b6cb0;
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.9rem;
    color: #4a5568;
    text-align: center;
}

.current-step {
    font-weight: 600;
    color: #2d3748;
    margin-top: 0.5rem;
}

/* Path Controls */
.path-controls {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.path-result {
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

/* Theory Section */
.theory-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.theory-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1.5rem;
}

.theory-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

@media (max-width: 768px) {
    .theory-content {
        grid-template-columns: 1fr;
    }
}

.theory-item {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.theory-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
}

.theory-item p {
    color: #4a5568;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.theory-item ul {
    list-style: none;
    padding-left: 0;
}

.theory-item li {
    color: #4a5568;
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
}

.theory-item li::before {
    content: "▶";
    color: #667eea;
    position: absolute;
    left: 0;
    top: 0;
}

.theory-item li strong {
    color: #2d3748;
}

.formula {
    background: #2d3748;
    color: #e2e8f0;
    padding: 1rem;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    text-align: center;
    margin: 1rem 0;
}

/* Comparison Styles */
.comparison-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.comparison-table-container {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.comparison-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.comparison-table th,
.comparison-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

.comparison-table th {
    background: #667eea;
    color: white;
    font-weight: 600;
}

.comparison-table td.good {
    background: #c6f6d5;
    color: #22543d;
    font-weight: 600;
}

.comparison-table td.average {
    background: #fefcbf;
    color: #744210;
    font-weight: 600;
}

.comparison-table td.poor {
    background: #fed7d7;
    color: #742a2a;
    font-weight: 600;
}

/* Performance Charts */
.performance-charts {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.charts-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 1rem;
}

@media (max-width: 768px) {
    .charts-container {
        grid-template-columns: 1fr;
    }
}

.chart-item {
    text-align: center;
}

.chart-item h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
}

/* Recommendation Panel */
.recommendation-panel {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.recommendations {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.recommendation-item {
    background: #f8fafc;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
}

.recommendation-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.recommendation-header i {
    color: #667eea;
    font-size: 1.2rem;
}

.recommendation-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
}

.recommendation-item ul {
    list-style: none;
    padding-left: 0;
}

.recommendation-item li {
    color: #4a5568;
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
}

.recommendation-item li::before {
    content: "✓";
    color: #48bb78;
    font-weight: bold;
    position: absolute;
    left: 0;
    top: 0;
}

/* Footer */
.footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem 0;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: 2rem;
}

.footer p {
    color: #718096;
    margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header h1 {
        font-size: 2rem;
    }
    
    .nav-tabs {
        flex-direction: column;
        align-items: center;
    }
    
    .demo-container {
        grid-template-columns: 1fr;
    }
    
    .panel-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .controls {
        justify-content: center;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .table-header,
    .table-row {
        grid-template-columns: 1fr 1fr;
        font-size: 0.9rem;
    }
    
    .table-header span:last-child,
    .table-row span:last-child {
        display: none;
    }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
