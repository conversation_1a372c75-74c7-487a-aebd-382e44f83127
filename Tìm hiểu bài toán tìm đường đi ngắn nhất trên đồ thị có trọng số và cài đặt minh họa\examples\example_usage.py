"""
<PERSON><PERSON> dụ sử dụng các giải thuật tìm đường đi ngắn nhất
Author: Student
Date: 2025
"""

import sys
import os

# Thêm thư mục src vào path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from dijkstra import Graph as DijkstraGraph
from bellman_ford import Graph as BellmanFordGraph
from floyd_warshall import Graph as FloydWarshallGraph

def example_1_city_navigation():
    """
    Ví dụ 1: Hệ thống định tuyến trong thành phố
    Mô phỏng tìm đường đi ngắn nhất giữa các địa điểm trong thành phố
    """
    print("=" * 60)
    print("VÍ DỤ 1: HỆ THỐNG ĐỊNH TUYẾN TRONG THÀNH PHỐ")
    print("=" * 60)
    
    # Định nghĩa các địa điểm
    locations = {
        0: "Bệnh viện",
        1: "Trường học", 
        2: "<PERSON><PERSON><PERSON> thị",
        3: "Công viên",
        4: "Nhà ga",
        5: "Sân bay"
    }
    
    print("Các địa điểm:")
    for idx, name in locations.items():
        print(f"  {idx}: {name}")
    
    # Tạo đồ thị đường đi (khoảng cách tính bằng km)
    city_graph = DijkstraGraph(6)
    
    # Thêm các tuyến đường và khoảng cách
    routes = [
        (0, 1, 3),   # Bệnh viện -> Trường học: 3km
        (0, 2, 5),   # Bệnh viện -> Siêu thị: 5km
        (1, 2, 2),   # Trường học -> Siêu thị: 2km
        (1, 3, 4),   # Trường học -> Công viên: 4km
        (2, 3, 1),   # Siêu thị -> Công viên: 1km
        (2, 4, 6),   # Siêu thị -> Nhà ga: 6km
        (3, 4, 3),   # Công viên -> Nhà ga: 3km
        (3, 5, 8),   # Công viên -> Sân bay: 8km
        (4, 5, 2),   # Nhà ga -> Sân bay: 2km
    ]
    
    for u, v, distance in routes:
        city_graph.add_edge(u, v, distance)
        # Thêm đường ngược lại (đường hai chiều)
        city_graph.add_edge(v, u, distance)
    
    print("\nCác tuyến đường:")
    for u, v, distance in routes:
        print(f"  {locations[u]} ↔ {locations[v]}: {distance}km")
    
    # Tìm đường đi ngắn nhất từ Bệnh viện đến tất cả địa điểm khác
    source = 0  # Bệnh viện
    print(f"\nTìm đường đi ngắn nhất từ {locations[source]}:")
    
    distances, parents = city_graph.dijkstra(source)
    
    print("\nKết quả:")
    for i in range(len(locations)):
        if i != source:
            path = city_graph.get_path(parents, i)
            path_names = [locations[j] for j in path]
            print(f"  Đến {locations[i]}: {distances[i]}km")
            print(f"    Đường đi: {' → '.join(path_names)}")

def example_2_network_routing():
    """
    Ví dụ 2: Định tuyến mạng với khả năng xử lý trọng số âm
    Mô phỏng định tuyến trong mạng máy tính với chi phí âm (ưu đãi)
    """
    print("\n" + "=" * 60)
    print("VÍ DỤ 2: ĐỊNH TUYẾN MẠNG VỚI CHI PHÍ ÂM")
    print("=" * 60)
    
    # Định nghĩa các node mạng
    nodes = {
        0: "Server A",
        1: "Router B", 
        2: "Router C",
        3: "Router D",
        4: "Client E"
    }
    
    print("Các node mạng:")
    for idx, name in nodes.items():
        print(f"  {idx}: {name}")
    
    # Tạo đồ thị mạng (chi phí có thể âm do ưu đãi)
    network_graph = BellmanFordGraph(5)
    
    # Thêm các kết nối và chi phí
    connections = [
        (0, 1, 2),   # Server A -> Router B: chi phí 2
        (0, 2, 5),   # Server A -> Router C: chi phí 5
        (1, 2, -1),  # Router B -> Router C: ưu đãi -1
        (1, 3, 3),   # Router B -> Router D: chi phí 3
        (2, 3, 1),   # Router C -> Router D: chi phí 1
        (2, 4, 4),   # Router C -> Client E: chi phí 4
        (3, 4, -2),  # Router D -> Client E: ưu đãi -2
    ]
    
    for u, v, cost in connections:
        network_graph.add_edge(u, v, cost)
    
    print("\nCác kết nối mạng:")
    for u, v, cost in connections:
        cost_str = f"ưu đãi {abs(cost)}" if cost < 0 else f"chi phí {cost}"
        print(f"  {nodes[u]} → {nodes[v]}: {cost_str}")
    
    # Tìm đường đi với chi phí thấp nhất từ Server A
    source = 0
    print(f"\nTìm đường đi chi phí thấp nhất từ {nodes[source]}:")
    
    distances, parents, has_negative_cycle = network_graph.bellman_ford(source)
    
    if has_negative_cycle:
        print("⚠️  Phát hiện chu trình âm trong mạng!")
    else:
        print("\nKết quả:")
        for i in range(len(nodes)):
            if i != source:
                path = network_graph.get_path(parents, i)
                path_names = [nodes[j] for j in path]
                print(f"  Đến {nodes[i]}: chi phí {distances[i]}")
                print(f"    Đường đi: {' → '.join(path_names)}")

def example_3_logistics_optimization():
    """
    Ví dụ 3: Tối ưu hóa logistics - tìm chi phí vận chuyển giữa tất cả các kho
    """
    print("\n" + "=" * 60)
    print("VÍ DỤ 3: TỐI ƯU HÓA LOGISTICS")
    print("=" * 60)
    
    # Định nghĩa các kho hàng
    warehouses = {
        0: "Kho Hà Nội",
        1: "Kho Hải Phòng",
        2: "Kho Đà Nẵng", 
        3: "Kho TP.HCM"
    }
    
    print("Các kho hàng:")
    for idx, name in warehouses.items():
        print(f"  {idx}: {name}")
    
    # Tạo đồ thị chi phí vận chuyển
    logistics_graph = FloydWarshallGraph(4)
    
    # Thêm chi phí vận chuyển (triệu VNĐ)
    shipping_costs = [
        (0, 1, 2),   # Hà Nội -> Hải Phòng: 2 triệu
        (0, 2, 8),   # Hà Nội -> Đà Nẵng: 8 triệu
        (0, 3, 15),  # Hà Nội -> TP.HCM: 15 triệu
        (1, 2, 10),  # Hải Phòng -> Đà Nẵng: 10 triệu
        (1, 3, 12),  # Hải Phòng -> TP.HCM: 12 triệu
        (2, 3, 5),   # Đà Nẵng -> TP.HCM: 5 triệu
    ]
    
    for u, v, cost in shipping_costs:
        logistics_graph.add_edge(u, v, cost)
        # Thêm chiều ngược lại với cùng chi phí
        logistics_graph.add_edge(v, u, cost)
    
    print("\nChi phí vận chuyển trực tiếp:")
    for u, v, cost in shipping_costs:
        print(f"  {warehouses[u]} ↔ {warehouses[v]}: {cost} triệu VNĐ")
    
    # Tìm chi phí vận chuyển tối ưu giữa tất cả các kho
    print("\nTìm chi phí vận chuyển tối ưu giữa tất cả các kho:")
    
    result = logistics_graph.floyd_warshall()
    
    print("\nBảng chi phí vận chuyển tối ưu (triệu VNĐ):")
    print("        ", end="")
    for j in range(4):
        print(f"{j:>8}", end="")
    print()
    
    for i in range(4):
        print(f"{i:>8}", end="")
        for j in range(4):
            if i == j:
                print(f"{'0':>8}", end="")
            else:
                print(f"{result[i][j]:>8}", end="")
        print()
    
    print("\nGiải thích:")
    for i in range(4):
        for j in range(i + 1, 4):
            print(f"  {warehouses[i]} → {warehouses[j]}: {result[i][j]} triệu VNĐ")

def example_4_game_pathfinding():
    """
    Ví dụ 4: Tìm đường trong game với địa hình khác nhau
    """
    print("\n" + "=" * 60)
    print("VÍ DỤ 4: TÌM ĐƯỜNG TRONG GAME")
    print("=" * 60)
    
    # Định nghĩa các vùng địa hình
    terrains = {
        0: "Thành phố (Start)",
        1: "Đồng cỏ",
        2: "Rừng rậm",
        3: "Núi đá",
        4: "Sa mạc",
        5: "Lâu đài (Goal)"
    }
    
    print("Bản đồ game:")
    for idx, name in terrains.items():
        print(f"  {idx}: {name}")
    
    # Tạo đồ thị với chi phí di chuyển khác nhau
    game_graph = DijkstraGraph(6)
    
    # Chi phí di chuyển (điểm năng lượng)
    movement_costs = [
        (0, 1, 1),   # Thành phố -> Đồng cỏ: 1 năng lượng
        (0, 2, 3),   # Thành phố -> Rừng rậm: 3 năng lượng
        (1, 2, 2),   # Đồng cỏ -> Rừng rậm: 2 năng lượng
        (1, 3, 4),   # Đồng cỏ -> Núi đá: 4 năng lượng
        (2, 3, 1),   # Rừng rậm -> Núi đá: 1 năng lượng
        (2, 4, 5),   # Rừng rậm -> Sa mạc: 5 năng lượng
        (3, 4, 2),   # Núi đá -> Sa mạc: 2 năng lượng
        (3, 5, 6),   # Núi đá -> Lâu đài: 6 năng lượng
        (4, 5, 3),   # Sa mạc -> Lâu đài: 3 năng lượng
    ]
    
    for u, v, cost in movement_costs:
        game_graph.add_edge(u, v, cost)
    
    print("\nChi phí di chuyển:")
    for u, v, cost in movement_costs:
        print(f"  {terrains[u]} → {terrains[v]}: {cost} năng lượng")
    
    # Tìm đường đi tối ưu từ thành phố đến lâu đài
    start = 0
    goal = 5
    
    print(f"\nTìm đường đi tối ưu từ {terrains[start]} đến {terrains[goal]}:")
    
    distances, parents = game_graph.dijkstra(start)
    
    if distances[goal] != sys.maxsize:
        path = game_graph.get_path(parents, goal)
        path_names = [terrains[i] for i in path]
        
        print(f"\nĐường đi tối ưu:")
        print(f"  Tổng chi phí: {distances[goal]} năng lượng")
        print(f"  Lộ trình: {' → '.join(path_names)}")
        
        print(f"\nChi tiết từng bước:")
        for i in range(len(path) - 1):
            current = path[i]
            next_step = path[i + 1]
            # Tìm chi phí của bước này
            step_cost = None
            for u, v, cost in movement_costs:
                if u == current and v == next_step:
                    step_cost = cost
                    break
            print(f"  Bước {i + 1}: {terrains[current]} → {terrains[next_step]} ({step_cost} năng lượng)")
    else:
        print("Không tìm thấy đường đi!")

def main():
    """Chạy tất cả các ví dụ"""
    print("CHƯƠNG TRÌNH VÍ DỤ SỬ DỤNG CÁC GIẢI THUẬT TÌM ĐƯỜNG ĐI NGẮN NHẤT")
    print("Sinh viên: [Tên sinh viên]")
    print("Lớp: [Tên lớp]")
    
    try:
        example_1_city_navigation()
        example_2_network_routing()
        example_3_logistics_optimization()
        example_4_game_pathfinding()
        
        print("\n" + "=" * 60)
        print("KẾT LUẬN")
        print("=" * 60)
        print("✅ Đã hoàn thành tất cả các ví dụ minh họa")
        print("📊 Các giải thuật đều hoạt động chính xác với các bài toán thực tế")
        print("🎯 Mỗi giải thuật có ưu điểm riêng phù hợp với từng loại bài toán")
        
    except Exception as e:
        print(f"❌ Lỗi khi chạy ví dụ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
