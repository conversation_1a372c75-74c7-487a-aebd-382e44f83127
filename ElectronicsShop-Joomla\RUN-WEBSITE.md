# 🚀 Hướng Dẫn Chạy Website Electronics Shop

## Bước 1: Cài đặt XAMPP

### Tải XAMPP
1. T<PERSON><PERSON> cập: https://www.apachefriends.org/download.html
2. Tải **XAMPP for Windows** (PHP 8.1 hoặc 8.2)
3. Chạy file installer vớ<PERSON> quyền **Administrator**

### Cài đặt XAMPP
1. Chọn components cần thiết:
   - ✅ Apache
   - ✅ MySQL  
   - ✅ PHP
   - ✅ phpMyAdmin
2. Cài đặt vào thư mục: `C:\xampp`
3. Hoàn tất cài đặt

### Khởi động Services
1. Mở **XAMPP Control Panel**
2. Click **Start** cho **Apache**
3. Click **Start** cho **MySQL**
4. Kiểm tra: Truy cập http://localhost (phải thấy trang XAMPP)

## Bước 2: Tạo Database

### Truy cập phpMyAdmin
1. Mở trình duyệt
2. <PERSON><PERSON><PERSON> cậ<PERSON>: http://localhost/phpmyadmin
3. <PERSON><PERSON><PERSON> nhập (username: `root`, password: để trống)

### Tạo Database
1. Click tab **Databases**
2. Tên database: `electronics_shop`
3. Collation: `utf8mb4_unicode_ci`
4. Click **Create**

## Bước 3: Tải và Cài đặt Joomla

### Tự động (Khuyến nghị)
1. Mở **PowerShell** với quyền **Administrator**
2. Chuyển đến thư mục dự án:
   ```powershell
   cd "d:\test\.NET SDK\ElectronicsShop-Joomla"
   ```
3. Chạy script:
   ```powershell
   .\download-joomla.ps1
   ```

### Thủ công (Nếu script lỗi)
1. Tải Joomla 4.x từ: https://www.joomla.org/download.html
2. Giải nén vào: `C:\xampp\htdocs\electronics-shop`
3. Truy cập: http://localhost/electronics-shop
4. Làm theo wizard cài đặt:
   - **Site Name**: `Electronics Shop - Thiết Bị Điện Tử Cũ`
   - **Admin Username**: `admin`
   - **Admin Password**: `admin123` (hoặc mật khẩu mạnh)
   - **Admin Email**: `<EMAIL>`
   - **Database Type**: `MySQLi`
   - **Host Name**: `localhost`
   - **Username**: `root`
   - **Password**: (để trống)
   - **Database Name**: `electronics_shop`

## Bước 4: Cài đặt VirtueMart

### Tải VirtueMart
1. Chạy script:
   ```powershell
   .\download-virtuemart.ps1
   ```
2. Hoặc tải thủ công từ: https://virtuemart.net/downloads

### Cài đặt Extensions
1. Đăng nhập Joomla Admin: http://localhost/electronics-shop/administrator
2. Vào **System > Install > Extensions**
3. Upload và cài đặt theo thứ tự:
   - `com_virtuemart_4.x.x.zip` (Core)
   - `plg_vmpayment_standard.zip` (Payment)
   - `plg_vmshipment_standard.zip` (Shipment)
   - `plg_vminvoice_standard.zip` (Invoice)
   - `virtuemart_vi-VN.zip` (Vietnamese)

## Bước 5: Import Database Design

### Import Schema
1. Mở phpMyAdmin: http://localhost/phpmyadmin
2. Chọn database `electronics_shop`
3. Click tab **Import**
4. Chọn file: `Database/database-design.sql`
5. Click **Go**

### Import Sample Data
1. Tiếp tục import file: `Sample-Data/sample-products.sql`
2. Click **Go**

## Bước 6: Cài đặt Template

### Tạo Template Package
1. Nén thư mục `Templates/electronics-shop` thành file ZIP
2. Đặt tên: `electronics-shop-template.zip`

### Cài đặt Template
1. Vào Joomla Admin: **System > Install > Extensions**
2. Upload file `electronics-shop-template.zip`
3. Vào **System > Site Templates**
4. Set **Electronics Shop** làm **Default**

## Bước 7: Cài đặt Custom Module

### Tạo Module Package
1. Nén thư mục `Custom-Extensions/mod_product_condition` thành ZIP
2. Đặt tên: `mod_product_condition.zip`

### Cài đặt Module
1. Upload và cài đặt module
2. Vào **Content > Site Modules**
3. Click **New**
4. Chọn **Product Condition**
5. Cấu hình và assign vào position `sidebar-a`

## Bước 8: Cấu hình VirtueMart

### Chạy Installation Tool
1. Vào **Components > VirtueMart**
2. Click **Tools > Install Sample Data**
3. Click **Install**

### Cấu hình Shop
1. Vào **VirtueMart > Configuration**
2. Tab **Shop**:
   - **Shop Name**: `Electronics Shop`
   - **Currency**: `Vietnamese Dong (VND)`
   - **Country**: `Vietnam`

### Tạo Categories
1. Vào **VirtueMart > Categories**
2. Tạo các danh mục:
   - Laptop cũ
   - Điện thoại cũ
   - Máy tính bảng
   - Phụ kiện
   - Linh kiện

## Bước 9: Kiểm thử Website

### Chạy Test Script
```powershell
.\test-website.ps1
```

### Kiểm tra thủ công
1. **Frontend**: http://localhost/electronics-shop
2. **Admin**: http://localhost/electronics-shop/administrator
3. **Shop**: http://localhost/electronics-shop/shop

### Checklist
- [ ] Trang chủ hiển thị đúng
- [ ] Menu navigation hoạt động
- [ ] VirtueMart shop hiển thị
- [ ] Template áp dụng đúng
- [ ] Module condition filter hoạt động
- [ ] Admin panel truy cập được

## 🎉 Hoàn thành!

Website Electronics Shop đã sẵn sàng hoạt động tại:
- **Frontend**: http://localhost/electronics-shop
- **Admin Panel**: http://localhost/electronics-shop/administrator

## 🔧 Troubleshooting

### Lỗi thường gặp
1. **Apache không start**: Kiểm tra port 80 có bị chiếm không
2. **MySQL không start**: Kiểm tra port 3306
3. **Database connection error**: Kiểm tra thông tin database
4. **Template không hiển thị**: Kiểm tra template đã set default
5. **VirtueMart lỗi**: Kiểm tra extensions đã enable

### Logs
- **Apache**: `C:\xampp\apache\logs\error.log`
- **MySQL**: `C:\xampp\mysql\data\mysql_error.log`
- **Joomla**: `logs/joomla_error.php`

## 📞 Hỗ trợ
Nếu gặp vấn đề, vui lòng kiểm tra:
1. File `Documentation/TROUBLESHOOTING.md`
2. Chạy script `test-website.ps1` để chẩn đoán
3. Kiểm tra log files
