@model GocPho.Models.RegisterViewModel

@{
    ViewData["Title"] = "Chỉnh sửa thông tin";
}

<style>
    .edit-profile-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
    }

    .page-header {
        text-align: center;
        margin-bottom: 40px;
        padding: 40px 0;
        background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
        color: white;
        border-radius: 15px;
    }

    .page-header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        font-weight: 700;
    }

    .page-header p {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0;
    }

    .form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        padding: 40px;
        margin-bottom: 30px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #333;
        font-size: 1rem;
    }

    .form-control {
        width: 100%;
        padding: 15px;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }

    .form-control:focus {
        outline: none;
        border-color: #8B4513;
        background: white;
        box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
    }

    .form-control:disabled {
        background: #e9ecef;
        color: #6c757d;
        cursor: not-allowed;
    }

    .text-danger {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 5px;
        display: block;
    }

    .form-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin-top: 30px;
        flex-wrap: wrap;
    }

    .btn {
        padding: 12px 30px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 1rem;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        color: white;
    }

    .input-icon {
        position: relative;
    }

    .input-icon::before {
        content: attr(data-icon);
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 1.2rem;
        color: #8B4513;
        z-index: 1;
    }

    .input-icon .form-control {
        padding-left: 50px;
    }

    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 5px;
        font-style: italic;
    }

    @@media (max-width: 768px) {
        .edit-profile-container {
            padding: 10px;
        }

        .page-header {
            padding: 30px 20px;
        }

        .page-header h1 {
            font-size: 2rem;
        }

        .form-card {
            padding: 25px;
        }

        .form-actions {
            flex-direction: column;
            align-items: center;
        }

        .btn {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }
    }
</style>

<div class="edit-profile-container">
    <div class="page-header">
        <h1>✏️ Chỉnh sửa thông tin</h1>
        <p>Cập nhật thông tin cá nhân của bạn</p>
    </div>

    <div class="form-card">
        <form asp-action="Profile" method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>

            <div class="form-group">
                <label asp-for="Email" class="form-label">📧 Email</label>
                <div class="input-icon" data-icon="📧">
                    <input asp-for="Email" class="form-control" type="email" disabled />
                </div>
                <div class="help-text">Email không thể thay đổi</div>
                <span asp-validation-for="Email" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="FullName" class="form-label">👤 Họ và tên</label>
                <div class="input-icon" data-icon="👤">
                    <input asp-for="FullName" class="form-control" placeholder="Nhập họ và tên của bạn" />
                </div>
                <span asp-validation-for="FullName" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="PhoneNumber" class="form-label">📱 Số điện thoại</label>
                <div class="input-icon" data-icon="📱">
                    <input asp-for="PhoneNumber" class="form-control" placeholder="Nhập số điện thoại" />
                </div>
                <div class="help-text">Số điện thoại sẽ được sử dụng để liên hệ khi giao hàng</div>
                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    💾 Lưu thay đổi
                </button>
                <a href="@Url.Action("Profile")" class="btn btn-secondary">
                    ❌ Hủy bỏ
                </a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
