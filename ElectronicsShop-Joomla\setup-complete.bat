@echo off
title Electronics Shop - Complete Setup
color 0A

echo ========================================
echo    Electronics Shop Complete Setup
echo ========================================
echo.

echo This script will help you install:
echo 1. XAMPP (Web server)
echo 2. <PERSON><PERSON><PERSON> (CMS)
echo 3. Database setup
echo 4. Basic configuration
echo.
pause

echo ========================================
echo    Step 1: XAMPP Installation
echo ========================================
echo.

REM Check if XAMPP exists
if exist "C:\xampp\xampp-control.exe" (
    echo [OK] XAMPP is already installed!
    goto :start_xampp
) else (
    echo XAMPP not found. Starting download...
    powershell -ExecutionPolicy Bypass -File "download-xampp-simple.ps1"
    echo.
    echo Please install XAMPP and then press any key to continue...
    pause
)

:start_xampp
echo ========================================
echo    Step 2: Starting XAMPP Services
echo ========================================
echo.

echo Starting XAMPP Control Panel...
start "" "C:\xampp\xampp-control.exe"

echo.
echo IMPORTANT: In XAMPP Control Panel, please:
echo 1. Click START next to Apache
echo 2. Click START next to MySQL
echo 3. Both should turn GREEN
echo.
echo Press any key when both services are running...
pause

echo ========================================
echo    Step 3: Testing XAMPP
echo ========================================
echo.

echo Opening XAMPP test page...
start "" "http://localhost"

echo.
echo You should see XAMPP welcome page.
echo Press any key if you can see it...
pause

echo ========================================
echo    Step 4: Database Setup
echo ========================================
echo.

echo Opening phpMyAdmin...
start "" "http://localhost/phpmyadmin"

echo.
echo In phpMyAdmin, please:
echo 1. Click "Databases" tab
echo 2. Create new database: electronics_shop
echo 3. Collation: utf8mb4_unicode_ci
echo 4. Click Create
echo.
echo Press any key when database is created...
pause

echo ========================================
echo    Step 5: Joomla Installation
echo ========================================
echo.

echo Downloading and installing Joomla...
powershell -ExecutionPolicy Bypass -File "download-joomla-simple.ps1"

echo.
echo Press any key to continue...
pause

echo ========================================
echo    Step 6: Joomla Setup Wizard
echo ========================================
echo.

echo Opening Joomla installation...
start "" "http://localhost/electronics-shop"

echo.
echo In Joomla setup wizard, use these settings:
echo.
echo SITE CONFIGURATION:
echo - Site Name: Electronics Shop - Thiet Bi Dien Tu Cu
echo - Description: Website ban thiet bi dien tu cu uy tin
echo - Admin Email: <EMAIL>
echo - Admin Username: admin
echo - Admin Password: admin123 (or your choice)
echo.
echo DATABASE CONFIGURATION:
echo - Database Type: MySQLi
echo - Host Name: localhost
echo - Username: root
echo - Password: (leave empty)
echo - Database Name: electronics_shop
echo - Table Prefix: jos_
echo.
echo FINALISATION:
echo - Install Sample Data: Blog Sample Data
echo - Email Configuration: Yes
echo.
pause

echo ========================================
echo    Step 7: Final Check
echo ========================================
echo.

echo Testing website URLs...
echo.
echo Frontend: http://localhost/electronics-shop
start "" "http://localhost/electronics-shop"

timeout /t 3 /nobreak >nul

echo Admin Panel: http://localhost/electronics-shop/administrator
start "" "http://localhost/electronics-shop/administrator"

echo.
echo ========================================
echo    Setup Complete!
echo ========================================
echo.
echo Your Electronics Shop website is ready!
echo.
echo URLs to remember:
echo - Website: http://localhost/electronics-shop
echo - Admin: http://localhost/electronics-shop/administrator
echo - phpMyAdmin: http://localhost/phpmyadmin
echo - XAMPP Control: C:\xampp\xampp-control.exe
echo.
echo Next steps:
echo 1. Install VirtueMart (e-commerce)
echo 2. Import sample data
echo 3. Install custom template
echo 4. Configure products and categories
echo.
echo Thank you for using Electronics Shop!
echo.
pause
