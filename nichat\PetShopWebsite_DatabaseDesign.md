# THIẾT KẾ CƠ SỞ DỮ LIỆU - WEBSITE BÁN THÚ CƯNG

## 1. TỔNG QUAN

Website bán thú cưng với đầy đủ chức năng e-commerce:
- <PERSON><PERSON> thú cưng (chó, mèo, chim, cá, v.v.)
- <PERSON><PERSON> thống đăng ký/đăng nhập
- Giỏ hàng và đặt hàng
- Quản lý admin
- Tìm kiếm và lọc sản phẩm

## 2. CÁC BẢNG CHÍNH

### 2.1 ApplicationUser (Người dùng)
```csharp
public class ApplicationUser : IdentityUser
{
    public string FullName { get; set; }
    public string? Address { get; set; }
    public string? PhoneNumber { get; set; }
    public DateTime DateOfBirth { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    
    // Navigation properties
    public virtual ICollection<Order> Orders { get; set; }
    public virtual ICollection<CartItem> CartItems { get; set; }
    public virtual ICollection<Review> Reviews { get; set; }
}
```

### 2.2 Category (<PERSON>h mục thú cưng)
```csharp
public class Category
{
    public int Id { get; set; }
    public string Name { get; set; } // Chó, Mèo, Chim, Cá, Hamster, v.v.
    public string? Description { get; set; }
    public string? ImageUrl { get; set; }
    public bool IsActive { get; set; } = true;
    public int DisplayOrder { get; set; }
    
    // Navigation properties
    public virtual ICollection<Pet> Pets { get; set; }
}
```

### 2.3 Pet (Thú cưng - Sản phẩm)
```csharp
public class Pet
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Breed { get; set; } // Giống: Golden Retriever, Persian, v.v.
    public int Age { get; set; } // Tuổi (tháng)
    public string Gender { get; set; } // Đực/Cái
    public decimal Weight { get; set; } // Cân nặng (kg)
    public string Color { get; set; } // Màu lông
    public string? Description { get; set; }
    public decimal Price { get; set; }
    public decimal? SalePrice { get; set; } // Giá khuyến mãi
    public int StockQuantity { get; set; } // Số lượng tồn kho
    public string? MainImageUrl { get; set; }
    public string? ImageUrls { get; set; } // JSON array của URLs
    public bool IsFeatured { get; set; } = false; // Sản phẩm nổi bật
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public DateTime? UpdatedAt { get; set; }
    
    // Health & Care Info
    public bool IsVaccinated { get; set; } = false; // Đã tiêm phòng
    public bool IsDewormed { get; set; } = false; // Đã tẩy giun
    public string? HealthStatus { get; set; } // Tình trạng sức khỏe
    public string? CareInstructions { get; set; } // Hướng dẫn chăm sóc
    
    // Foreign Keys
    public int CategoryId { get; set; }
    
    // Navigation properties
    public virtual Category Category { get; set; }
    public virtual ICollection<OrderDetail> OrderDetails { get; set; }
    public virtual ICollection<CartItem> CartItems { get; set; }
    public virtual ICollection<Review> Reviews { get; set; }
}
```

### 2.4 CartItem (Giỏ hàng)
```csharp
public class CartItem
{
    public int Id { get; set; }
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    
    // Foreign Keys
    public string UserId { get; set; }
    public int PetId { get; set; }
    
    // Navigation properties
    public virtual ApplicationUser User { get; set; }
    public virtual Pet Pet { get; set; }
    
    // Calculated property
    public decimal TotalPrice => UnitPrice * Quantity;
}
```

### 2.5 Order (Đơn hàng)
```csharp
public class Order
{
    public int Id { get; set; }
    public string OrderNumber { get; set; } // Mã đơn hàng
    public DateTime OrderDate { get; set; } = DateTime.Now;
    public OrderStatus Status { get; set; } = OrderStatus.Pending;
    public decimal SubTotal { get; set; }
    public decimal ShippingFee { get; set; }
    public decimal TotalAmount { get; set; }
    
    // Shipping Info
    public string ShippingName { get; set; }
    public string ShippingPhone { get; set; }
    public string ShippingAddress { get; set; }
    public string? Notes { get; set; }
    
    // Payment Info
    public PaymentMethod PaymentMethod { get; set; }
    public PaymentStatus PaymentStatus { get; set; } = PaymentStatus.Pending;
    public DateTime? PaymentDate { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public DateTime? UpdatedAt { get; set; }
    
    // Foreign Keys
    public string UserId { get; set; }
    
    // Navigation properties
    public virtual ApplicationUser User { get; set; }
    public virtual ICollection<OrderDetail> OrderDetails { get; set; }
}
```

### 2.6 OrderDetail (Chi tiết đơn hàng)
```csharp
public class OrderDetail
{
    public int Id { get; set; }
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
    
    // Foreign Keys
    public int OrderId { get; set; }
    public int PetId { get; set; }
    
    // Navigation properties
    public virtual Order Order { get; set; }
    public virtual Pet Pet { get; set; }
}
```

### 2.7 Review (Đánh giá)
```csharp
public class Review
{
    public int Id { get; set; }
    public int Rating { get; set; } // 1-5 sao
    public string? Comment { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public bool IsApproved { get; set; } = false;
    
    // Foreign Keys
    public string UserId { get; set; }
    public int PetId { get; set; }
    
    // Navigation properties
    public virtual ApplicationUser User { get; set; }
    public virtual Pet Pet { get; set; }
}
```

## 3. ENUMS

### 3.1 OrderStatus
```csharp
public enum OrderStatus
{
    Pending = 0,        // Chờ xử lý
    Confirmed = 1,      // Đã xác nhận
    Processing = 2,     // Đang chuẩn bị
    Shipping = 3,       // Đang giao hàng
    Delivered = 4,      // Đã giao hàng
    Cancelled = 5,      // Đã hủy
    Returned = 6        // Đã trả hàng
}
```

### 3.2 PaymentMethod
```csharp
public enum PaymentMethod
{
    COD = 0,           // Thanh toán khi nhận hàng
    BankTransfer = 1,  // Chuyển khoản ngân hàng
    CreditCard = 2,    // Thẻ tín dụng
    EWallet = 3        // Ví điện tử
}
```

### 3.3 PaymentStatus
```csharp
public enum PaymentStatus
{
    Pending = 0,       // Chờ thanh toán
    Paid = 1,          // Đã thanh toán
    Failed = 2,        // Thanh toán thất bại
    Refunded = 3       // Đã hoàn tiền
}
```

## 4. INDEXES VÀ CONSTRAINTS

### 4.1 Indexes quan trọng
- Pet.CategoryId (Foreign Key Index)
- Pet.IsActive, Pet.IsFeatured (Composite Index)
- Pet.Price (Range queries)
- Order.UserId, Order.Status (Composite Index)
- Order.OrderDate (Date range queries)

### 4.2 Unique Constraints
- Order.OrderNumber (Unique)
- ApplicationUser.Email (Unique - từ Identity)

## 5. SEED DATA

### 5.1 Categories mặc định
- Chó
- Mèo  
- Chim
- Cá cảnh
- Hamster & Chuột lang
- Thỏ

### 5.2 Admin Account
- Username: admin
- Password: Admin@123
- Email: <EMAIL>
- Role: Admin

## 6. BUSINESS RULES

1. **Stock Management**: Kiểm tra số lượng tồn kho trước khi cho phép đặt hàng
2. **Price Validation**: SalePrice phải nhỏ hơn Price nếu có
3. **Order Number**: Tự động generate theo format: PS{YYYYMMDD}{HHMMSS}
4. **Age Validation**: Tuổi thú cưng phải > 0 và < 300 tháng
5. **Weight Validation**: Cân nặng phải > 0
6. **Review**: Chỉ cho phép review sau khi mua hàng thành công

Thiết kế này đảm bảo đầy đủ chức năng cho một website bán thú cưng hoàn chỉnh với khả năng mở rộng trong tương lai.
