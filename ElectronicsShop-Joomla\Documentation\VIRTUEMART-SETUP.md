# Hướng Dẫn Cài Đặt và Cấu Hình VirtueMart

## Tổng quan
VirtueMart là extension e-commerce mạnh mẽ cho <PERSON>, phù hợp để xây dựng website bán thiết bị điện tử cũ.

## Bước 1: Tả<PERSON> VirtueMart

### Tải từ trang chính thức
1. <PERSON><PERSON><PERSON> cập https://virtuemart.net/downloads
2. Tải VirtueMart 4.x (tương thích Joom<PERSON> 4)
3. Tải các plugin cần thiết:
   - VM Payment - Standard Payment Methods
   - VM Shipment - Standard Shipment Methods
   - VM Invoice - Standard Invoice

### Các file cần tải
- `com_virtuemart_4.x.x.zip` - Core VirtueMart
- `plg_vmpayment_standard.zip` - Payment plugins
- `plg_vmshipment_standard.zip` - Shipment plugins
- `plg_vminvoice_standard.zip` - Invoice plugins

## Bước 2: <PERSON>ài Đặt VirtueMart

### Cài đặt qua Joomla Admin
1. <PERSON><PERSON><PERSON> <PERSON>hậ<PERSON> Administrator
2. Vào `System > Install > Extensions`
3. <PERSON><PERSON><PERSON> tab `Upload Package File`
4. Upload file `com_virtuemart_4.x.x.zip`
5. Click `Upload & Install`

### Cài đặt các plugin
1. Tiếp tục upload và cài đặt các plugin:
   - Payment plugins
   - Shipment plugins
   - Invoice plugins

### Kiểm tra cài đặt
1. Vào `Components > VirtueMart`
2. Nếu thấy VirtueMart dashboard thì cài đặt thành công

## Bước 3: Cấu Hình Cơ Bản

### Chạy VirtueMart Installation Tool
1. Vào `Components > VirtueMart`
2. Click `Tools > Install Sample Data`
3. Chọn `Install Sample Data` để có dữ liệu mẫu
4. Click `Install`

### Cấu hình Shop Information
1. Vào `VirtueMart > Configuration`
2. Tab `Shop`:
   - Shop Name: `Electronics Shop`
   - Company Name: `Electronics Shop Vietnam`
   - Shop Email: `<EMAIL>`
   - Shop Phone: `+84 ***********`

### Cấu hình Currency (Tiền tệ)
1. Vào `VirtueMart > Configuration`
2. Tab `Shop`:
   - Default Currency: `Vietnamese Dong (VND)`
   - Currency Symbol: `₫`
   - Currency Position: `After Price`
   - Decimal Places: `0`

### Cấu hình Country & State
1. Vào `VirtueMart > Configuration`
2. Tab `Shop`:
   - Default Country: `Vietnam`
   - Default State: `Ho Chi Minh City`

## Bước 4: Cấu Hình Categories (Danh Mục)

### Tạo danh mục chính
1. Vào `VirtueMart > Categories`
2. Click `New` để tạo danh mục mới:

#### Laptop cũ
- Category Name: `Laptop cũ`
- Category Description: `Laptop đã qua sử dụng với chất lượng tốt`
- Published: `Yes`
- Ordering: `1`

#### Điện thoại cũ
- Category Name: `Điện thoại cũ`
- Category Description: `Smartphone và điện thoại di động cũ`
- Published: `Yes`
- Ordering: `2`

#### Máy tính bảng
- Category Name: `Máy tính bảng`
- Category Description: `Tablet và iPad đã qua sử dụng`
- Published: `Yes`
- Ordering: `3`

#### Phụ kiện
- Category Name: `Phụ kiện`
- Category Description: `Phụ kiện điện tử: tai nghe, sạc, cáp...`
- Published: `Yes`
- Ordering: `4`

#### Linh kiện
- Category Name: `Linh kiện`
- Category Description: `Linh kiện máy tính: RAM, ổ cứng, VGA...`
- Published: `Yes`
- Ordering: `5`

### Tạo danh mục con
Tạo các danh mục con cho từng danh mục chính (như đã mô tả trong database design).

## Bước 5: Cấu Hình Payment Methods

### Cài đặt COD (Cash on Delivery)
1. Vào `VirtueMart > Payment Methods`
2. Click `New`
3. Cấu hình:
   - Payment Name: `Thanh toán khi nhận hàng (COD)`
   - Published: `Yes`
   - Payment Method: `Standard Payment`
   - Payment Type: `Cash on Delivery`

### Cài đặt Bank Transfer
1. Click `New` payment method
2. Cấu hình:
   - Payment Name: `Chuyển khoản ngân hàng`
   - Published: `Yes`
   - Payment Method: `Standard Payment`
   - Payment Type: `Bank Transfer`
   - Bank Details: Thêm thông tin tài khoản ngân hàng

### Cài đặt E-Wallet (Ví điện tử)
1. Click `New` payment method
2. Cấu hình:
   - Payment Name: `Ví điện tử (MoMo, ZaloPay)`
   - Published: `Yes`
   - Payment Method: `Standard Payment`

## Bước 6: Cấu Hình Shipping Methods

### Cài đặt Standard Shipping
1. Vào `VirtueMart > Shipment Methods`
2. Click `New`
3. Cấu hình:
   - Shipment Name: `Giao hàng tiêu chuẩn`
   - Published: `Yes`
   - Shipment Method: `Standard Shipment`
   - Shipping Rate: `30000` (30,000 VND)

### Cài đặt Express Shipping
1. Click `New` shipment method
2. Cấu hình:
   - Shipment Name: `Giao hàng nhanh`
   - Published: `Yes`
   - Shipment Method: `Standard Shipment`
   - Shipping Rate: `50000` (50,000 VND)

### Cài đặt Free Shipping
1. Click `New` shipment method
2. Cấu hình:
   - Shipment Name: `Miễn phí giao hàng`
   - Published: `Yes`
   - Shipment Method: `Standard Shipment`
   - Shipping Rate: `0`
   - Minimum Order: `5000000` (5 triệu VND)

## Bước 7: Cấu Hình Tax (Thuế)

### Cài đặt VAT
1. Vào `VirtueMart > Tax Rules`
2. Click `New`
3. Cấu hình:
   - Tax Name: `VAT`
   - Tax Rate: `10` (10%)
   - Published: `Yes`

## Bước 8: Cấu Hình Template Integration

### Tạo Menu Items
1. Vào `Menus > Main Menu`
2. Tạo các menu items:
   - `Shop` (VirtueMart Category Layout)
   - `Cart` (VirtueMart Cart)
   - `Checkout` (VirtueMart Checkout)

### Cấu hình Modules
1. Vào `Extensions > Modules`
2. Tạo các modules:
   - VirtueMart Cart Module
   - VirtueMart Categories Module
   - VirtueMart Featured Products Module
   - VirtueMart Search Module

## Bước 9: Cấu Hình Email Templates

### Order Confirmation Email
1. Vào `VirtueMart > Configuration`
2. Tab `Email`:
   - Order Confirmation: `Yes`
   - Email Template: Customize theo tiếng Việt

### Invoice Email
1. Cấu hình email hóa đơn
2. Thêm thông tin công ty và logo

## Bước 10: Cấu Hình SEO

### SEO Settings
1. Vào `VirtueMart > Configuration`
2. Tab `SEO`:
   - SEF URLs: `Yes`
   - Meta Description: Enable
   - Meta Keywords: Enable

### URL Rewriting
1. Cấu hình .htaccess cho SEF URLs
2. Enable Joomla SEF URLs

## Bước 11: Import Sample Data

### Sử dụng SQL Scripts
1. Import database design từ `Database/database-design.sql`
2. Import sample data từ `Sample-Data/sample-products.sql`

### Thêm hình ảnh sản phẩm
1. Upload hình ảnh vào `images/stories/virtuemart/product/`
2. Liên kết hình ảnh với sản phẩm trong VirtueMart

## Bước 12: Testing

### Kiểm tra chức năng
- [ ] Hiển thị danh mục sản phẩm
- [ ] Hiển thị chi tiết sản phẩm
- [ ] Thêm sản phẩm vào giỏ hàng
- [ ] Quy trình checkout
- [ ] Payment methods hoạt động
- [ ] Shipping methods hoạt động
- [ ] Email notifications

### Test URLs
- Shop: `/shop`
- Categories: `/shop/laptop-cu`
- Product: `/shop/laptop-cu/dell-latitude-e7450`
- Cart: `/cart`
- Checkout: `/checkout`

## Troubleshooting

### Lỗi thường gặp
1. **VirtueMart not showing**: Kiểm tra menu items và modules
2. **Payment not working**: Kiểm tra payment plugins đã enable
3. **Images not displaying**: Kiểm tra đường dẫn hình ảnh
4. **Currency not showing**: Kiểm tra currency configuration

### Log Files
- Joomla Error Log: `logs/joomla_error.php`
- VirtueMart Log: `administrator/components/com_virtuemart/logs/`

## Kết luận
Sau khi hoàn thành các bước trên, website bán thiết bị điện tử cũ với VirtueMart đã sẵn sàng hoạt động.
