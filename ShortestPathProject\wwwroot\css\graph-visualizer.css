/* Graph Visualizer Styles */

.graph-canvas {
    border: 2px solid #dee2e6;
    border-radius: 8px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    cursor: crosshair;
    transition: all 0.3s ease;
}

.graph-canvas:hover {
    border-color: #007bff;
    box-shadow: 0 0 10px rgba(0, 123, 255, 0.2);
}

/* Vertex Styles */
.vertex-circle {
    fill: #007bff;
    stroke: #0056b3;
    stroke-width: 2;
    cursor: pointer;
    transition: all 0.3s ease;
}

.vertex-circle:hover {
    fill: #0056b3;
    stroke-width: 3;
    filter: drop-shadow(0 0 5px rgba(0, 123, 255, 0.5));
}

.vertex-circle.selected {
    fill: #ffc107;
    stroke: #e0a800;
    stroke-width: 3;
    filter: drop-shadow(0 0 8px rgba(255, 193, 7, 0.8));
}

.vertex-circle.source {
    fill: #28a745;
    stroke: #1e7e34;
    stroke-width: 3;
}

.vertex-circle.target {
    fill: #dc3545;
    stroke: #c82333;
    stroke-width: 3;
}

.vertex-circle.path {
    fill: #ffc107;
    stroke: #e0a800;
    stroke-width: 3;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { filter: drop-shadow(0 0 5px rgba(255, 193, 7, 0.5)); }
    50% { filter: drop-shadow(0 0 15px rgba(255, 193, 7, 0.8)); }
    100% { filter: drop-shadow(0 0 5px rgba(255, 193, 7, 0.5)); }
}

/* Edge Styles */
.edge {
    stroke: #6c757d;
    stroke-width: 2;
    fill: none;
    transition: all 0.3s ease;
}

.edge:hover {
    stroke: #495057;
    stroke-width: 3;
}

.edge.path {
    stroke: #ffc107;
    stroke-width: 4;
    animation: pathFlow 2s infinite;
}

@keyframes pathFlow {
    0% { stroke-dasharray: 0, 10; }
    100% { stroke-dasharray: 10, 0; }
}

.arrow {
    fill: #6c757d;
    transition: all 0.3s ease;
}

.arrow.path {
    fill: #ffc107;
}

/* Text Styles */
.vertex-label {
    font-size: 14px;
    font-weight: bold;
    fill: white;
    text-anchor: middle;
    dominant-baseline: middle;
    pointer-events: none;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.edge-weight {
    font-size: 12px;
    font-weight: bold;
    fill: #495057;
    text-anchor: middle;
    dominant-baseline: middle;
    pointer-events: none;
    background: white;
    padding: 2px 4px;
    border-radius: 3px;
}

/* Card Styles */
.algorithm-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.algorithm-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-bottom: none;
    font-weight: 600;
}

/* Step Styles */
.step-item {
    padding: 8px 12px;
    margin: 4px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 4px solid #007bff;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.step-item:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    border-left-color: #0056b3;
    transform: translateX(2px);
}

/* Result Styles */
.result-item {
    padding: 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 8px;
    border: 1px solid #dee2e6;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.result-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.comparison-results .result-item {
    border-left: 4px solid #007bff;
}

.comparison-results .result-item:nth-child(2) {
    border-left-color: #28a745;
}

.comparison-results .result-item:nth-child(3) {
    border-left-color: #ffc107;
}

.comparison-results .result-item:nth-child(4) {
    border-left-color: #dc3545;
}

/* Performance Chart */
.performance-chart {
    max-height: 300px;
    background: white;
    border-radius: 8px;
    padding: 10px;
}

/* Button Styles */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
}

.btn-group .btn:last-child {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
}

/* Form Controls */
.form-select, .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Loading Styles */
.spinner-border {
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .graph-canvas {
        height: 400px;
    }
    
    .vertex-circle {
        r: 15;
    }
    
    .vertex-label {
        font-size: 12px;
    }
    
    .edge-weight {
        font-size: 10px;
    }
    
    .step-item {
        font-size: 12px;
        padding: 6px 10px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .graph-canvas {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
        border-color: #4a5568;
    }
    
    .vertex-label {
        fill: #f7fafc;
    }
    
    .edge-weight {
        fill: #e2e8f0;
    }
    
    .step-item {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        color: #f7fafc;
    }
    
    .result-item {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        border-color: #4a5568;
        color: #f7fafc;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.text-shadow {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(45deg, #007bff, #28a745) 1;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
