namespace StudentManagement
{
    /// <summary>
    /// Node cho danh sách liên kết đơn
    /// </summary>
    public class StudentNode
    {
        public Student Data { get; set; }
        public StudentNode Next { get; set; }

        public StudentNode(Student student)
        {
            Data = student;
            Next = null;
        }
    }

    /// <summary>
    /// Node cho danh sách liên kết kép
    /// </summary>
    public class DoublyStudentNode
    {
        public Student Data { get; set; }
        public DoublyStudentNode Next { get; set; }
        public DoublyStudentNode Previous { get; set; }

        public DoublyStudentNode(Student student)
        {
            Data = student;
            Next = null;
            Previous = null;
        }
    }
}
