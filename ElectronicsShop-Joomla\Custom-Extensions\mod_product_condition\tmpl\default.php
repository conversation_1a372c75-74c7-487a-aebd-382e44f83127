<?php
/**
 * @package     Product Condition Module
 * @subpackage  mod_product_condition
 * @copyright   Copyright (C) 2025 Electronics Shop. All rights reserved.
 * @license     GNU General Public License version 2 or later
 */

defined('_JEXEC') or die;

use <PERSON><PERSON><PERSON>\CMS\Language\Text;
use <PERSON><PERSON><PERSON>\CMS\Router\Route;

if (empty($conditions)) {
    return;
}
?>

<div class="product-condition-module<?php echo $moduleClassSfx; ?>">
    
    <?php if ($displayMode === 'filter') : ?>
        <!-- Filter Mode -->
        <div class="condition-filter">
            <!-- All Products Option -->
            <a href="<?php echo $helper->getFilterUrl(0); ?>" 
               class="condition-item condition-filter-link<?php echo !$currentCondition ? ' active' : ''; ?>"
               data-condition-id="0">
                <span class="condition-name">
                    <?php echo Text::_('MOD_PRODUCT_CONDITION_ALL_CONDITIONS'); ?>
                </span>
            </a>
            
            <?php foreach ($conditions as $condition) : ?>
                <a href="<?php echo $helper->getFilterUrl($condition->condition_id); ?>" 
                   class="condition-item condition-filter-link<?php echo $helper->isConditionActive($condition->condition_id) ? ' active' : ''; ?>"
                   data-condition-id="<?php echo $condition->condition_id; ?>">
                   
                    <?php if ($showColorIndicator) : ?>
                        <span class="condition-color" style="background-color: <?php echo $condition->condition_color; ?>;"></span>
                    <?php endif; ?>
                    
                    <span class="condition-name">
                        <?php echo htmlspecialchars($condition->condition_name, ENT_QUOTES, 'UTF-8'); ?>
                    </span>
                    
                    <?php if ($showPercentage) : ?>
                        <span class="condition-percentage">
                            (<?php echo $condition->condition_percentage; ?>%)
                        </span>
                    <?php endif; ?>
                    
                    <?php if ($condition->product_count > 0) : ?>
                        <span class="badge bg-secondary ms-2">
                            <?php echo $condition->product_count; ?>
                        </span>
                    <?php endif; ?>
                </a>
            <?php endforeach; ?>
        </div>
        
    <?php elseif ($displayMode === 'badge') : ?>
        <!-- Badge Mode -->
        <div class="condition-badges">
            <?php foreach ($conditions as $condition) : ?>
                <span class="condition-badge" style="background-color: <?php echo $condition->condition_color; ?>;">
                    <?php if ($showColorIndicator) : ?>
                        <span class="condition-color" style="background-color: <?php echo $condition->condition_color; ?>;"></span>
                    <?php endif; ?>
                    
                    <?php echo htmlspecialchars($condition->condition_name, ENT_QUOTES, 'UTF-8'); ?>
                    
                    <?php if ($showPercentage) : ?>
                        (<?php echo $condition->condition_percentage; ?>%)
                    <?php endif; ?>
                </span>
            <?php endforeach; ?>
        </div>
        
    <?php elseif ($displayMode === 'list') : ?>
        <!-- List Mode -->
        <ul class="condition-list">
            <?php foreach ($conditions as $condition) : ?>
                <li>
                    <div class="d-flex align-items-center">
                        <?php if ($showColorIndicator) : ?>
                            <span class="condition-color" style="background-color: <?php echo $condition->condition_color; ?>;"></span>
                        <?php endif; ?>
                        
                        <div class="condition-info flex-grow-1">
                            <div class="condition-name-wrapper">
                                <span class="condition-name">
                                    <?php echo htmlspecialchars($condition->condition_name, ENT_QUOTES, 'UTF-8'); ?>
                                </span>
                                
                                <?php if ($showPercentage) : ?>
                                    <span class="condition-percentage">
                                        (<?php echo $condition->condition_percentage; ?>%)
                                    </span>
                                <?php endif; ?>
                                
                                <?php if ($condition->product_count > 0) : ?>
                                    <span class="badge bg-secondary ms-2">
                                        <?php echo $condition->product_count; ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($showDescription && !empty($condition->condition_description)) : ?>
                                <div class="condition-description">
                                    <?php echo htmlspecialchars($condition->condition_description, ENT_QUOTES, 'UTF-8'); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </li>
            <?php endforeach; ?>
        </ul>
    <?php endif; ?>
    
    <!-- Condition Legend (if enabled) -->
    <?php if ($params->get('show_legend', 0)) : ?>
        <div class="condition-legend mt-3">
            <h6><?php echo Text::_('MOD_PRODUCT_CONDITION_LEGEND'); ?></h6>
            <div class="legend-items">
                <?php foreach ($conditions as $condition) : ?>
                    <div class="legend-item d-flex align-items-center mb-1">
                        <span class="condition-color me-2" style="background-color: <?php echo $condition->condition_color; ?>;"></span>
                        <span class="legend-text">
                            <strong><?php echo htmlspecialchars($condition->condition_name, ENT_QUOTES, 'UTF-8'); ?></strong>
                            <?php if ($showPercentage) : ?>
                                (<?php echo $condition->condition_percentage; ?>%)
                            <?php endif; ?>
                            <?php if ($showDescription && !empty($condition->condition_description)) : ?>
                                - <?php echo htmlspecialchars($condition->condition_description, ENT_QUOTES, 'UTF-8'); ?>
                            <?php endif; ?>
                        </span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Statistics (if enabled) -->
    <?php if ($params->get('show_statistics', 0)) : ?>
        <?php $stats = $helper->getConditionStatistics(); ?>
        <div class="condition-statistics mt-3">
            <h6><?php echo Text::_('MOD_PRODUCT_CONDITION_STATISTICS'); ?></h6>
            <div class="stats-summary">
                <p class="mb-2">
                    <strong><?php echo Text::_('MOD_PRODUCT_CONDITION_TOTAL_PRODUCTS'); ?>:</strong>
                    <?php echo $stats['total_products']; ?>
                </p>
                
                <?php if ($stats['total_products'] > 0) : ?>
                    <div class="stats-breakdown">
                        <?php foreach ($stats['conditions'] as $conditionId => $stat) : ?>
                            <?php if ($stat['count'] > 0) : ?>
                                <div class="stat-item d-flex justify-content-between align-items-center mb-1">
                                    <span class="d-flex align-items-center">
                                        <span class="condition-color me-2" style="background-color: <?php echo $stat['color']; ?>;"></span>
                                        <?php echo htmlspecialchars($stat['name'], ENT_QUOTES, 'UTF-8'); ?>
                                    </span>
                                    <span class="stat-count">
                                        <?php echo $stat['count']; ?>
                                        <small class="text-muted">
                                            (<?php echo round(($stat['count'] / $stats['total_products']) * 100, 1); ?>%)
                                        </small>
                                    </span>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php if ($enableAjaxFilter) : ?>
    <!-- AJAX Loading Indicator -->
    <div id="condition-loading" class="text-center" style="display: none;">
        <div class="spinner-border spinner-border-sm" role="status">
            <span class="visually-hidden"><?php echo Text::_('JGLOBAL_LOADING'); ?></span>
        </div>
        <span class="ms-2"><?php echo Text::_('MOD_PRODUCT_CONDITION_FILTERING'); ?></span>
    </div>
<?php endif; ?>
