using System;
using System.Collections.Generic;

namespace StudentManagement
{
    /// <summary>
    /// Danh sách liên kết đơn để quản lý sinh viên
    /// </summary>
    public class SingleLinkedList
    {
        private StudentNode head;
        private int count;

        public int Count => count;

        public SingleLinkedList()
        {
            head = null;
            count = 0;
        }

        /// <summary>
        /// Thêm sinh viên vào đầu danh sách
        /// </summary>
        public void AddFirst(Student student)
        {
            StudentNode newNode = new StudentNode(student);
            newNode.Next = head;
            head = newNode;
            count++;
        }

        /// <summary>
        /// Thêm sinh viên vào cuối danh sách
        /// </summary>
        public void AddLast(Student student)
        {
            StudentNode newNode = new StudentNode(student);
            
            if (head == null)
            {
                head = newNode;
            }
            else
            {
                StudentNode current = head;
                while (current.Next != null)
                {
                    current = current.Next;
                }
                current.Next = newNode;
            }
            count++;
        }

        /// <summary>
        /// Thêm sinh viên vào vị trí chỉ định
        /// </summary>
        public void AddAt(int index, Student student)
        {
            if (index < 0 || index > count)
                throw new ArgumentOutOfRangeException("Index không hợp lệ");

            if (index == 0)
            {
                AddFirst(student);
                return;
            }

            StudentNode newNode = new StudentNode(student);
            StudentNode current = head;
            
            for (int i = 0; i < index - 1; i++)
            {
                current = current.Next;
            }
            
            newNode.Next = current.Next;
            current.Next = newNode;
            count++;
        }

        /// <summary>
        /// Xóa sinh viên theo mã sinh viên
        /// </summary>
        public bool Remove(string maSinhVien)
        {
            if (head == null) return false;

            if (head.Data.MaSinhVien == maSinhVien)
            {
                head = head.Next;
                count--;
                return true;
            }

            StudentNode current = head;
            while (current.Next != null)
            {
                if (current.Next.Data.MaSinhVien == maSinhVien)
                {
                    current.Next = current.Next.Next;
                    count--;
                    return true;
                }
                current = current.Next;
            }
            return false;
        }

        /// <summary>
        /// Tìm kiếm sinh viên theo mã sinh viên
        /// </summary>
        public Student Find(string maSinhVien)
        {
            StudentNode current = head;
            while (current != null)
            {
                if (current.Data.MaSinhVien == maSinhVien)
                    return current.Data;
                current = current.Next;
            }
            return null;
        }

        /// <summary>
        /// Tìm kiếm sinh viên theo tên
        /// </summary>
        public List<Student> FindByName(string hoTen)
        {
            List<Student> result = new List<Student>();
            StudentNode current = head;
            
            while (current != null)
            {
                if (current.Data.HoTen.ToLower().Contains(hoTen.ToLower()))
                    result.Add(current.Data);
                current = current.Next;
            }
            return result;
        }

        /// <summary>
        /// Lấy tất cả sinh viên
        /// </summary>
        public List<Student> GetAll()
        {
            List<Student> result = new List<Student>();
            StudentNode current = head;
            
            while (current != null)
            {
                result.Add(current.Data);
                current = current.Next;
            }
            return result;
        }

        /// <summary>
        /// Xóa tất cả sinh viên
        /// </summary>
        public void Clear()
        {
            head = null;
            count = 0;
        }

        /// <summary>
        /// Kiểm tra danh sách có rỗng không
        /// </summary>
        public bool IsEmpty()
        {
            return head == null;
        }

        /// <summary>
        /// Sắp xếp danh sách theo điểm trung bình (giảm dần)
        /// </summary>
        public void SortByGPA()
        {
            if (head == null || head.Next == null) return;

            bool swapped;
            do
            {
                swapped = false;
                StudentNode current = head;
                
                while (current.Next != null)
                {
                    if (current.Data.DiemTrungBinh < current.Next.Data.DiemTrungBinh)
                    {
                        // Hoán đổi dữ liệu
                        Student temp = current.Data;
                        current.Data = current.Next.Data;
                        current.Next.Data = temp;
                        swapped = true;
                    }
                    current = current.Next;
                }
            } while (swapped);
        }
    }
}
