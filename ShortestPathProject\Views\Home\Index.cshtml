﻿@{
    ViewData["Title"] = "Thuật toán tìm đường đi ngắn nhất";
}

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="bg-primary text-white p-4 rounded">
                <h1 class="mb-2">Thu<PERSON>t toán tìm đường đi ngắn nhất</h1>
                <p class="mb-0"><PERSON> họa và so sánh các thuật toán: Dijkstra, Bellman-Ford, Floyd-Warshall, SPFA</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Panel điều khiển -->
        <div class="col-md-3">
            <div class="card algorithm-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Đi<PERSON>u khiển</h5>
                </div>
                <div class="card-body">
                    <!-- <PERSON><PERSON><PERSON> thuật toán -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">Thuật toán:</label>
                        <select id="algorithmSelect" class="form-select">
                            <option value="dijkstra">Dijkstra</option>
                            <option value="bellman-ford">Bellman-Ford</option>
                            <option value="floyd-warshall">Floyd-Warshall</option>
                            <option value="spfa">SPFA</option>
                            <option value="compare">So sánh tất cả</option>
                        </select>
                    </div>

                    <!-- Chọn đỉnh nguồn và đích -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">Đỉnh nguồn:</label>
                        <select id="sourceSelect" class="form-select">
                            <option value="">Chọn đỉnh nguồn</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Đỉnh đích:</label>
                        <select id="targetSelect" class="form-select">
                            <option value="">Chọn đỉnh đích</option>
                        </select>
                    </div>

                    <!-- Nút thực thi -->
                    <button id="runAlgorithm" class="btn btn-success w-100 mb-2">
                        Chạy thuật toán
                    </button>

                    <button id="clearResult" class="btn btn-secondary w-100 mb-3">
                        Xóa kết quả
                    </button>

                    <!-- Đồ thị mẫu -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">Đồ thị mẫu:</label>
                        <select id="sampleGraphSelect" class="form-select">
                            <option value="">Chọn đồ thị mẫu</option>
                            <option value="simple">Đồ thị đơn giản</option>
                            <option value="negative">Có trọng số âm</option>
                            <option value="large">Đồ thị lớn</option>
                        </select>
                    </div>

                    <button id="loadSample" class="btn btn-outline-primary w-100 mb-3">
                        Tải đồ thị mẫu
                    </button>

                    <!-- Chế độ chỉnh sửa -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">Chế độ chỉnh sửa:</label>
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="editMode" id="selectMode" checked>
                            <label class="btn btn-outline-primary btn-sm" for="selectMode">Chọn</label>

                            <input type="radio" class="btn-check" name="editMode" id="addVertexMode">
                            <label class="btn btn-outline-primary btn-sm" for="addVertexMode">Đỉnh</label>

                            <input type="radio" class="btn-check" name="editMode" id="addEdgeMode">
                            <label class="btn btn-outline-primary btn-sm" for="addEdgeMode">Cạnh</label>
                        </div>
                    </div>

                    <button id="clearGraph" class="btn btn-outline-danger w-100">
                        Xóa đồ thị
                    </button>
                </div>
            </div>

            <!-- Kết quả -->
            <div class="card algorithm-card mt-3">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">Kết quả</h6>
                </div>
                <div class="card-body">
                    <div id="resultContainer">
                        <p class="text-muted text-center">
                            Chưa có kết quả
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vùng hiển thị đồ thị -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">Đồ thị</h5>
                </div>
                <div class="card-body p-0">
                    <svg id="graphSvg" class="graph-canvas" width="100%" height="600" viewBox="0 0 800 600">
                        <!-- Đồ thị sẽ được vẽ ở đây -->
                    </svg>
                </div>
                <div class="card-footer">
                    <small class="text-muted">
                        <strong>Hướng dẫn:</strong>
                        Chọn chế độ "Đỉnh" và click để thêm đỉnh.
                        Chọn chế độ "Cạnh" và click hai đỉnh để tạo cạnh.
                    </small>
                </div>
            </div>
        </div>

        <!-- Panel thông tin -->
        <div class="col-md-3">
            <div class="card algorithm-card">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">Thông tin đồ thị</h6>
                </div>
                <div class="card-body">
                    <div id="graphInfo">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border rounded p-2">
                                    <div class="h4 mb-0 text-primary" id="vertexCount">0</div>
                                    <small class="text-muted">Đỉnh</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="border rounded p-2">
                                    <div class="h4 mb-0 text-info" id="edgeCount">0</div>
                                    <small class="text-muted">Cạnh</small>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <p class="mb-1"><strong>Loại:</strong> <span id="graphType">Có hướng</span></p>
                            <p class="mb-0"><strong>Trọng số âm:</strong> <span id="hasNegativeWeights">Không</span></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Các bước thực hiện -->
            <div class="card algorithm-card mt-3">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0">Các bước thực hiện</h6>
                </div>
                <div class="card-body">
                    <div id="stepsContainer" style="max-height: 300px; overflow-y: auto;">
                        <p class="text-muted text-center">
                            Chưa có bước nào
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
