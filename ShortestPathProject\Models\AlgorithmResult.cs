using System;
using System.Collections.Generic;
using System.Linq;

namespace ShortestPathProject.Models
{
    public class AlgorithmResult
    {
        public Dictionary<Vertex, double> Distances { get; set; }
        public Dictionary<Vertex, Vertex> Predecessors { get; set; }
        public List<Vertex> Path { get; set; }
        public double PathLength { get; set; }
        public long ExecutionTimeMs { get; set; }
        public string AlgorithmName { get; set; }
        public bool HasNegativeCycle { get; set; }
        public List<string> Steps { get; set; } // <PERSON><PERSON><PERSON> bước thực hiện
        
        // <PERSON>-<PERSON>
        public double[,] DistanceMatrix { get; set; }
        public int[,] NextMatrix { get; set; }
        public List<Vertex> VertexList { get; set; }
        
        public AlgorithmResult(string algorithmName)
        {
            AlgorithmName = algorithmName;
            Distances = new Dictionary<Vertex, double>();
            Predecessors = new Dictionary<Vertex, Vertex>();
            Path = new List<Vertex>();
            Steps = new List<string>();
            HasNegativeCycle = false;
        }
        
        // Xây dựng đường đi từ predecessor
        public void BuildPath(Vertex source, Vertex target)
        {
            Path.Clear();
            
            if (!Distances.ContainsKey(target) || 
                double.IsPositiveInfinity(Distances[target]))
            {
                return; // Không có đường đi
            }
            
            List<Vertex> reversePath = new List<Vertex>();
            Vertex current = target;
            
            while (current != null)
            {
                reversePath.Add(current);
                current = Predecessors.ContainsKey(current) ? 
                         Predecessors[current] : null;
            }
            
            // Đảo ngược để có đường đi từ source đến target
            Path = reversePath.AsEnumerable().Reverse().ToList();
            PathLength = Distances[target];
        }
        
        // Chuyển đổi sang JSON để gửi về client
        public object ToJson()
        {
            return new
            {
                algorithmName = AlgorithmName,
                distances = Distances.ToDictionary(
                    kvp => kvp.Key.Name, 
                    kvp => double.IsPositiveInfinity(kvp.Value) ? "∞" : kvp.Value.ToString("F2")
                ),
                path = Path.Select(v => v.Name).ToList(),
                pathLength = double.IsPositiveInfinity(PathLength) ? "∞" : PathLength.ToString("F2"),
                executionTimeMs = ExecutionTimeMs,
                hasNegativeCycle = HasNegativeCycle,
                steps = Steps
            };
        }
    }

    // Model cho request từ client
    public class AlgorithmRequest
    {
        public List<VertexData> Vertices { get; set; } = new List<VertexData>();
        public List<EdgeData> Edges { get; set; } = new List<EdgeData>();
        public string Source { get; set; } = "";
        public string Target { get; set; } = "";
        public bool IsDirected { get; set; } = true;
    }

    public class VertexData
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public double X { get; set; }
        public double Y { get; set; }
    }

    public class EdgeData
    {
        public int From { get; set; }
        public int To { get; set; }
        public double Weight { get; set; }
    }

    // Model cho response so sánh
    public class ComparisonResult
    {
        public List<AlgorithmResult> Results { get; set; } = new List<AlgorithmResult>();
        public string FastestAlgorithm { get; set; } = "";
        public Dictionary<string, long> ExecutionTimes { get; set; } = new Dictionary<string, long>();
        
        public object ToJson()
        {
            return new
            {
                algorithms = Results.Select(r => r.ToJson()).ToList(),
                fastestAlgorithm = FastestAlgorithm,
                executionTimes = ExecutionTimes,
                summary = new
                {
                    totalAlgorithms = Results.Count,
                    hasNegativeCycle = Results.Any(r => r.HasNegativeCycle),
                    allPathsFound = Results.All(r => r.Path.Count > 0)
                }
            };
        }
    }
}
