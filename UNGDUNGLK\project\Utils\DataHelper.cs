using DoublyLinkedListApp.Models;
using DoublyLinkedListApp.Services;
using System;
using System.Collections.Generic;

namespace DoublyLinkedListApp.Utils
{
    /// <summary>
    /// Helper class để tạo dữ liệu mẫu và hiển thị dữ liệu
    /// </summary>
    public static class DataHelper
    {
        /// <summary>
        /// Tạo dữ liệu mẫu
        /// </summary>
        public static void TaoDuLieuMau(HangHoaService service)
        {
            var danhSachMau = new List<HangHoa>
            {
                new HangHoa("HH001", "Laptop Dell Inspiron 15", "Chiếc", 15000000, 10),
                new HangHoa("HH002", "Chuột không dây Logitech", "Chiếc", 250000, 50),
                new HangHoa("HH003", "Bàn phím cơ Gaming", "Chiếc", 1200000, 25),
                new HangHoa("HH004", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> 24 inch", "<PERSON>ế<PERSON>", 3500000, 15),
                new HangHoa("HH005", "Ổ cứng SSD 500GB", "Chiếc", 1800000, 30),
                new HangHoa("HH006", "RAM DDR4 8GB", "Thanh", 800000, 40),
                new HangHoa("HH007", "Tai nghe Sony WH-1000XM4", "Chiếc", 7500000, 8),
                new HangHoa("HH008", "Webcam Logitech C920", "Chiếc", 2200000, 20),
                new HangHoa("HH009", "Loa Bluetooth JBL", "Chiếc", 1500000, 12),
                new HangHoa("HH010", "Máy in Canon Pixma", "Chiếc", 2800000, 5),
                new HangHoa("HH011", "USB 3.0 32GB", "Chiếc", 150000, 100),
                new HangHoa("HH012", "Cáp HDMI 2m", "Sợi", 80000, 60),
                new HangHoa("HH013", "Ổ cắm điện thông minh", "Chiếc", 350000, 35),
                new HangHoa("HH014", "Đế tản nhiệt laptop", "Chiếc", 450000, 18),
                new HangHoa("HH015", "Balo laptop Targus", "Chiếc", 650000, 22),
                new HangHoa("HH016", "Giấy A4 Double A", "Ream", 85000, 200),
                new HangHoa("HH017", "Bút bi Thiên Long", "Cây", 5000, 500),
                new HangHoa("HH018", "Máy tính bảng iPad", "Chiếc", 12000000, 6),
                new HangHoa("HH019", "Điện thoại iPhone 14", "Chiếc", 25000000, 3),
                new HangHoa("HH020", "Sạc dự phòng 10000mAh", "Chiếc", 400000, 45)
            };

            foreach (var hangHoa in danhSachMau)
            {
                service.ThemHangHoa(hangHoa);
            }

            Console.WriteLine($"Đã tạo {danhSachMau.Count} bản ghi dữ liệu mẫu!");
        }

        /// <summary>
        /// Hiển thị danh sách hàng hóa dạng bảng
        /// </summary>
        public static void HienThiDanhSach(List<HangHoa> danhSach, string tieuDe = "DANH SÁCH HÀNG HÓA")
        {
            if (danhSach.Count == 0)
            {
                Console.WriteLine("Danh sách trống!");
                return;
            }

            MenuHelper.HienThiTieuDe(tieuDe);

            // Header
            Console.WriteLine("╔════════╦══════════════════════════╦═══════════╦═══════════╦═══════════╦═══════════════╦══════════════╗");
            Console.WriteLine("║ Mã số  ║         Tên hàng         ║ Đơn vị    ║ Đơn giá   ║ Số lượng  ║   Thành tiền  ║  Trạng thái  ║");
            Console.WriteLine("╠════════╬══════════════════════════╬═══════════╬═══════════╬═══════════╬═══════════════╬══════════════╣");

            // Data rows
            foreach (var hangHoa in danhSach)
            {
                Console.WriteLine($"║ {hangHoa.MaSo,-6} ║ {TruncateString(hangHoa.TenHang, 24),-24} ║ {TruncateString(hangHoa.DonViTinh, 9),-9} ║ {hangHoa.DonGia,9:N0} ║ {hangHoa.SoLuong,9} ║ {hangHoa.ThanhTien,13:N0} ║ {hangHoa.TrangThai,-12} ║");
            }

            Console.WriteLine("╚════════╩══════════════════════════╩═══════════╩═══════════╩═══════════╩═══════════════╩══════════════╝");
            Console.WriteLine($"Tổng số: {danhSach.Count} bản ghi");
        }

        /// <summary>
        /// Hiển thị thông tin chi tiết một hàng hóa
        /// </summary>
        public static void HienThiChiTiet(HangHoa hangHoa)
        {
            Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                    THÔNG TIN CHI TIẾT                        ║");
            Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
            Console.WriteLine($"║ Mã số:           {hangHoa.MaSo,-43} ║");
            Console.WriteLine($"║ Tên hàng:        {hangHoa.TenHang,-43} ║");
            Console.WriteLine($"║ Đơn vị tính:     {hangHoa.DonViTinh,-43} ║");
            Console.WriteLine($"║ Đơn giá:         {hangHoa.DonGia:N0,-43} ║");
            Console.WriteLine($"║ Số lượng:        {hangHoa.SoLuong,-43} ║");
            Console.WriteLine($"║ Thành tiền:      {hangHoa.ThanhTien:N0,-43} ║");
            Console.WriteLine($"║ Trạng thái:      {hangHoa.TrangThai,-43} ║");
            Console.WriteLine($"║ Phân loại giá:   {hangHoa.PhanLoaiGia,-43} ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        }

        /// <summary>
        /// Hiển thị thống kê dạng bảng
        /// </summary>
        public static void HienThiThongKe(Dictionary<string, int> thongKe, string tieuDe, string tenCot)
        {
            if (thongKe.Count == 0)
            {
                Console.WriteLine("Không có dữ liệu để thống kê!");
                return;
            }

            MenuHelper.HienThiTieuDe(tieuDe);

            Console.WriteLine($"╔══════════════════════════╦══════════════╗");
            Console.WriteLine($"║ {tenCot,-24} ║   Số lượng   ║");
            Console.WriteLine($"╠══════════════════════════╬══════════════╣");

            foreach (var item in thongKe)
            {
                Console.WriteLine($"║ {item.Key,-24} ║ {item.Value,12} ║");
            }

            Console.WriteLine($"╚══════════════════════════╩══════════════╝");
            Console.WriteLine($"Tổng số loại: {thongKe.Count}");
        }

        /// <summary>
        /// Cắt chuỗi nếu quá dài
        /// </summary>
        private static string TruncateString(string input, int maxLength)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            return input.Length <= maxLength ? input : input.Substring(0, maxLength - 3) + "...";
        }

        /// <summary>
        /// Nhập thông tin hàng hóa từ người dùng
        /// </summary>
        public static HangHoa NhapThongTinHangHoa()
        {
            MenuHelper.HienThiTieuDe("NHẬP THÔNG TIN HÀNG HÓA");

            string maSo = MenuHelper.DocChuoiKhongRong("Nhập mã số: ");
            string tenHang = MenuHelper.DocChuoiKhongRong("Nhập tên hàng: ");
            string donViTinh = MenuHelper.DocChuoiKhongRong("Nhập đơn vị tính: ");
            decimal donGia = (decimal)MenuHelper.DocSoThuc("Nhập đơn giá: ", 0, double.MaxValue);
            int soLuong = MenuHelper.DocSoNguyen("Nhập số lượng: ", 0, int.MaxValue);

            return new HangHoa(maSo, tenHang, donViTinh, donGia, soLuong);
        }

        /// <summary>
        /// Hiển thị kết quả tìm kiếm
        /// </summary>
        public static void HienThiKetQuaTimKiem(List<HangHoa> ketQua, string tuKhoa)
        {
            if (ketQua.Count == 0)
            {
                Console.WriteLine($"Không tìm thấy kết quả nào cho từ khóa: '{tuKhoa}'");
            }
            else
            {
                HienThiDanhSach(ketQua, $"KẾT QUẢ TÌM KIẾM: '{tuKhoa.ToUpper()}'");
            }
        }
    }
}
