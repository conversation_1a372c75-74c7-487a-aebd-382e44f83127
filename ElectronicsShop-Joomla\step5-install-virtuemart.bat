@echo off
title Step 5 - Install VirtueMart
color 0D

echo ========================================
echo    STEP 5: INSTALL VIRTUEMART
echo ========================================
echo.

echo VirtueMart is the e-commerce extension for Joomla
echo It will add shopping cart, products, orders functionality
echo.

echo ========================================
echo    DOWNLOADING VIRTUEMART
echo ========================================
echo.

echo Starting VirtueMart download...
powershell -ExecutionPolicy Bypass -File "download-virtuemart.ps1"

echo.
echo Press any key when VirtueMart download is complete...
pause

echo ========================================
echo    INSTALLING VIRTUEMART
echo ========================================
echo.

echo Opening Joomla Admin Panel for extension installation...
start "" "http://localhost/electronics-shop/administrator"

echo.
echo VIRTUEMART INSTALLATION STEPS:
echo ==============================
echo.
echo 1. LOGIN TO JOOMLA ADMIN:
echo    - Username: admin
echo    - Password: admin123
echo.
echo 2. INSTALL VIRTUEMART CORE:
echo    - Go to: System > Install > Extensions
echo    - Click "Upload Package File" tab
echo    - Choose: com_virtuemart_4.x.x.zip
echo    - Click "Upload & Install"
echo    - Wait for "Installation Success" message
echo.
echo 3. INSTALL PAYMENT PLUGINS:
echo    - Upload: plg_vmpayment_standard.zip
echo    - Click "Upload & Install"
echo.
echo 4. INSTALL SHIPMENT PLUGINS:
echo    - Upload: plg_vmshipment_standard.zip
echo    - Click "Upload & Install"
echo.
echo 5. INSTALL INVOICE PLUGIN:
echo    - Upload: plg_vminvoice_standard.zip
echo    - Click "Upload & Install"
echo.
echo 6. INSTALL VIETNAMESE LANGUAGE:
echo    - Upload: virtuemart_vi-VN.zip
echo    - Click "Upload & Install"
echo.
echo Press any key when all VirtueMart extensions are installed...
pause

echo ========================================
echo    CONFIGURE VIRTUEMART
echo ========================================
echo.

echo Opening VirtueMart configuration...
start "" "http://localhost/electronics-shop/administrator/index.php?option=com_virtuemart"

echo.
echo VIRTUEMART CONFIGURATION STEPS:
echo ===============================
echo.
echo 1. RUN INSTALLATION TOOL:
echo    - Go to: Components > VirtueMart
echo    - Click "Tools" > "Install Sample Data"
echo    - Click "Install" to add sample data
echo.
echo 2. CONFIGURE SHOP SETTINGS:
echo    - Go to: VirtueMart > Configuration
echo    - Shop tab:
echo      * Shop Name: Electronics Shop
echo      * Currency: Vietnamese Dong (VND)
echo      * Country: Vietnam
echo      * State: Ho Chi Minh City
echo.
echo 3. CREATE CATEGORIES:
echo    - Go to: VirtueMart > Categories
echo    - Create categories:
echo      * Laptop cu
echo      * Dien thoai cu
echo      * May tinh bang
echo      * Phu kien
echo      * Linh kien
echo.
echo Press any key when VirtueMart is configured...
pause

echo ========================================
echo    TESTING VIRTUEMART
echo ========================================
echo.

echo Testing VirtueMart shop...
start "" "http://localhost/electronics-shop/index.php?option=com_virtuemart"

echo.
echo You should now see:
echo 1. VirtueMart shop page
echo 2. Product categories
echo 3. Sample products (if installed)
echo.
echo Press any key when you can see the shop...
pause

echo ========================================
echo    STEP 5 COMPLETE!
echo ========================================
echo.
echo [SUCCESS] VirtueMart is installed and configured!
echo.
echo Your e-commerce website is ready:
echo - Frontend: http://localhost/electronics-shop
echo - Shop: http://localhost/electronics-shop/index.php?option=com_virtuemart
echo - Admin: http://localhost/electronics-shop/administrator
echo.
echo Next step: Install custom template and modules
echo.
echo Press any key to continue to Step 6...
pause
