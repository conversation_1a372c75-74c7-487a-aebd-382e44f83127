/* Product Detail Page Styles */

/* Product Images */
.product-images {
    position: sticky;
    top: 100px;
}

.main-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--border-radius);
    border: 1px solid #eee;
    cursor: zoom-in;
    transition: var(--transition-base);
}

.main-image:hover {
    transform: scale(1.02);
    box-shadow: var(--box-shadow-lg);
}

.thumbnail-images {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;
}

.thumbnail-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius);
    border: 2px solid transparent;
    cursor: pointer;
    transition: var(--transition-base);
    flex-shrink: 0;
}

.thumbnail-image:hover,
.thumbnail-image.active {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

/* Product Info */
.product-info {
    padding-left: 2rem;
}

.product-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.product-brand {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.rating-stars {
    display: flex;
    gap: 0.125rem;
}

.rating-text {
    color: var(--secondary-color);
    font-size: 0.875rem;
}

.product-price {
    margin-bottom: 1.5rem;
}

.current-price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--danger-color);
    margin-right: 1rem;
}

.original-price {
    font-size: 1.5rem;
    color: var(--secondary-color);
    text-decoration: line-through;
    margin-right: 1rem;
}

.discount-percent {
    background: var(--success-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 600;
    font-size: 1rem;
}

/* Condition Info */
.condition-info {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.condition-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.condition-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
}

.condition-badge-large {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 600;
    font-size: 1rem;
}

.condition-progress {
    margin-bottom: 1rem;
}

.condition-progress .progress {
    height: 12px;
    border-radius: 6px;
}

.condition-description {
    color: var(--secondary-color);
    line-height: 1.6;
}

/* Product Actions */
.product-actions {
    background: white;
    border: 1px solid #eee;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    position: sticky;
    top: 120px;
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.quantity-btn {
    width: 40px;
    height: 40px;
    border: 1px solid var(--primary-color);
    background: white;
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-base);
}

.quantity-btn:hover {
    background: var(--primary-color);
    color: white;
}

.quantity-input {
    width: 80px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    padding: 0.5rem;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.btn-add-cart {
    background: var(--primary-color);
    border: none;
    color: white;
    padding: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition-base);
}

.btn-add-cart:hover {
    background: #0056b3;
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.btn-buy-now {
    background: var(--success-color);
    border: none;
    color: white;
    padding: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition-base);
}

.btn-buy-now:hover {
    background: #1e7e34;
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.btn-wishlist {
    background: white;
    border: 2px solid var(--danger-color);
    color: var(--danger-color);
    padding: 0.75rem;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition-base);
}

.btn-wishlist:hover,
.btn-wishlist.active {
    background: var(--danger-color);
    color: white;
}

/* Product Tabs */
.product-tabs {
    margin-top: 3rem;
}

.nav-tabs {
    border-bottom: 2px solid #eee;
}

.nav-tabs .nav-link {
    border: none;
    color: var(--secondary-color);
    font-weight: 600;
    padding: 1rem 1.5rem;
    border-radius: 0;
    position: relative;
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background: none;
    border: none;
}

.nav-tabs .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
}

.tab-content {
    padding: 2rem 0;
}

/* Specifications Table */
.specifications-table {
    width: 100%;
    border-collapse: collapse;
}

.specifications-table th,
.specifications-table td {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    text-align: left;
}

.specifications-table th {
    background: var(--light-color);
    font-weight: 600;
    width: 30%;
}

/* Reviews Section */
.review-summary {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
}

.review-overview {
    text-align: center;
}

.avg-rating-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--warning-color);
    margin-bottom: 0.5rem;
}

.rating-distribution {
    margin-top: 1rem;
}

.rating-bar {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.rating-label {
    width: 60px;
    font-size: 0.875rem;
}

.rating-progress {
    flex: 1;
    margin: 0 1rem;
}

.rating-progress .progress {
    height: 8px;
}

.rating-count {
    width: 40px;
    text-align: right;
    font-size: 0.875rem;
    color: var(--secondary-color);
}

.review-item {
    border-bottom: 1px solid #eee;
    padding: 1.5rem 0;
}

.review-header {
    display: flex;
    justify-content: between;
    align-items: start;
    margin-bottom: 1rem;
}

.reviewer-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.reviewer-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.reviewer-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.review-date {
    color: var(--secondary-color);
    font-size: 0.875rem;
}

.review-content {
    line-height: 1.6;
    margin-bottom: 1rem;
}

.review-images {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.review-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition-base);
}

.review-image:hover {
    transform: scale(1.05);
}

/* Stock Status */
.stock-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.stock-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.stock-indicator.in-stock {
    background: var(--success-color);
}

.stock-indicator.low-stock {
    background: var(--warning-color);
}

.stock-indicator.out-of-stock {
    background: var(--danger-color);
}

.stock-text {
    font-weight: 600;
}

.stock-text.in-stock {
    color: var(--success-color);
}

.stock-text.low-stock {
    color: var(--warning-color);
}

.stock-text.out-of-stock {
    color: var(--danger-color);
}

/* Warranty Info */
.warranty-info {
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.warranty-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.warranty-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.warranty-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.warranty-features li i {
    color: var(--success-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .product-info {
        padding-left: 0;
        margin-top: 2rem;
    }
    
    .product-title {
        font-size: 1.5rem;
    }
    
    .current-price {
        font-size: 2rem;
    }
    
    .original-price {
        font-size: 1.25rem;
    }
    
    .product-actions {
        position: static;
        margin-top: 2rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .thumbnail-images {
        justify-content: center;
    }
    
    .review-header {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .reviewer-info {
        gap: 0.75rem;
    }
    
    .reviewer-avatar {
        width: 40px;
        height: 40px;
    }
}

@media (max-width: 576px) {
    .product-title {
        font-size: 1.25rem;
    }
    
    .current-price {
        font-size: 1.75rem;
    }
    
    .condition-info,
    .product-actions,
    .warranty-info {
        padding: 1rem;
    }
    
    .quantity-selector {
        justify-content: center;
    }
    
    .nav-tabs .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
    
    .review-summary {
        padding: 1.5rem;
    }
    
    .avg-rating-number {
        font-size: 2.5rem;
    }
}
