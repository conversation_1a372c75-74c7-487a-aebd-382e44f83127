/*
===============================================================================
    ỨNG DỤNG QUẢN LÝ HỌC SINH - DANH SÁCH LIÊN KẾT ĐƠN
    Phiên bản C++ Console với giao diện đẹp và đầy đủ chức năng
===============================================================================

Sinh viên thực hiện: Huỳnh Thái Bảo
Ngày sinh: 28/04/1994
Email: <EMAIL>
Điện thoại: 0355771075
Lớp: DX23TT11
Mã sinh viên: 170123488

===============================================================================
*/

#include <iostream>
#include <iomanip>
#include <cstring>
#include <string>
#include <algorithm>
#include <vector>
#include <fstream>
#include <sstream>
#include <cstdlib>
#include <ctime>

#ifdef _WIN32
    #include <windows.h>
    #include <conio.h>
#else
    #include <termios.h>
    #include <unistd.h>
#endif

using namespace std;

// Màu sắc cho console
namespace Color {
    const string RESET = "\033[0m";
    const string RED = "\033[31m";
    const string GREEN = "\033[32m";
    const string YELLOW = "\033[33m";
    const string BLUE = "\033[34m";
    const string MAGENTA = "\033[35m";
    const string CYAN = "\033[36m";
    const string WHITE = "\033[37m";
    const string BOLD = "\033[1m";
    const string UNDERLINE = "\033[4m";
}

// Cấu trúc dữ liệu học sinh
struct HocSinh {
    char MaSo[10];           // Mã số học sinh
    char HoTen[50];          // Họ tên học sinh
    int NamSinh;             // Năm sinh
    int Khoi;                // Khối (10, 11, 12)
    char Lop[10];            // Lớp
    float DiemTrungBinh;     // Điểm trung bình
    
    // Constructor mặc định
    HocSinh() {
        strcpy(MaSo, "");
        strcpy(HoTen, "");
        NamSinh = 0;
        Khoi = 0;
        strcpy(Lop, "");
        DiemTrungBinh = 0.0;
    }
    
    // Constructor có tham số
    HocSinh(const char* maSo, const char* hoTen, int namSinh, int khoi, const char* lop, float diemTB) {
        strcpy(MaSo, maSo);
        strcpy(HoTen, hoTen);
        NamSinh = namSinh;
        Khoi = khoi;
        strcpy(Lop, lop);
        DiemTrungBinh = diemTB;
    }
};

// Cấu trúc nút trong danh sách liên kết đơn
struct Node {
    HocSinh data;    // Dữ liệu học sinh
    Node* next;      // Con trỏ trỏ đến nút tiếp theo
    
    // Constructor
    Node(HocSinh hs) {
        data = hs;
        next = nullptr;
    }
};

// Lớp quản lý danh sách liên kết đơn
class StudentLinkedList {
private:
    Node* head;
    int size;
    
public:
    // Constructor
    StudentLinkedList() : head(nullptr), size(0) {}
    
    // Destructor
    ~StudentLinkedList() {
        clear();
    }
    
    // Thêm học sinh vào cuối danh sách
    void insertAtEnd(const HocSinh& hs);
    
    // Xóa học sinh theo mã số
    bool deleteByMaSo(const char* maSo);
    
    // Tìm kiếm học sinh theo mã số
    Node* searchByMaSo(const char* maSo);
    
    // Hiển thị tất cả học sinh
    void displayAll();
    
    // Sắp xếp theo mã số
    void sortByMaSo();
    
    // Sắp xếp theo điểm trung bình
    void sortByDiemTB();
    
    // Thống kê dữ liệu
    void showStatistics();
    
    // Lọc theo khối
    void filterByKhoi(int khoi);
    
    // Lọc theo điểm
    void filterByDiem(float nguong, bool isHigher);
    
    // Cập nhật thông tin học sinh
    bool updateStudent(const char* maSo);
    
    // Xóa tất cả
    void clear();
    
    // Lấy kích thước
    int getSize() const { return size; }
    
    // Kiểm tra rỗng
    bool isEmpty() const { return head == nullptr; }
    
    // Lưu dữ liệu ra file
    void saveToFile(const string& filename);
    
    // Tải dữ liệu từ file
    void loadFromFile(const string& filename);
    
    // Hiển thị visualization của linked list
    void showVisualization();
};

// Các hàm tiện ích
void clearScreen();
void pauseScreen();
void printHeader();
void printStudentInfo();
void printSeparator(char ch = '=', int length = 80);
HocSinh inputStudentInfo();
bool validateStudentInput(const HocSinh& hs);
void displayMenu();
void displaySubMenu(const string& title, const vector<string>& options);
int getChoice(int min, int max);
void showWelcomeScreen();
void showGoodbyeScreen();

// Biến toàn cục
StudentLinkedList studentList;

// Hàm main
int main() {
    // Thiết lập console
    #ifdef _WIN32
        SetConsoleOutputCP(CP_UTF8);
        SetConsoleCP(CP_UTF8);
    #endif
    
    // Hiển thị màn hình chào mừng
    showWelcomeScreen();
    
    // Thêm dữ liệu mẫu
    studentList.insertAtEnd(HocSinh("HS001", "Nguyễn Văn An", 2005, 12, "A1", 8.5));
    studentList.insertAtEnd(HocSinh("HS002", "Trần Thị Bình", 2006, 11, "B2", 7.2));
    studentList.insertAtEnd(HocSinh("HS003", "Lê Văn Cường", 2007, 10, "C3", 6.8));
    studentList.insertAtEnd(HocSinh("HS004", "Phạm Thị Dung", 2005, 12, "A2", 9.1));
    studentList.insertAtEnd(HocSinh("HS005", "Hoàng Văn Em", 2006, 11, "B1", 5.5));
    
    int choice;
    do {
        clearScreen();
        printHeader();
        displayMenu();
        choice = getChoice(0, 9);
        
        switch (choice) {
            case 1: {
                clearScreen();
                cout << Color::CYAN << Color::BOLD << "➕ THÊM HỌC SINH MỚI" << Color::RESET << endl;
                printSeparator('-', 50);
                
                HocSinh hs = inputStudentInfo();
                if (validateStudentInput(hs)) {
                    // Kiểm tra trùng mã số
                    if (studentList.searchByMaSo(hs.MaSo)) {
                        cout << Color::RED << "❌ Mã số học sinh đã tồn tại!" << Color::RESET << endl;
                    } else {
                        studentList.insertAtEnd(hs);
                        cout << Color::GREEN << "✅ Thêm học sinh thành công!" << Color::RESET << endl;
                    }
                } else {
                    cout << Color::RED << "❌ Dữ liệu không hợp lệ!" << Color::RESET << endl;
                }
                pauseScreen();
                break;
            }
            
            case 2: {
                clearScreen();
                cout << Color::BLUE << Color::BOLD << "📋 DANH SÁCH HỌC SINH" << Color::RESET << endl;
                printSeparator('-', 50);
                studentList.displayAll();
                pauseScreen();
                break;
            }
            
            case 3: {
                clearScreen();
                cout << Color::MAGENTA << Color::BOLD << "🔍 TÌM KIẾM HỌC SINH" << Color::RESET << endl;
                printSeparator('-', 50);
                
                char maSo[10];
                cout << "Nhập mã số cần tìm: ";
                cin >> maSo;
                
                Node* found = studentList.searchByMaSo(maSo);
                if (found) {
                    cout << Color::GREEN << "✅ Tìm thấy học sinh:" << Color::RESET << endl;
                    printSeparator('-', 80);
                    cout << left << setw(8) << "Mã số" 
                         << setw(25) << "Họ tên"
                         << setw(10) << "Năm sinh"
                         << setw(6) << "Khối"
                         << setw(8) << "Lớp"
                         << setw(8) << "Điểm TB" << endl;
                    printSeparator('-', 80);
                    cout << left << setw(8) << found->data.MaSo 
                         << setw(25) << found->data.HoTen
                         << setw(10) << found->data.NamSinh
                         << setw(6) << found->data.Khoi
                         << setw(8) << found->data.Lop
                         << setw(8) << fixed << setprecision(1) << found->data.DiemTrungBinh << endl;
                } else {
                    cout << Color::RED << "❌ Không tìm thấy học sinh!" << Color::RESET << endl;
                }
                pauseScreen();
                break;
            }
            
            case 4: {
                clearScreen();
                cout << Color::RED << Color::BOLD << "🗑️ XÓA HỌC SINH" << Color::RESET << endl;
                printSeparator('-', 50);
                
                char maSo[10];
                cout << "Nhập mã số cần xóa: ";
                cin >> maSo;
                
                if (studentList.deleteByMaSo(maSo)) {
                    cout << Color::GREEN << "✅ Xóa học sinh thành công!" << Color::RESET << endl;
                } else {
                    cout << Color::RED << "❌ Không tìm thấy học sinh!" << Color::RESET << endl;
                }
                pauseScreen();
                break;
            }
            
            case 5: {
                clearScreen();
                cout << Color::YELLOW << Color::BOLD << "✏️ CẬP NHẬT THÔNG TIN HỌC SINH" << Color::RESET << endl;
                printSeparator('-', 50);
                
                char maSo[10];
                cout << "Nhập mã số cần cập nhật: ";
                cin >> maSo;
                
                if (studentList.updateStudent(maSo)) {
                    cout << Color::GREEN << "✅ Cập nhật thành công!" << Color::RESET << endl;
                } else {
                    cout << Color::RED << "❌ Không tìm thấy học sinh!" << Color::RESET << endl;
                }
                pauseScreen();
                break;
            }
            
            case 6: {
                clearScreen();
                cout << Color::CYAN << Color::BOLD << "🔄 SẮP XẾP DANH SÁCH" << Color::RESET << endl;
                printSeparator('-', 50);
                
                vector<string> sortOptions = {
                    "Sắp xếp theo mã số",
                    "Sắp xếp theo điểm trung bình (giảm dần)"
                };
                displaySubMenu("Chọn kiểu sắp xếp:", sortOptions);
                
                int sortChoice = getChoice(1, 2);
                if (sortChoice == 1) {
                    studentList.sortByMaSo();
                    cout << Color::GREEN << "✅ Đã sắp xếp theo mã số!" << Color::RESET << endl;
                } else {
                    studentList.sortByDiemTB();
                    cout << Color::GREEN << "✅ Đã sắp xếp theo điểm TB!" << Color::RESET << endl;
                }
                pauseScreen();
                break;
            }
            
            case 7: {
                clearScreen();
                cout << Color::BLUE << Color::BOLD << "📊 THỐNG KÊ DỮ LIỆU" << Color::RESET << endl;
                printSeparator('-', 50);
                studentList.showStatistics();
                pauseScreen();
                break;
            }
            
            case 8: {
                clearScreen();
                cout << Color::MAGENTA << Color::BOLD << "🔍 TRÍCH LỌC DỮ LIỆU" << Color::RESET << endl;
                printSeparator('-', 50);
                
                vector<string> filterOptions = {
                    "Lọc theo khối",
                    "Lọc theo điểm trung bình"
                };
                displaySubMenu("Chọn kiểu lọc:", filterOptions);
                
                int filterChoice = getChoice(1, 2);
                if (filterChoice == 1) {
                    int khoi;
                    cout << "Nhập khối (10/11/12): ";
                    cin >> khoi;
                    studentList.filterByKhoi(khoi);
                } else {
                    float nguong;
                    int op;
                    cout << "Nhập điểm ngưỡng: ";
                    cin >> nguong;
                    cout << "1. >= ngưỡng, 2. < ngưỡng: ";
                    cin >> op;
                    studentList.filterByDiem(nguong, op == 1);
                }
                pauseScreen();
                break;
            }
            
            case 9: {
                clearScreen();
                cout << Color::CYAN << Color::BOLD << "🔗 MINH HỌA CẤU TRÚC LIÊN KẾT" << Color::RESET << endl;
                printSeparator('-', 50);
                studentList.showVisualization();
                pauseScreen();
                break;
            }
            
            case 0:
                showGoodbyeScreen();
                break;
                
            default:
                cout << Color::RED << "❌ Lựa chọn không hợp lệ!" << Color::RESET << endl;
                pauseScreen();
        }
    } while (choice != 0);
    
    return 0;
}

// ===============================================================================
//                          IMPLEMENTATION CÁC HÀM
// ===============================================================================

// Hàm xóa màn hình
void clearScreen() {
    #ifdef _WIN32
        system("cls");
    #else
        system("clear");
    #endif
}

// Hàm tạm dừng màn hình
void pauseScreen() {
    cout << "\n" << Color::YELLOW << "Nhấn Enter để tiếp tục..." << Color::RESET;
    cin.ignore();
    cin.get();
}

// Hàm in header chương trình
void printHeader() {
    cout << Color::BOLD << Color::BLUE;
    printSeparator('=', 80);
    cout << "                    🎓 QUẢN LÝ HỌC SINH - DANH SÁCH LIÊN KẾT ĐƠN" << endl;
    cout << "                         Sinh viên: Huỳnh Thái Bảo - DX23TT11" << endl;
    printSeparator('=', 80);
    cout << Color::RESET;
}

// Hàm in thông tin sinh viên
void printStudentInfo() {
    cout << Color::CYAN << Color::BOLD;
    cout << "👨‍🎓 THÔNG TIN SINH VIÊN THỰC HIỆN:" << endl;
    printSeparator('-', 50);
    cout << Color::RESET;
    cout << "Họ tên: " << Color::BOLD << "Huỳnh Thái Bảo" << Color::RESET << endl;
    cout << "Ngày sinh: " << Color::BOLD << "28/04/1994" << Color::RESET << endl;
    cout << "Email: " << Color::BOLD << "<EMAIL>" << Color::RESET << endl;
    cout << "Điện thoại: " << Color::BOLD << "0355771075" << Color::RESET << endl;
    cout << "Lớp: " << Color::BOLD << "DX23TT11" << Color::RESET << endl;
    cout << "Mã sinh viên: " << Color::BOLD << "170123488" << Color::RESET << endl;
    printSeparator('-', 50);
}

// Hàm in đường phân cách
void printSeparator(char ch, int length) {
    cout << string(length, ch) << endl;
}

// Hàm hiển thị menu chính
void displayMenu() {
    cout << Color::GREEN << Color::BOLD << "📋 MENU CHÍNH:" << Color::RESET << endl;
    printSeparator('-', 40);
    cout << Color::CYAN << "1. ➕ Thêm học sinh mới" << Color::RESET << endl;
    cout << Color::BLUE << "2. 📋 Hiển thị danh sách học sinh" << Color::RESET << endl;
    cout << Color::MAGENTA << "3. 🔍 Tìm kiếm học sinh" << Color::RESET << endl;
    cout << Color::RED << "4. 🗑️ Xóa học sinh" << Color::RESET << endl;
    cout << Color::YELLOW << "5. ✏️ Cập nhật thông tin học sinh" << Color::RESET << endl;
    cout << Color::CYAN << "6. 🔄 Sắp xếp danh sách" << Color::RESET << endl;
    cout << Color::BLUE << "7. 📊 Thống kê dữ liệu" << Color::RESET << endl;
    cout << Color::MAGENTA << "8. 🔍 Trích lọc dữ liệu" << Color::RESET << endl;
    cout << Color::CYAN << "9. 🔗 Minh họa cấu trúc liên kết" << Color::RESET << endl;
    cout << Color::RED << "0. 🚪 Thoát chương trình" << Color::RESET << endl;
    printSeparator('-', 40);
    cout << Color::BOLD << "Nhập lựa chọn (0-9): " << Color::RESET;
}

// Hàm hiển thị sub menu
void displaySubMenu(const string& title, const vector<string>& options) {
    cout << Color::BOLD << title << Color::RESET << endl;
    printSeparator('-', 30);
    for (size_t i = 0; i < options.size(); i++) {
        cout << Color::CYAN << (i + 1) << ". " << options[i] << Color::RESET << endl;
    }
    printSeparator('-', 30);
    cout << Color::BOLD << "Nhập lựa chọn: " << Color::RESET;
}

// Hàm lấy lựa chọn từ người dùng
int getChoice(int min, int max) {
    int choice;
    while (true) {
        cin >> choice;
        if (cin.fail() || choice < min || choice > max) {
            cin.clear();
            cin.ignore(10000, '\n');
            cout << Color::RED << "❌ Lựa chọn không hợp lệ! Vui lòng nhập từ "
                 << min << " đến " << max << ": " << Color::RESET;
        } else {
            cin.ignore();
            return choice;
        }
    }
}

// Hàm nhập thông tin học sinh
HocSinh inputStudentInfo() {
    HocSinh hs;

    cout << Color::BOLD << "Nhập thông tin học sinh:" << Color::RESET << endl;
    printSeparator('-', 30);

    cout << "Mã số học sinh: ";
    cin >> hs.MaSo;
    cin.ignore();

    cout << "Họ tên: ";
    cin.getline(hs.HoTen, 50);

    cout << "Năm sinh: ";
    cin >> hs.NamSinh;

    cout << "Khối (10/11/12): ";
    cin >> hs.Khoi;

    cout << "Lớp: ";
    cin >> hs.Lop;

    cout << "Điểm trung bình: ";
    cin >> hs.DiemTrungBinh;

    return hs;
}

// Hàm validate dữ liệu đầu vào
bool validateStudentInput(const HocSinh& hs) {
    if (strlen(hs.MaSo) < 3) {
        cout << Color::RED << "❌ Mã số phải có ít nhất 3 ký tự!" << Color::RESET << endl;
        return false;
    }

    if (strlen(hs.HoTen) < 2) {
        cout << Color::RED << "❌ Họ tên phải có ít nhất 2 ký tự!" << Color::RESET << endl;
        return false;
    }

    if (hs.NamSinh < 1990 || hs.NamSinh > 2010) {
        cout << Color::RED << "❌ Năm sinh phải từ 1990 đến 2010!" << Color::RESET << endl;
        return false;
    }

    if (hs.Khoi < 10 || hs.Khoi > 12) {
        cout << Color::RED << "❌ Khối phải là 10, 11 hoặc 12!" << Color::RESET << endl;
        return false;
    }

    if (strlen(hs.Lop) < 1) {
        cout << Color::RED << "❌ Lớp không được để trống!" << Color::RESET << endl;
        return false;
    }

    if (hs.DiemTrungBinh < 0 || hs.DiemTrungBinh > 10) {
        cout << Color::RED << "❌ Điểm trung bình phải từ 0 đến 10!" << Color::RESET << endl;
        return false;
    }

    return true;
}

// Hàm hiển thị màn hình chào mừng
void showWelcomeScreen() {
    clearScreen();
    cout << Color::BOLD << Color::BLUE;
    printSeparator('*', 80);
    cout << "                                                                                " << endl;
    cout << "    ██████╗ ██╗   ██╗ █████╗ ███╗   ██╗    ██╗  ██╗   ██╗    ██╗  ██╗███████╗" << endl;
    cout << "   ██╔═══██╗██║   ██║██╔══██╗████╗  ██║    ██║  ╚██╗ ██╔╝    ██║  ██║██╔════╝" << endl;
    cout << "   ██║   ██║██║   ██║███████║██╔██╗ ██║    ██║   ╚████╔╝     ███████║███████╗" << endl;
    cout << "   ██║▄▄ ██║██║   ██║██╔══██║██║╚██╗██║    ██║    ╚██╔╝      ██╔══██║╚════██║" << endl;
    cout << "   ╚██████╔╝╚██████╔╝██║  ██║██║ ╚████║    ███████╗██║       ██║  ██║███████║" << endl;
    cout << "    ╚══▀▀═╝  ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═══╝    ╚══════╝╚═╝       ╚═╝  ╚═╝╚══════╝" << endl;
    cout << "                                                                                " << endl;
    printSeparator('*', 80);
    cout << Color::RESET;

    cout << Color::CYAN << Color::BOLD;
    cout << "                    🎓 CHƯƠNG TRÌNH QUẢN LÝ HỌC SINH" << endl;
    cout << "                   Sử dụng cấu trúc DANH SÁCH LIÊN KẾT ĐƠN" << endl;
    cout << Color::RESET << endl;

    printStudentInfo();

    cout << Color::GREEN << Color::BOLD;
    cout << "🚀 Chương trình đã sẵn sàng với 5 học sinh mẫu!" << endl;
    cout << Color::RESET;

    pauseScreen();
}

// Hàm hiển thị màn hình tạm biệt
void showGoodbyeScreen() {
    clearScreen();
    cout << Color::BOLD << Color::MAGENTA;
    printSeparator('=', 60);
    cout << "                    🎉 CẢM ƠN BẠN ĐÃ SỬ DỤNG!" << endl;
    cout << "                   📚 Đồ án Cấu trúc dữ liệu" << endl;
    cout << "                    👨‍🎓 Huỳnh Thái Bảo - DX23TT11" << endl;
    printSeparator('=', 60);
    cout << Color::RESET << endl;

    cout << Color::CYAN;
    cout << "✨ Chương trình đã minh họa thành công:" << endl;
    cout << "   • Cấu trúc dữ liệu Danh Sách Liên Kết Đơn" << endl;
    cout << "   • Các thao tác CRUD (Create, Read, Update, Delete)" << endl;
    cout << "   • Tìm kiếm, sắp xếp và thống kê dữ liệu" << endl;
    cout << "   • Visualization cấu trúc liên kết" << endl;
    cout << Color::RESET << endl;

    cout << Color::YELLOW << "📧 Liên hệ: <EMAIL>" << endl;
    cout << "📱 Điện thoại: 0355771075" << Color::RESET << endl << endl;
}

// ===============================================================================
//                    IMPLEMENTATION CLASS StudentLinkedList
// ===============================================================================

// Thêm học sinh vào cuối danh sách
void StudentLinkedList::insertAtEnd(const HocSinh& hs) {
    Node* newNode = new Node(hs);

    if (head == nullptr) {
        head = newNode;
    } else {
        Node* current = head;
        while (current->next != nullptr) {
            current = current->next;
        }
        current->next = newNode;
    }
    size++;
}

// Xóa học sinh theo mã số
bool StudentLinkedList::deleteByMaSo(const char* maSo) {
    if (head == nullptr) return false;

    // Nếu nút đầu chứa mã số cần xóa
    if (strcmp(head->data.MaSo, maSo) == 0) {
        Node* temp = head;
        head = head->next;
        delete temp;
        size--;
        return true;
    }

    // Tìm nút chứa mã số cần xóa
    Node* current = head;
    while (current->next != nullptr && strcmp(current->next->data.MaSo, maSo) != 0) {
        current = current->next;
    }

    if (current->next != nullptr) {
        Node* nodeToDelete = current->next;
        current->next = nodeToDelete->next;
        delete nodeToDelete;
        size--;
        return true;
    }

    return false;
}

// Tìm kiếm học sinh theo mã số
Node* StudentLinkedList::searchByMaSo(const char* maSo) {
    Node* current = head;
    while (current != nullptr) {
        if (strcmp(current->data.MaSo, maSo) == 0) {
            return current;
        }
        current = current->next;
    }
    return nullptr;
}

// Hiển thị tất cả học sinh
void StudentLinkedList::displayAll() {
    if (head == nullptr) {
        cout << Color::YELLOW << "📝 Danh sách rỗng!" << Color::RESET << endl;
        return;
    }

    cout << Color::BOLD;
    printSeparator('=', 85);
    cout << left << setw(8) << "Mã số"
         << setw(25) << "Họ tên"
         << setw(10) << "Năm sinh"
         << setw(8) << "Khối"
         << setw(10) << "Lớp"
         << setw(12) << "Điểm TB" << endl;
    printSeparator('-', 85);
    cout << Color::RESET;

    Node* current = head;
    int count = 0;
    while (current != nullptr) {
        cout << left << setw(8) << current->data.MaSo
             << setw(25) << current->data.HoTen
             << setw(10) << current->data.NamSinh
             << setw(8) << current->data.Khoi
             << setw(10) << current->data.Lop
             << setw(12) << fixed << setprecision(1) << current->data.DiemTrungBinh << endl;
        current = current->next;
        count++;
    }

    printSeparator('=', 85);
    cout << Color::BOLD << Color::BLUE << "📊 Tổng số học sinh: " << count << Color::RESET << endl;
}

// Sắp xếp theo mã số (Bubble Sort)
void StudentLinkedList::sortByMaSo() {
    if (head == nullptr || head->next == nullptr) return;

    bool swapped;
    do {
        swapped = false;
        Node* current = head;

        while (current->next != nullptr) {
            if (strcmp(current->data.MaSo, current->next->data.MaSo) > 0) {
                // Hoán đổi dữ liệu
                HocSinh temp = current->data;
                current->data = current->next->data;
                current->next->data = temp;
                swapped = true;
            }
            current = current->next;
        }
    } while (swapped);
}

// Sắp xếp theo điểm trung bình (giảm dần)
void StudentLinkedList::sortByDiemTB() {
    if (head == nullptr || head->next == nullptr) return;

    bool swapped;
    do {
        swapped = false;
        Node* current = head;

        while (current->next != nullptr) {
            if (current->data.DiemTrungBinh < current->next->data.DiemTrungBinh) {
                // Hoán đổi dữ liệu
                HocSinh temp = current->data;
                current->data = current->next->data;
                current->next->data = temp;
                swapped = true;
            }
            current = current->next;
        }
    } while (swapped);
}

// Thống kê dữ liệu
void StudentLinkedList::showStatistics() {
    if (head == nullptr) {
        cout << Color::YELLOW << "📝 Không có dữ liệu để thống kê!" << Color::RESET << endl;
        return;
    }

    // Thống kê theo khối
    int count10 = 0, count11 = 0, count12 = 0;
    float totalDiem = 0;
    float maxDiem = head->data.DiemTrungBinh;
    float minDiem = head->data.DiemTrungBinh;
    Node* maxNode = head;
    Node* minNode = head;
    int passed = 0;

    Node* current = head;
    while (current != nullptr) {
        // Đếm theo khối
        switch (current->data.Khoi) {
            case 10: count10++; break;
            case 11: count11++; break;
            case 12: count12++; break;
        }

        // Tính điểm
        totalDiem += current->data.DiemTrungBinh;
        if (current->data.DiemTrungBinh >= 5.0) passed++;

        // Tìm max/min
        if (current->data.DiemTrungBinh > maxDiem) {
            maxDiem = current->data.DiemTrungBinh;
            maxNode = current;
        }
        if (current->data.DiemTrungBinh < minDiem) {
            minDiem = current->data.DiemTrungBinh;
            minNode = current;
        }

        current = current->next;
    }

    float avgDiem = totalDiem / size;
    float passRate = (float)passed / size * 100;

    cout << Color::BOLD << Color::BLUE;
    printSeparator('=', 70);
    cout << "                        📊 THỐNG KÊ DỮ LIỆU HỌC SINH" << endl;
    printSeparator('=', 70);
    cout << Color::RESET;

    cout << Color::CYAN << Color::BOLD << "📈 TỔNG QUAN:" << Color::RESET << endl;
    cout << "• Tổng số học sinh: " << Color::BOLD << size << Color::RESET << endl;
    cout << "• Điểm trung bình chung: " << Color::BOLD << fixed << setprecision(2) << avgDiem << Color::RESET << endl;
    cout << "• Tỉ lệ đạt yêu cầu (≥5.0): " << Color::BOLD << fixed << setprecision(1) << passRate << "%" << Color::RESET << endl;
    cout << endl;

    cout << Color::GREEN << Color::BOLD << "📚 THEO KHỐI:" << Color::RESET << endl;
    cout << "• Khối 10: " << Color::BOLD << count10 << Color::RESET << " học sinh" << endl;
    cout << "• Khối 11: " << Color::BOLD << count11 << Color::RESET << " học sinh" << endl;
    cout << "• Khối 12: " << Color::BOLD << count12 << Color::RESET << " học sinh" << endl;
    cout << endl;

    cout << Color::YELLOW << Color::BOLD << "🏆 HỌC SINH XUẤT SẮC:" << Color::RESET << endl;
    cout << "• Điểm cao nhất: " << Color::BOLD << Color::GREEN << fixed << setprecision(1) << maxDiem << Color::RESET << endl;
    cout << "• " << Color::BOLD << maxNode->data.HoTen << Color::RESET << " (" << maxNode->data.MaSo << ") - Lớp " << maxNode->data.Lop << endl;
    cout << endl;

    cout << Color::RED << Color::BOLD << "⚠️ HỌC SINH CẦN HỖ TRỢ:" << Color::RESET << endl;
    cout << "• Điểm thấp nhất: " << Color::BOLD << Color::RED << fixed << setprecision(1) << minDiem << Color::RESET << endl;
    cout << "• " << Color::BOLD << minNode->data.HoTen << Color::RESET << " (" << minNode->data.MaSo << ") - Lớp " << minNode->data.Lop << endl;
    cout << endl;

    cout << Color::MAGENTA << Color::BOLD << "📊 KẾT QUẢ HỌC TẬP:" << Color::RESET << endl;
    cout << "• Học sinh đạt (≥5.0): " << Color::BOLD << Color::GREEN << passed << Color::RESET << " (" << fixed << setprecision(1) << passRate << "%)" << endl;
    cout << "• Học sinh không đạt (<5.0): " << Color::BOLD << Color::RED << (size - passed) << Color::RESET << " (" << (100 - passRate) << "%)" << endl;

    printSeparator('=', 70);
}

// Lọc theo khối
void StudentLinkedList::filterByKhoi(int khoi) {
    if (head == nullptr) {
        cout << Color::YELLOW << "📝 Danh sách rỗng!" << Color::RESET << endl;
        return;
    }

    cout << Color::BOLD << Color::BLUE << "🔍 Danh sách học sinh khối " << khoi << ":" << Color::RESET << endl;
    printSeparator('-', 85);
    cout << left << setw(8) << "Mã số"
         << setw(25) << "Họ tên"
         << setw(10) << "Năm sinh"
         << setw(8) << "Khối"
         << setw(10) << "Lớp"
         << setw(12) << "Điểm TB" << endl;
    printSeparator('-', 85);

    Node* current = head;
    int count = 0;
    while (current != nullptr) {
        if (current->data.Khoi == khoi) {
            cout << left << setw(8) << current->data.MaSo
                 << setw(25) << current->data.HoTen
                 << setw(10) << current->data.NamSinh
                 << setw(8) << current->data.Khoi
                 << setw(10) << current->data.Lop
                 << setw(12) << fixed << setprecision(1) << current->data.DiemTrungBinh << endl;
            count++;
        }
        current = current->next;
    }

    printSeparator('=', 85);
    if (count == 0) {
        cout << Color::YELLOW << "📝 Không có học sinh nào trong khối " << khoi << "!" << Color::RESET << endl;
    } else {
        cout << Color::BOLD << Color::BLUE << "📊 Tổng số học sinh khối " << khoi << ": " << count << Color::RESET << endl;
    }
}

// Lọc theo điểm
void StudentLinkedList::filterByDiem(float nguong, bool isHigher) {
    if (head == nullptr) {
        cout << Color::YELLOW << "📝 Danh sách rỗng!" << Color::RESET << endl;
        return;
    }

    cout << Color::BOLD << Color::BLUE << "🔍 Danh sách học sinh có điểm TB "
         << (isHigher ? ">= " : "< ") << nguong << ":" << Color::RESET << endl;
    printSeparator('-', 85);
    cout << left << setw(8) << "Mã số"
         << setw(25) << "Họ tên"
         << setw(10) << "Năm sinh"
         << setw(8) << "Khối"
         << setw(10) << "Lớp"
         << setw(12) << "Điểm TB" << endl;
    printSeparator('-', 85);

    Node* current = head;
    int count = 0;
    while (current != nullptr) {
        bool condition = isHigher ? (current->data.DiemTrungBinh >= nguong) : (current->data.DiemTrungBinh < nguong);
        if (condition) {
            cout << left << setw(8) << current->data.MaSo
                 << setw(25) << current->data.HoTen
                 << setw(10) << current->data.NamSinh
                 << setw(8) << current->data.Khoi
                 << setw(10) << current->data.Lop
                 << setw(12) << fixed << setprecision(1) << current->data.DiemTrungBinh << endl;
            count++;
        }
        current = current->next;
    }

    printSeparator('=', 85);
    if (count == 0) {
        cout << Color::YELLOW << "📝 Không có học sinh nào thỏa mãn điều kiện!" << Color::RESET << endl;
    } else {
        cout << Color::BOLD << Color::BLUE << "📊 Tổng số học sinh thỏa mãn: " << count << Color::RESET << endl;
    }
}

// Cập nhật thông tin học sinh
bool StudentLinkedList::updateStudent(const char* maSo) {
    Node* node = searchByMaSo(maSo);
    if (node == nullptr) {
        return false;
    }

    cout << Color::BOLD << "Thông tin hiện tại:" << Color::RESET << endl;
    printSeparator('-', 85);
    cout << left << setw(8) << "Mã số"
         << setw(25) << "Họ tên"
         << setw(10) << "Năm sinh"
         << setw(8) << "Khối"
         << setw(10) << "Lớp"
         << setw(12) << "Điểm TB" << endl;
    printSeparator('-', 85);
    cout << left << setw(8) << node->data.MaSo
         << setw(25) << node->data.HoTen
         << setw(10) << node->data.NamSinh
         << setw(8) << node->data.Khoi
         << setw(10) << node->data.Lop
         << setw(12) << fixed << setprecision(1) << node->data.DiemTrungBinh << endl;
    printSeparator('=', 85);

    cout << Color::BOLD << "\nNhập thông tin mới:" << Color::RESET << endl;
    cout << "Họ tên: ";
    cin.ignore();
    cin.getline(node->data.HoTen, 50);
    cout << "Năm sinh: ";
    cin >> node->data.NamSinh;
    cout << "Khối: ";
    cin >> node->data.Khoi;
    cout << "Lớp: ";
    cin >> node->data.Lop;
    cout << "Điểm trung bình: ";
    cin >> node->data.DiemTrungBinh;

    return true;
}

// Xóa tất cả
void StudentLinkedList::clear() {
    while (head != nullptr) {
        Node* temp = head;
        head = head->next;
        delete temp;
    }
    size = 0;
}

// Hiển thị visualization của linked list
void StudentLinkedList::showVisualization() {
    if (head == nullptr) {
        cout << Color::YELLOW << "📝 Danh sách rỗng - Không có gì để hiển thị!" << Color::RESET << endl;
        return;
    }

    cout << Color::BOLD << Color::CYAN << "🔗 CẤU TRÚC DANH SÁCH LIÊN KẾT ĐƠN:" << Color::RESET << endl;
    printSeparator('=', 100);

    cout << Color::BOLD << "HEAD" << Color::RESET;

    Node* current = head;
    while (current != nullptr) {
        cout << Color::CYAN << " -> " << Color::RESET;
        cout << Color::BOLD << "[" << current->data.MaSo << "|";
        if (current->next != nullptr) {
            cout << "next]" << Color::RESET;
        } else {
            cout << Color::RED << "NULL]" << Color::RESET;
        }
        current = current->next;
    }

    cout << endl << endl;

    // Hiển thị chi tiết từng node
    cout << Color::BOLD << "CHI TIẾT CÁC NODE:" << Color::RESET << endl;
    printSeparator('-', 100);

    current = head;
    int nodeIndex = 1;
    while (current != nullptr) {
        cout << Color::BOLD << Color::BLUE << "Node " << nodeIndex << ":" << Color::RESET << endl;
        cout << "  📋 Dữ liệu: " << current->data.MaSo << " - " << current->data.HoTen << endl;
        cout << "  🔗 Next: ";
        if (current->next != nullptr) {
            cout << Color::GREEN << "Trỏ đến Node " << (nodeIndex + 1) << Color::RESET << endl;
        } else {
            cout << Color::RED << "NULL (Kết thúc danh sách)" << Color::RESET << endl;
        }
        cout << endl;

        current = current->next;
        nodeIndex++;
    }

    printSeparator('=', 100);
    cout << Color::BOLD << Color::MAGENTA << "📊 Tổng số node: " << size << Color::RESET << endl;
}

// Lưu dữ liệu ra file
void StudentLinkedList::saveToFile(const string& filename) {
    ofstream file(filename);
    if (!file.is_open()) {
        cout << Color::RED << "❌ Không thể mở file để ghi!" << Color::RESET << endl;
        return;
    }

    Node* current = head;
    while (current != nullptr) {
        file << current->data.MaSo << ","
             << current->data.HoTen << ","
             << current->data.NamSinh << ","
             << current->data.Khoi << ","
             << current->data.Lop << ","
             << current->data.DiemTrungBinh << endl;
        current = current->next;
    }

    file.close();
    cout << Color::GREEN << "✅ Đã lưu " << size << " học sinh vào file: " << filename << Color::RESET << endl;
}

// Tải dữ liệu từ file
void StudentLinkedList::loadFromFile(const string& filename) {
    ifstream file(filename);
    if (!file.is_open()) {
        cout << Color::RED << "❌ Không thể mở file để đọc!" << Color::RESET << endl;
        return;
    }

    clear(); // Xóa dữ liệu hiện tại

    string line;
    int count = 0;
    while (getline(file, line)) {
        stringstream ss(line);
        string item;
        HocSinh hs;

        // Parse CSV
        getline(ss, item, ',');
        strcpy(hs.MaSo, item.c_str());

        getline(ss, item, ',');
        strcpy(hs.HoTen, item.c_str());

        getline(ss, item, ',');
        hs.NamSinh = stoi(item);

        getline(ss, item, ',');
        hs.Khoi = stoi(item);

        getline(ss, item, ',');
        strcpy(hs.Lop, item.c_str());

        getline(ss, item, ',');
        hs.DiemTrungBinh = stof(item);

        insertAtEnd(hs);
        count++;
    }

    file.close();
    cout << Color::GREEN << "✅ Đã tải " << count << " học sinh từ file: " << filename << Color::RESET << endl;
}
