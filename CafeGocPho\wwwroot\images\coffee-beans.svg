<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="beanGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#D2691E;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B4513;stop-opacity:1" />
    </radialGradient>
  </defs>
  <rect width="400" height="300" fill="#F5F5DC"/>
  
  <!-- Coffee beans scattered -->
  <ellipse cx="100" cy="80" rx="15" ry="8" fill="url(#beanGradient)" transform="rotate(45 100 80)"/>
  <ellipse cx="150" cy="120" rx="15" ry="8" fill="url(#beanGradient)" transform="rotate(-30 150 120)"/>
  <ellipse cx="200" cy="90" rx="15" ry="8" fill="url(#beanGradient)" transform="rotate(60 200 90)"/>
  <ellipse cx="250" cy="140" rx="15" ry="8" fill="url(#beanGradient)" transform="rotate(15 250 140)"/>
  <ellipse cx="300" cy="100" rx="15" ry="8" fill="url(#beanGradient)" transform="rotate(-45 300 100)"/>
  
  <ellipse cx="120" cy="180" rx="15" ry="8" fill="url(#beanGradient)" transform="rotate(30 120 180)"/>
  <ellipse cx="180" cy="200" rx="15" ry="8" fill="url(#beanGradient)" transform="rotate(-60 180 200)"/>
  <ellipse cx="240" cy="220" rx="15" ry="8" fill="url(#beanGradient)" transform="rotate(75 240 220)"/>
  <ellipse cx="290" cy="190" rx="15" ry="8" fill="url(#beanGradient)" transform="rotate(-15 290 190)"/>
  
  <!-- Central text -->
  <text x="200" y="160" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#8B4513" text-anchor="middle">Hạt cà phê</text>
  <text x="200" y="185" font-family="Arial, sans-serif" font-size="16" fill="#8B4513" text-anchor="middle">chất lượng cao</text>
</svg>
