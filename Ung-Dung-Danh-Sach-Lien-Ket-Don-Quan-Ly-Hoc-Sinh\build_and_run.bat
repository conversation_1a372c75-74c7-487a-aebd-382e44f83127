@echo off
REM Build and Run script for C++ Student Management System
REM Ứng Dụng Quản Lý <PERSON> - <PERSON>h S<PERSON>ch <PERSON> Kết Đơn

title Quan Ly Hoc Sinh - C++ Console Demo

echo.
echo ========================================
echo    QUAN LY HOC SINH - C++ CONSOLE
echo ========================================
echo.
echo Sinh vien thuc hien: Huynh Thai Bao
echo Lop: DX23TT11 - MSSV: 170123488
echo.
echo ========================================
echo.

REM Check if g++ is available
g++ --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] G++ khong duoc cai dat hoac khong co trong PATH!
    echo.
    echo Vui long:
    echo 1. <PERSON>ai dat MinGW-w64 hoac MSYS2
    echo 2. Cai dat Visual Studio Build Tools
    echo 3. Them compiler vao PATH
    echo.
    echo Download MinGW: https://www.mingw-w64.org/
    echo.
    pause
    exit /b 1
)

echo [INFO] Kiem tra compiler...
g++ --version | findstr "g++"

REM Check if source file exists
if not exist "QuanLyHocSinh_Enhanced.cpp" (
    echo [ERROR] Khong tim thay file QuanLyHocSinh_Enhanced.cpp!
    echo.
    if exist "QuanLyHocSinh.cpp" (
        echo [INFO] Tim thay QuanLyHocSinh.cpp, su dung phien ban co ban...
        set SOURCE_FILE=QuanLyHocSinh.cpp
        set TARGET_FILE=QuanLyHocSinh.exe
    ) else (
        echo Vui long dam bao file source nam trong cung thu muc.
        pause
        exit /b 1
    )
) else (
    set SOURCE_FILE=QuanLyHocSinh_Enhanced.cpp
    set TARGET_FILE=QuanLyHocSinh_Enhanced.exe
)

echo [INFO] Source file: %SOURCE_FILE%
echo [INFO] Target file: %TARGET_FILE%
echo.

REM Clean old executable
if exist "%TARGET_FILE%" (
    echo [INFO] Xoa file executable cu...
    del "%TARGET_FILE%"
)

echo [INFO] Dang bien dich chuong trinh...
echo.

REM Compile the program
g++ -std=c++11 -Wall -Wextra -O2 -o "%TARGET_FILE%" "%SOURCE_FILE%"

REM Check compilation result
if %errorlevel% neq 0 (
    echo.
    echo [ERROR] Bien dich that bai!
    echo.
    echo Cac nguyen nhan co the:
    echo - Loi syntax trong code C++
    echo - Thieu header files
    echo - Compiler khong ho tro C++11
    echo.
    pause
    exit /b %errorlevel%
)

echo [SUCCESS] Bien dich thanh cong!
echo.

REM Check if executable was created
if not exist "%TARGET_FILE%" (
    echo [ERROR] Khong tim thay file executable sau khi bien dich!
    pause
    exit /b 1
)

echo [INFO] Dang khoi dong ung dung...
echo.
echo ========================================
echo           CHUONG TRINH BAT DAU
echo ========================================
echo.

REM Set UTF-8 code page for better character display
chcp 65001 >nul 2>&1

REM Run the program
"%TARGET_FILE%"

REM Check if the program ran successfully
if %errorlevel% neq 0 (
    echo.
    echo [ERROR] Co loi xay ra khi chay ung dung!
    echo Error code: %errorlevel%
    echo.
) else (
    echo.
    echo [INFO] Chuong trinh da ket thuc thanh cong!
)

echo.
echo ========================================
echo           HOAN THANH
echo ========================================
echo.
echo Cam on ban da su dung ung dung!
echo.
echo Files da tao:
if exist "%TARGET_FILE%" (
    echo - %TARGET_FILE% ^(executable^)
)
if exist "students_data.csv" (
    echo - students_data.csv ^(du lieu xuat ra^)
)
echo.
echo De chay lai: %TARGET_FILE%
echo De bien dich lai: %~nx0
echo.
pause
