using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using ShortestPathProject.Models;

namespace ShortestPathProject.Services
{
    public class DijkstraService
    {
        public AlgorithmResult FindShortestPath(Graph graph, Vertex source, Vertex target)
        {
            var result = new AlgorithmResult("Dijkstra");
            var stopwatch = Stopwatch.StartNew();
            
            // Kiểm tra trọng số âm
            if (graph.Edges.Any(e => e.Weight < 0))
            {
                result.Steps.Add("Cảnh báo: Dijkstra không hỗ trợ trọng số âm!");
                stopwatch.Stop();
                result.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;
                return result;
            }
            
            // Khởi tạo
            var distances = new Dictionary<Vertex, double>();
            var predecessors = new Dictionary<Vertex, Vertex>();
            var visited = new HashSet<Vertex>();
            var priorityQueue = new PriorityQueue<Vertex, double>();
            
            // Đặt khoảng cách ban đầu
            foreach (var vertex in graph.Vertices)
            {
                distances[vertex] = double.PositiveInfinity;
            }
            distances[source] = 0;
            
            priorityQueue.Enqueue(source, 0);
            result.Steps.Add($"Khởi tạo: dist[{source.Name}] = 0");
            
            while (priorityQueue.Count > 0)
            {
                var current = priorityQueue.Dequeue();
                
                if (visited.Contains(current))
                    continue;
                    
                visited.Add(current);
                result.Steps.Add($"Xử lý đỉnh {current.Name} (dist = {distances[current]:F2})");
                
                // Nếu đã đến đích, có thể dừng sớm
                if (current.Equals(target))
                {
                    result.Steps.Add($"Đã tìm thấy đường đi đến {target.Name}");
                    break;
                }
                
                // Duyệt các đỉnh kề
                foreach (var edge in graph.GetAdjacentEdges(current))
                {
                    var neighbor = edge.To;
                    
                    if (visited.Contains(neighbor))
                        continue;
                        
                    double newDistance = distances[current] + edge.Weight;
                    
                    if (newDistance < distances[neighbor])
                    {
                        distances[neighbor] = newDistance;
                        predecessors[neighbor] = current;
                        priorityQueue.Enqueue(neighbor, newDistance);
                        
                        result.Steps.Add(
                            $"Cập nhật: dist[{neighbor.Name}] = {newDistance:F2} " +
                            $"qua {current.Name} (cạnh có trọng số {edge.Weight})"
                        );
                    }
                }
            }
            
            stopwatch.Stop();
            
            // Lưu kết quả
            result.Distances = distances;
            result.Predecessors = predecessors;
            result.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;
            result.BuildPath(source, target);
            
            if (result.Path.Count > 0)
            {
                result.Steps.Add($"Đường đi ngắn nhất: {string.Join(" -> ", result.Path.Select(v => v.Name))}");
                result.Steps.Add($"Độ dài: {result.PathLength:F2}");
            }
            else
            {
                result.Steps.Add($"Không có đường đi từ {source.Name} đến {target.Name}");
            }
            
            result.Steps.Add($"Thời gian thực thi: {result.ExecutionTimeMs} ms");
            
            return result;
        }
        
        // Tìm đường đi ngắn nhất đến tất cả đỉnh
        public AlgorithmResult FindAllShortestPaths(Graph graph, Vertex source)
        {
            var result = new AlgorithmResult("Dijkstra - All Paths");
            var stopwatch = Stopwatch.StartNew();
            
            // Kiểm tra trọng số âm
            if (graph.Edges.Any(e => e.Weight < 0))
            {
                result.Steps.Add("Cảnh báo: Dijkstra không hỗ trợ trọng số âm!");
                stopwatch.Stop();
                result.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;
                return result;
            }
            
            var distances = new Dictionary<Vertex, double>();
            var predecessors = new Dictionary<Vertex, Vertex>();
            var visited = new HashSet<Vertex>();
            var priorityQueue = new PriorityQueue<Vertex, double>();
            
            // Khởi tạo
            foreach (var vertex in graph.Vertices)
            {
                distances[vertex] = double.PositiveInfinity;
            }
            distances[source] = 0;
            priorityQueue.Enqueue(source, 0);
            
            result.Steps.Add($"Tìm đường đi ngắn nhất từ {source.Name} đến tất cả đỉnh");
            
            while (priorityQueue.Count > 0)
            {
                var current = priorityQueue.Dequeue();
                
                if (visited.Contains(current))
                    continue;
                    
                visited.Add(current);
                
                foreach (var edge in graph.GetAdjacentEdges(current))
                {
                    var neighbor = edge.To;
                    
                    if (visited.Contains(neighbor))
                        continue;
                        
                    double newDistance = distances[current] + edge.Weight;
                    
                    if (newDistance < distances[neighbor])
                    {
                        distances[neighbor] = newDistance;
                        predecessors[neighbor] = current;
                        priorityQueue.Enqueue(neighbor, newDistance);
                    }
                }
            }
            
            stopwatch.Stop();
            
            result.Distances = distances;
            result.Predecessors = predecessors;
            result.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;
            
            // Thêm thông tin về kết quả
            foreach (var vertex in graph.Vertices)
            {
                if (!vertex.Equals(source))
                {
                    if (double.IsPositiveInfinity(distances[vertex]))
                    {
                        result.Steps.Add($"Không có đường đi đến {vertex.Name}");
                    }
                    else
                    {
                        result.Steps.Add($"Khoảng cách đến {vertex.Name}: {distances[vertex]:F2}");
                    }
                }
            }
            
            result.Steps.Add($"Thời gian thực thi: {result.ExecutionTimeMs} ms");
            
            return result;
        }

        // Kiểm tra tính hợp lệ của đồ thị cho Dijkstra
        public bool IsValidForDijkstra(Graph graph)
        {
            return !graph.Edges.Any(e => e.Weight < 0);
        }

        // Lấy thống kê về quá trình thực thi
        public Dictionary<string, object> GetExecutionStats(AlgorithmResult result, Graph graph)
        {
            return new Dictionary<string, object>
            {
                ["algorithm"] = result.AlgorithmName,
                ["executionTime"] = result.ExecutionTimeMs,
                ["vertexCount"] = graph.VertexCount,
                ["edgeCount"] = graph.EdgeCount,
                ["pathFound"] = result.Path.Count > 0,
                ["pathLength"] = result.PathLength,
                ["stepsCount"] = result.Steps.Count,
                ["memoryComplexity"] = $"O({graph.VertexCount})",
                ["timeComplexity"] = $"O(({graph.VertexCount} + {graph.EdgeCount}) log {graph.VertexCount})"
            };
        }
    }
}
