# Simple Joomla Download Script
Write-Host "========================================" -ForegroundColor Green
Write-Host "   Joomla Download Script" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Check if XAMPP exists
if (-not (Test-Path "C:\xampp")) {
    Write-Host "ERROR: XAMPP not found!" -ForegroundColor Red
    Write-Host "Please install XAMPP first." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit
}

# Variables
$joomlaVersion = "4.4.2"
$joomlaUrl = "https://github.com/joomla/joomla-cms/releases/download/$joomlaVersion/Joomla_$joomlaVersion-Stable-Full_Package.zip"
$downloadDir = "$env:USERPROFILE\Downloads\Joomla"
$joomlaZip = "$downloadDir\joomla-$joomlaVersion.zip"
$extractPath = "C:\xampp\htdocs\electronics-shop"

# Create download directory
if (-not (Test-Path $downloadDir)) {
    New-Item -ItemType Directory -Path $downloadDir -Force
}

Write-Host "Downloading Joomla $joomlaVersion..." -ForegroundColor Yellow
Write-Host "URL: $joomlaUrl" -ForegroundColor Gray

try {
    # Download Joomla
    Invoke-WebRequest -Uri $joomlaUrl -OutFile $joomlaZip -UseBasicParsing
    Write-Host "Download completed!" -ForegroundColor Green
    
    # Create extraction directory
    if (Test-Path $extractPath) {
        Write-Host "Directory exists. Creating backup..." -ForegroundColor Yellow
        $backupPath = "$extractPath-backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
        Move-Item $extractPath $backupPath -ErrorAction SilentlyContinue
    }
    
    New-Item -ItemType Directory -Path $extractPath -Force
    
    # Extract Joomla
    Write-Host "Extracting Joomla to $extractPath..." -ForegroundColor Yellow
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::ExtractToDirectory($joomlaZip, $extractPath)
    
    Write-Host "Joomla extracted successfully!" -ForegroundColor Green
    Write-Host "Location: $extractPath" -ForegroundColor Cyan
    
    # Set permissions
    Write-Host "Setting permissions..." -ForegroundColor Yellow
    icacls $extractPath /grant "Everyone:(OI)(CI)F" /T | Out-Null
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "   Joomla Setup Complete!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Make sure XAMPP Apache and MySQL are running" -ForegroundColor White
    Write-Host "2. Open: http://localhost/electronics-shop" -ForegroundColor White
    Write-Host "3. Follow Joomla installation wizard" -ForegroundColor White
    Write-Host ""
    
    # Ask to open browser
    $openBrowser = Read-Host "Open installation page now? (y/n)"
    if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
        Start-Process "http://localhost/electronics-shop"
    }
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please download manually from: https://www.joomla.org/download.html" -ForegroundColor Yellow
}

Read-Host "Press Enter to continue"
