<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 400">
  <defs>
    <linearGradient id="cupGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B4513;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A0522D;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="mochaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B4513;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#654321;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3E2723;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="whipGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFAF0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F5DEB3;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="chocolateGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#D2691E;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B4513;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="200" cy="200" r="190" fill="#FFF8DC" stroke="#D2B48C" stroke-width="4"/>
  
  <!-- Saucer -->
  <ellipse cx="200" cy="320" rx="80" ry="15" fill="url(#cupGradient)"/>
  
  <!-- Cup -->
  <path d="M 145 320 Q 145 280 155 240 L 245 240 Q 255 280 255 320 Z" fill="url(#cupGradient)" stroke="#654321" stroke-width="2"/>
  
  <!-- Handle -->
  <path d="M 255 280 Q 285 280 285 300 Q 285 320 255 320" fill="none" stroke="#654321" stroke-width="6" stroke-linecap="round"/>
  
  <!-- Mocha -->
  <path d="M 155 240 Q 155 245 160 250 L 240 250 Q 245 245 245 240 Z" fill="url(#mochaGradient)"/>
  
  <!-- Whipped cream -->
  <ellipse cx="200" cy="245" rx="42" ry="8" fill="url(#whipGradient)"/>
  <ellipse cx="190" cy="238" rx="25" ry="6" fill="url(#whipGradient)"/>
  <ellipse cx="210" cy="235" rx="20" ry="5" fill="url(#whipGradient)"/>
  <ellipse cx="200" cy="228" rx="15" ry="4" fill="url(#whipGradient)"/>
  
  <!-- Chocolate drizzle -->
  <path d="M 180 240 Q 185 235 190 240 Q 195 245 200 240 Q 205 235 210 240 Q 215 245 220 240" 
        fill="none" stroke="url(#chocolateGradient)" stroke-width="3" stroke-linecap="round"/>
  <path d="M 175 245 Q 180 240 185 245 Q 190 250 195 245 Q 200 240 205 245 Q 210 250 215 245 Q 220 240 225 245" 
        fill="none" stroke="url(#chocolateGradient)" stroke-width="2" stroke-linecap="round"/>
  
  <!-- Chocolate chips on top -->
  <circle cx="190" cy="230" r="2" fill="#654321"/>
  <circle cx="210" cy="232" r="2" fill="#654321"/>
  <circle cx="200" cy="225" r="1.5" fill="#654321"/>
  
  <!-- Steam -->
  <path d="M 180 215 Q 175 205 180 195 Q 185 185 180 175" fill="none" stroke="#E6E6FA" stroke-width="3" stroke-linecap="round" opacity="0.7"/>
  <path d="M 200 220 Q 195 210 200 200 Q 205 190 200 180" fill="none" stroke="#E6E6FA" stroke-width="3" stroke-linecap="round" opacity="0.7"/>
  <path d="M 220 215 Q 215 205 220 195 Q 225 185 220 175" fill="none" stroke="#E6E6FA" stroke-width="3" stroke-linecap="round" opacity="0.7"/>
  
  <!-- Coffee beans decoration -->
  <g transform="translate(80,120)">
    <ellipse cx="0" cy="0" rx="10" ry="15" fill="#8B4513" transform="rotate(25)"/>
    <line x1="0" y1="-10" x2="0" y2="10" stroke="#654321" stroke-width="1.5"/>
  </g>
  
  <g transform="translate(320,140)">
    <ellipse cx="0" cy="0" rx="10" ry="15" fill="#8B4513" transform="rotate(-20)"/>
    <line x1="0" y1="-10" x2="0" y2="10" stroke="#654321" stroke-width="1.5"/>
  </g>
  
  <!-- Chocolate bar decoration -->
  <g transform="translate(100,300)">
    <rect x="0" y="0" width="20" height="12" fill="#654321" rx="2"/>
    <line x1="5" y1="0" x2="5" y2="12" stroke="#8B4513" stroke-width="1"/>
    <line x1="10" y1="0" x2="10" y2="12" stroke="#8B4513" stroke-width="1"/>
    <line x1="15" y1="0" x2="15" y2="12" stroke="#8B4513" stroke-width="1"/>
    <line x1="0" y1="4" x2="20" y2="4" stroke="#8B4513" stroke-width="1"/>
    <line x1="0" y1="8" x2="20" y2="8" stroke="#8B4513" stroke-width="1"/>
  </g>
  
  <g transform="translate(280,310)">
    <rect x="0" y="0" width="15" height="10" fill="#654321" rx="2"/>
    <line x1="3" y1="0" x2="3" y2="10" stroke="#8B4513" stroke-width="1"/>
    <line x1="6" y1="0" x2="6" y2="10" stroke="#8B4513" stroke-width="1"/>
    <line x1="9" y1="0" x2="9" y2="10" stroke="#8B4513" stroke-width="1"/>
    <line x1="12" y1="0" x2="12" y2="10" stroke="#8B4513" stroke-width="1"/>
    <line x1="0" y1="3" x2="15" y2="3" stroke="#8B4513" stroke-width="1"/>
    <line x1="0" y1="6" x2="15" y2="6" stroke="#8B4513" stroke-width="1"/>
  </g>
  
  <!-- Title -->
  <text x="200" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#654321">Mocha</text>
</svg>
