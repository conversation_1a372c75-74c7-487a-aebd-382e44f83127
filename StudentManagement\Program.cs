using System;
using System.Collections.Generic;
using System.Globalization;

namespace StudentManagement
{
    class Program
    {
        private static StudentManager manager = new StudentManager(true);

        static void Main(string[] args)
        {
            try
            {
                Console.OutputEncoding = System.Text.Encoding.UTF8;
            }
            catch
            {
                // Ignore encoding errors
            }

            Console.WriteLine("================================================================================");
            Console.WriteLine("                    CHUONG TRINH QUAN LY SINH VIEN                            ");
            Console.WriteLine("                     SU DUNG DANH SACH LIEN KET                              ");
            Console.WriteLine("================================================================================");

            // Thêm dữ liệu mẫu
            AddSampleData();
            
            bool running = true;
            while (running)
            {
                ShowMenu();
                string choice = Console.ReadLine();
                
                switch (choice)
                {
                    case "1":
                        AddNewStudent();
                        break;
                    case "2":
                        DisplayAllStudents();
                        break;
                    case "3":
                        SearchStudent();
                        break;
                    case "4":
                        SearchStudentByName();
                        break;
                    case "5":
                        SearchStudentByClass();
                        break;
                    case "6":
                        RemoveStudent();
                        break;
                    case "7":
                        SortStudents();
                        break;
                    case "8":
                        ShowStatistics();
                        break;
                    case "9":
                        ShowTopStudent();
                        break;
                    case "10":
                        SwitchListType();
                        break;
                    case "11":
                        DisplayStudentsReverse();
                        break;
                    case "12":
                        ClearAllStudents();
                        break;
                    case "0":
                        running = false;
                        Console.WriteLine("Cảm ơn bạn đã sử dụng chương trình!");
                        break;
                    default:
                        Console.WriteLine("Lựa chọn không hợp lệ. Vui lòng thử lại.");
                        break;
                }
                
                if (running)
                {
                    Console.WriteLine("\nNhấn phím bất kỳ để tiếp tục...");
                    Console.ReadKey();
                }
            }
        }

        static void ShowMenu()
        {
            Console.Clear();
            Console.WriteLine("================================================================================");
            Console.WriteLine("                              MENU CHINH                                      ");
            Console.WriteLine("================================================================================");
            Console.WriteLine("  1. Them sinh vien moi");
            Console.WriteLine("  2. Hien thi tat ca sinh vien");
            Console.WriteLine("  3. Tim sinh vien theo ma");
            Console.WriteLine("  4. Tim sinh vien theo ten");
            Console.WriteLine("  5. Tim sinh vien theo lop");
            Console.WriteLine("  6. Xoa sinh vien");
            Console.WriteLine("  7. Sap xep sinh vien theo diem");
            Console.WriteLine("  8. Xem thong ke");
            Console.WriteLine("  9. Xem sinh vien co diem cao nhat");
            Console.WriteLine(" 10. Chuyen doi loai danh sach (don/kep)");
            Console.WriteLine(" 11. Hien thi sinh vien theo thu tu nguoc");
            Console.WriteLine(" 12. Xoa tat ca sinh vien");
            Console.WriteLine("  0. Thoat chuong trinh");
            Console.WriteLine("================================================================================");
            Console.Write("Nhap lua chon cua ban: ");
        }

        static void AddSampleData()
        {
            var students = new List<Student>
            {
                new Student("SV001", "Nguyễn Văn An", new DateTime(2002, 5, 15), "Nam", "CNTT01", 8.5, "<EMAIL>", "0123456789"),
                new Student("SV002", "Trần Thị Bình", new DateTime(2002, 8, 20), "Nữ", "CNTT01", 9.2, "<EMAIL>", "0987654321"),
                new Student("SV003", "Lê Văn Cường", new DateTime(2001, 12, 10), "Nam", "CNTT02", 7.8, "<EMAIL>", "0369852147"),
                new Student("SV004", "Phạm Thị Dung", new DateTime(2002, 3, 25), "Nữ", "CNTT02", 8.9, "<EMAIL>", "0741852963"),
                new Student("SV005", "Hoàng Văn Em", new DateTime(2001, 9, 5), "Nam", "CNTT03", 6.5, "<EMAIL>", "0258147369")
            };

            foreach (var student in students)
            {
                manager.AddStudent(student);
            }
        }

        static void AddNewStudent()
        {
            Console.Clear();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                              THÊM SINH VIÊN MỚI                                     ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════════════════════════════╝");

            try
            {
                Console.Write("Mã sinh viên: ");
                string maSV = Console.ReadLine();
                
                // Kiểm tra mã sinh viên đã tồn tại
                if (manager.FindStudent(maSV) != null)
                {
                    Console.WriteLine("Mã sinh viên đã tồn tại!");
                    return;
                }

                Console.Write("Họ và tên: ");
                string hoTen = Console.ReadLine();

                Console.Write("Ngày sinh (dd/MM/yyyy): ");
                DateTime ngaySinh = DateTime.ParseExact(Console.ReadLine(), "dd/MM/yyyy", CultureInfo.InvariantCulture);

                Console.Write("Giới tính (Nam/Nữ): ");
                string gioiTinh = Console.ReadLine();

                Console.Write("Lớp: ");
                string lop = Console.ReadLine();

                Console.Write("Điểm trung bình: ");
                double diemTB = double.Parse(Console.ReadLine());

                Console.Write("Email: ");
                string email = Console.ReadLine();

                Console.Write("Số điện thoại: ");
                string sdt = Console.ReadLine();

                Student newStudent = new Student(maSV, hoTen, ngaySinh, gioiTinh, lop, diemTB, email, sdt);
                manager.AddStudent(newStudent);

                Console.WriteLine("Thêm sinh viên thành công!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi: {ex.Message}");
            }
        }

        static void DisplayAllStudents()
        {
            Console.Clear();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                            DANH SÁCH TẤT CẢ SINH VIÊN                               ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════════════════════════════╝");

            var students = manager.GetAllStudents();
            if (students.Count == 0)
            {
                Console.WriteLine("Không có sinh viên nào trong danh sách.");
                return;
            }

            Console.WriteLine($"Tổng số sinh viên: {students.Count}");
            Console.WriteLine(new string('=', 120));

            foreach (var student in students)
            {
                Console.WriteLine(student.ToString());
            }
        }

        static void SearchStudent()
        {
            Console.Clear();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                            TÌM SINH VIÊN THEO MÃ                                    ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════════════════════════════╝");

            Console.Write("Nhập mã sinh viên cần tìm: ");
            string maSV = Console.ReadLine();

            Student student = manager.FindStudent(maSV);
            if (student != null)
            {
                Console.WriteLine("Tìm thấy sinh viên:");
                Console.WriteLine(student.ToDetailString());
            }
            else
            {
                Console.WriteLine("Không tìm thấy sinh viên có mã: " + maSV);
            }
        }

        static void SearchStudentByName()
        {
            Console.Clear();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                            TÌM SINH VIÊN THEO TÊN                                   ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════════════════════════════╝");

            Console.Write("Nhập tên sinh viên cần tìm: ");
            string hoTen = Console.ReadLine();

            var students = manager.FindStudentsByName(hoTen);
            if (students.Count > 0)
            {
                Console.WriteLine($"Tìm thấy {students.Count} sinh viên:");
                Console.WriteLine(new string('=', 120));
                foreach (var student in students)
                {
                    Console.WriteLine(student.ToString());
                }
            }
            else
            {
                Console.WriteLine("Không tìm thấy sinh viên nào có tên chứa: " + hoTen);
            }
        }

        static void SearchStudentByClass()
        {
            Console.Clear();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                            TÌM SINH VIÊN THEO LỚP                                   ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════════════════════════════╝");

            Console.Write("Nhập tên lớp cần tìm: ");
            string lop = Console.ReadLine();

            var students = manager.FindStudentsByClass(lop);
            if (students.Count > 0)
            {
                Console.WriteLine($"Tìm thấy {students.Count} sinh viên trong lớp {lop}:");
                Console.WriteLine(new string('=', 120));
                foreach (var student in students)
                {
                    Console.WriteLine(student.ToString());
                }
            }
            else
            {
                Console.WriteLine("Không tìm thấy sinh viên nào trong lớp: " + lop);
            }
        }

        static void RemoveStudent()
        {
            Console.Clear();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                                XÓA SINH VIÊN                                        ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════════════════════════════╝");

            Console.Write("Nhập mã sinh viên cần xóa: ");
            string maSV = Console.ReadLine();

            Student student = manager.FindStudent(maSV);
            if (student != null)
            {
                Console.WriteLine("Thông tin sinh viên sẽ bị xóa:");
                Console.WriteLine(student.ToDetailString());
                Console.Write("Bạn có chắc chắn muốn xóa? (y/n): ");
                string confirm = Console.ReadLine();

                if (confirm.ToLower() == "y" || confirm.ToLower() == "yes")
                {
                    if (manager.RemoveStudent(maSV))
                    {
                        Console.WriteLine("Xóa sinh viên thành công!");
                    }
                    else
                    {
                        Console.WriteLine("Có lỗi xảy ra khi xóa sinh viên.");
                    }
                }
                else
                {
                    Console.WriteLine("Đã hủy thao tác xóa.");
                }
            }
            else
            {
                Console.WriteLine("Không tìm thấy sinh viên có mã: " + maSV);
            }
        }

        static void SortStudents()
        {
            Console.Clear();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                        SẮP XẾP SINH VIÊN THEO ĐIỂM                                  ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════════════════════════════╝");

            manager.SortStudentsByGPA();
            Console.WriteLine("Đã sắp xếp sinh viên theo điểm trung bình (cao đến thấp).");

            DisplayAllStudents();
        }

        static void ShowStatistics()
        {
            Console.Clear();
            manager.GetStatistics();
        }

        static void ShowTopStudent()
        {
            Console.Clear();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                          SINH VIÊN CÓ ĐIỂM CAO NHẤT                                 ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════════════════════════════╝");

            Student topStudent = manager.FindTopStudent();
            if (topStudent != null)
            {
                Console.WriteLine(topStudent.ToDetailString());
            }
            else
            {
                Console.WriteLine("Không có sinh viên nào trong danh sách.");
            }
        }

        static void SwitchListType()
        {
            Console.Clear();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                        CHUYỂN ĐỔI LOẠI DANH SÁCH                                   ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════════════════════════════╝");

            manager.SwitchListType();
        }

        static void DisplayStudentsReverse()
        {
            Console.Clear();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                      DANH SÁCH SINH VIÊN (THỨ TỰ NGƯỢC)                             ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════════════════════════════╝");

            var students = manager.GetAllStudentsReverse();
            if (students.Count == 0)
            {
                Console.WriteLine("Không có sinh viên nào trong danh sách.");
                return;
            }

            Console.WriteLine($"Tổng số sinh viên: {students.Count}");
            Console.WriteLine(new string('=', 120));

            foreach (var student in students)
            {
                Console.WriteLine(student.ToString());
            }
        }

        static void ClearAllStudents()
        {
            Console.Clear();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                            XÓA TẤT CẢ SINH VIÊN                                     ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════════════════════════════╝");

            Console.Write("Bạn có chắc chắn muốn xóa tất cả sinh viên? (y/n): ");
            string confirm = Console.ReadLine();

            if (confirm.ToLower() == "y" || confirm.ToLower() == "yes")
            {
                manager.ClearAllStudents();
                Console.WriteLine("Đã xóa tất cả sinh viên thành công!");
            }
            else
            {
                Console.WriteLine("Đã hủy thao tác xóa.");
            }
        }
    }
}
