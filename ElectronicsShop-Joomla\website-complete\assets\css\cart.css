/* Cart Page Styles */

/* Page Title */
.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 2rem;
}

/* Empty Cart */
.empty-cart {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.empty-cart i {
    font-size: 5rem;
    color: var(--secondary-color);
    margin-bottom: 2rem;
    opacity: 0.5;
}

.empty-cart h3 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.empty-cart p {
    font-size: 1.125rem;
    color: var(--secondary-color);
    margin-bottom: 2rem;
}

/* Cart Items */
.cart-items {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.cart-items-header {
    background: var(--light-color);
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    font-weight: 600;
    color: var(--dark-color);
}

.cart-item {
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    transition: var(--transition-base);
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item:hover {
    background: var(--light-color);
}

.cart-item-image {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: var(--border-radius);
    border: 1px solid #eee;
}

.cart-item-info {
    flex: 1;
    padding-left: 1.5rem;
}

.cart-item-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.cart-item-name a {
    color: inherit;
    text-decoration: none;
    transition: var(--transition-base);
}

.cart-item-name a:hover {
    color: var(--primary-color);
}

.cart-item-description {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
}

.cart-item-condition {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.cart-item-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--danger-color);
}

/* Quantity Controls */
.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 1rem 0;
}

.quantity-btn {
    width: 36px;
    height: 36px;
    border: 1px solid var(--primary-color);
    background: white;
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-base);
    font-size: 0.875rem;
    font-weight: 600;
}

.quantity-btn:hover:not(:disabled) {
    background: var(--primary-color);
    color: white;
}

.quantity-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.quantity-input {
    width: 60px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    padding: 0.5rem;
    font-weight: 600;
}

.quantity-label {
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin-right: 0.5rem;
}

/* Item Actions */
.cart-item-actions {
    display: flex;
    flex-direction: column;
    align-items: end;
    gap: 1rem;
}

.remove-item {
    background: none;
    border: none;
    color: var(--danger-color);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition-base);
    font-size: 0.875rem;
}

.remove-item:hover {
    background: var(--danger-color);
    color: white;
}

.save-for-later {
    background: none;
    border: 1px solid var(--secondary-color);
    color: var(--secondary-color);
    cursor: pointer;
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    transition: var(--transition-base);
    font-size: 0.75rem;
}

.save-for-later:hover {
    background: var(--secondary-color);
    color: white;
}

/* Cart Summary */
.cart-summary {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    position: sticky;
    top: 120px;
}

.cart-summary-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--primary-color);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.5rem 0;
}

.summary-row.total {
    border-top: 1px solid #eee;
    padding-top: 1rem;
    margin-top: 1.5rem;
    font-size: 1.25rem;
    font-weight: 700;
}

.summary-label {
    color: var(--dark-color);
}

.summary-value {
    font-weight: 600;
    color: var(--danger-color);
}

.summary-value.discount {
    color: var(--success-color);
}

.summary-value.shipping {
    color: var(--info-color);
}

/* Coupon Section */
.coupon-section {
    margin: 1.5rem 0;
    padding: 1.5rem 0;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}

.coupon-input {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.coupon-input input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
}

.coupon-input button {
    padding: 0.75rem 1.5rem;
    background: var(--success-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition-base);
}

.coupon-input button:hover {
    background: #1e7e34;
}

.applied-coupon {
    background: var(--success-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.remove-coupon {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    margin-left: 0.5rem;
}

/* Checkout Actions */
.checkout-actions {
    margin-top: 2rem;
}

.checkout-btn {
    width: 100%;
    padding: 1rem;
    font-size: 1.125rem;
    font-weight: 700;
    background: var(--success-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    transition: var(--transition-base);
    margin-bottom: 1rem;
}

.checkout-btn:hover {
    background: #1e7e34;
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.continue-shopping {
    width: 100%;
    padding: 0.75rem;
    font-weight: 600;
    background: white;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    transition: var(--transition-base);
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.continue-shopping:hover {
    background: var(--primary-color);
    color: white;
}

/* Security Info */
.security-info {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-top: 1.5rem;
    text-align: center;
}

.security-info i {
    color: var(--success-color);
    margin-right: 0.5rem;
}

.security-info small {
    color: var(--secondary-color);
    font-size: 0.8rem;
}

/* Cart Actions Bar */
.cart-actions-bar {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.cart-actions-bar .row {
    align-items: center;
}

.items-count {
    font-weight: 600;
    color: var(--dark-color);
}

.bulk-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.select-all {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--dark-color);
}

.clear-cart {
    background: none;
    border: 1px solid var(--danger-color);
    color: var(--danger-color);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 600;
    transition: var(--transition-base);
}

.clear-cart:hover {
    background: var(--danger-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }
    
    .cart-item {
        flex-direction: column;
        text-align: center;
    }
    
    .cart-item-info {
        padding-left: 0;
        padding-top: 1rem;
    }
    
    .cart-item-actions {
        flex-direction: row;
        justify-content: center;
        margin-top: 1rem;
    }
    
    .cart-summary {
        position: static;
        margin-top: 2rem;
    }
    
    .quantity-controls {
        justify-content: center;
    }
    
    .cart-actions-bar .row {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .bulk-actions {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .cart-item {
        padding: 1rem;
    }
    
    .cart-item-image {
        width: 80px;
        height: 80px;
    }
    
    .cart-summary {
        padding: 1.5rem;
    }
    
    .coupon-input {
        flex-direction: column;
    }
    
    .quantity-btn {
        width: 32px;
        height: 32px;
        font-size: 0.75rem;
    }
    
    .quantity-input {
        width: 50px;
    }
}

/* Animation for item removal */
.cart-item.removing {
    animation: slideOut 0.3s ease-out forwards;
}

@keyframes slideOut {
    to {
        transform: translateX(100%);
        opacity: 0;
        height: 0;
        padding: 0;
        margin: 0;
    }
}

/* Loading state for cart items */
.cart-item.updating {
    opacity: 0.6;
    pointer-events: none;
}

.cart-item.updating::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    border: 2px solid var(--primary-color);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
}
