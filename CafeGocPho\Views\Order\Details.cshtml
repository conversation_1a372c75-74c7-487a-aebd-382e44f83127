@model GocPho.Models.Order

@{
    ViewData["Title"] = $"Chi tiết đơn hàng #{Model.Id}";
}

<style>
    .order-details-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }

    .page-header {
        text-align: center;
        margin-bottom: 30px;
        padding: 30px 0;
        background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
        color: white;
        border-radius: 15px;
    }

    .page-header h1 {
        font-size: 2rem;
        margin-bottom: 10px;
        font-weight: 700;
    }

    .order-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 20px;
    }

    .order-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 25px;
        border-bottom: 1px solid #dee2e6;
    }

    .order-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .info-item {
        text-align: center;
    }

    .info-label {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 5px;
        text-transform: uppercase;
        font-weight: 600;
    }

    .info-value {
        font-size: 1.1rem;
        font-weight: 700;
        color: #333;
    }

    .order-status {
        text-align: center;
        padding: 12px 24px;
        border-radius: 25px;
        font-size: 1rem;
        font-weight: 700;
        text-transform: uppercase;
        display: inline-block;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
        border: 2px solid #ffeaa7;
    }

    .status-confirmed {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 2px solid #bee5eb;
    }

    .status-preparing {
        background-color: #d4edda;
        color: #155724;
        border: 2px solid #c3e6cb;
    }

    .status-delivering {
        background-color: #cce5ff;
        color: #004085;
        border: 2px solid #99d6ff;
    }

    .status-delivered {
        background-color: #d4edda;
        color: #155724;
        border: 2px solid #c3e6cb;
    }

    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
        border: 2px solid #f5c6cb;
    }

    .order-body {
        padding: 25px;
    }

    .section-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #8B4513;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #8B4513;
    }

    .customer-info {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 25px;
    }

    .customer-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
    }

    .customer-field {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .customer-field strong {
        color: #8B4513;
        min-width: 120px;
    }

    .order-items-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 25px;
    }

    .order-items-table th,
    .order-items-table td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
    }

    .order-items-table th {
        background: #f8f9fa;
        font-weight: 700;
        color: #8B4513;
        text-transform: uppercase;
        font-size: 0.9rem;
    }

    .order-items-table tr:hover {
        background: #f8f9fa;
    }

    .product-name {
        font-weight: 600;
        color: #333;
    }

    .quantity {
        text-align: center;
        font-weight: 600;
    }

    .price {
        text-align: right;
        font-weight: 600;
        color: #8B4513;
    }

    .order-summary {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        border: 2px solid #8B4513;
    }

    .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #dee2e6;
    }

    .summary-row:last-child {
        border-bottom: none;
        font-size: 1.2rem;
        font-weight: 700;
        color: #8B4513;
        padding-top: 15px;
        border-top: 2px solid #8B4513;
    }

    .back-button {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        display: inline-block;
        margin-top: 20px;
    }

    .back-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        color: white;
    }

    @@media (max-width: 768px) {
        .order-details-container {
            padding: 10px;
        }

        .page-header {
            padding: 20px;
        }

        .page-header h1 {
            font-size: 1.5rem;
        }

        .order-info-grid {
            grid-template-columns: 1fr;
        }

        .customer-info-grid {
            grid-template-columns: 1fr;
        }

        .order-items-table {
            font-size: 0.9rem;
        }

        .order-items-table th,
        .order-items-table td {
            padding: 10px 8px;
        }
    }
</style>

<div class="order-details-container">
    <div class="page-header">
        <h1>📋 Chi tiết đơn hàng #@Model.Id</h1>
    </div>

    <div class="order-card">
        <div class="order-header">
            <div class="order-info-grid">
                <div class="info-item">
                    <div class="info-label">Ngày đặt</div>
                    <div class="info-value">@Model.OrderDate.ToString("dd/MM/yyyy")</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Thời gian</div>
                    <div class="info-value">@Model.OrderDate.ToString("HH:mm")</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Tổng tiền</div>
                    <div class="info-value">@Model.TotalAmount.ToString("N0") VNĐ</div>
                </div>
            </div>

            <div style="text-align: center;">
                <div class="order-status <EMAIL>().ToLower()">
                    @switch (Model.Status)
                    {
                        case GocPho.Models.OrderStatus.Pending:
                            <span>⏳ Chờ xác nhận</span>
                            break;
                        case GocPho.Models.OrderStatus.Confirmed:
                            <span>✅ Đã xác nhận</span>
                            break;
                        case GocPho.Models.OrderStatus.Preparing:
                            <span>👨‍🍳 Đang chuẩn bị</span>
                            break;
                        case GocPho.Models.OrderStatus.Delivering:
                            <span>🚚 Đang giao hàng</span>
                            break;
                        case GocPho.Models.OrderStatus.Delivered:
                            <span>🎉 Hoàn thành</span>
                            break;
                        case GocPho.Models.OrderStatus.Cancelled:
                            <span>❌ Đã hủy</span>
                            break;
                    }
                </div>
            </div>
        </div>

        <div class="order-body">
            <!-- Thông tin khách hàng -->
            <h3 class="section-title">👤 Thông tin khách hàng</h3>
            <div class="customer-info">
                <div class="customer-info-grid">
                    <div class="customer-field">
                        <strong>Tên khách hàng:</strong>
                        <span>@Model.CustomerName</span>
                    </div>
                    <div class="customer-field">
                        <strong>Số điện thoại:</strong>
                        <span>@Model.PhoneNumber</span>
                    </div>
                    <div class="customer-field" style="grid-column: 1 / -1;">
                        <strong>Địa chỉ giao hàng:</strong>
                        <span>@Model.DeliveryAddress</span>
                    </div>
                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <div class="customer-field" style="grid-column: 1 / -1;">
                            <strong>Ghi chú:</strong>
                            <span>@Model.Notes</span>
                        </div>
                    }
                </div>
            </div>

            <!-- Chi tiết sản phẩm -->
            <h3 class="section-title">☕ Chi tiết sản phẩm</h3>
            <table class="order-items-table">
                <thead>
                    <tr>
                        <th>Sản phẩm</th>
                        <th style="text-align: center;">Số lượng</th>
                        <th style="text-align: right;">Đơn giá</th>
                        <th style="text-align: right;">Thành tiền</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.OrderItems)
                    {
                        <tr>
                            <td class="product-name">@item.Product?.Name</td>
                            <td class="quantity">@item.Quantity</td>
                            <td class="price">@item.UnitPrice.ToString("N0") VNĐ</td>
                            <td class="price">@((item.UnitPrice * item.Quantity).ToString("N0")) VNĐ</td>
                        </tr>
                    }
                </tbody>
            </table>

            <!-- Tổng kết đơn hàng -->
            <div class="order-summary">
                <div class="summary-row">
                    <span>Tạm tính:</span>
                    <span>@Model.OrderItems.Sum(x => x.UnitPrice * x.Quantity).ToString("N0") VNĐ</span>
                </div>
                <div class="summary-row">
                    <span>Phí giao hàng:</span>
                    <span>Miễn phí</span>
                </div>
                <div class="summary-row">
                    <span>Tổng cộng:</span>
                    <span>@Model.TotalAmount.ToString("N0") VNĐ</span>
                </div>
            </div>
        </div>
    </div>

    <a href="@Url.Action("MyOrders")" class="back-button">
        ← Quay lại danh sách đơn hàng
    </a>
</div>
