<?xml version="1.0" encoding="utf-8"?>
<extension type="module" version="4.0" client="site" method="upgrade">
    <name>MOD_PRODUCT_CONDITION</name>
    <author>Electronics Shop Team</author>
    <creationDate>July 2025</creationDate>
    <copyright>Copyright (C) 2025 Electronics Shop. All rights reserved.</copyright>
    <license>GNU General Public License version 2 or later</license>
    <authorEmail><EMAIL></authorEmail>
    <authorUrl>https://electronicsshop.com</authorUrl>
    <version>1.0.0</version>
    <description>MOD_PRODUCT_CONDITION_XML_DESCRIPTION</description>
    
    <files>
        <filename module="mod_product_condition">mod_product_condition.php</filename>
        <filename>helper.php</filename>
        <filename>tmpl/default.php</filename>
        <folder>language</folder>
        <folder>css</folder>
        <folder>js</folder>
    </files>
    
    <languages>
        <language tag="en-GB">language/en-GB/en-GB.mod_product_condition.ini</language>
        <language tag="vi-VN">language/vi-VN/vi-VN.mod_product_condition.ini</language>
    </languages>
    
    <config>
        <fields name="params">
            <fieldset name="basic">
                <field
                    name="display_mode"
                    type="list"
                    default="filter"
                    label="MOD_PRODUCT_CONDITION_DISPLAY_MODE_LABEL"
                    description="MOD_PRODUCT_CONDITION_DISPLAY_MODE_DESC">
                    <option value="filter">MOD_PRODUCT_CONDITION_MODE_FILTER</option>
                    <option value="badge">MOD_PRODUCT_CONDITION_MODE_BADGE</option>
                    <option value="list">MOD_PRODUCT_CONDITION_MODE_LIST</option>
                </field>
                
                <field
                    name="show_percentage"
                    type="radio"
                    default="1"
                    label="MOD_PRODUCT_CONDITION_SHOW_PERCENTAGE_LABEL"
                    description="MOD_PRODUCT_CONDITION_SHOW_PERCENTAGE_DESC">
                    <option value="0">JNO</option>
                    <option value="1">JYES</option>
                </field>
                
                <field
                    name="show_description"
                    type="radio"
                    default="1"
                    label="MOD_PRODUCT_CONDITION_SHOW_DESCRIPTION_LABEL"
                    description="MOD_PRODUCT_CONDITION_SHOW_DESCRIPTION_DESC">
                    <option value="0">JNO</option>
                    <option value="1">JYES</option>
                </field>
                
                <field
                    name="show_color_indicator"
                    type="radio"
                    default="1"
                    label="MOD_PRODUCT_CONDITION_SHOW_COLOR_LABEL"
                    description="MOD_PRODUCT_CONDITION_SHOW_COLOR_DESC">
                    <option value="0">JNO</option>
                    <option value="1">JYES</option>
                </field>
                
                <field
                    name="enable_ajax_filter"
                    type="radio"
                    default="1"
                    label="MOD_PRODUCT_CONDITION_AJAX_FILTER_LABEL"
                    description="MOD_PRODUCT_CONDITION_AJAX_FILTER_DESC">
                    <option value="0">JNO</option>
                    <option value="1">JYES</option>
                </field>
                
                <field
                    name="module_class_sfx"
                    type="text"
                    default=""
                    label="MOD_PRODUCT_CONDITION_MODULE_CLASS_LABEL"
                    description="MOD_PRODUCT_CONDITION_MODULE_CLASS_DESC" />
                    
                <field
                    name="cache"
                    type="list"
                    default="1"
                    label="COM_MODULES_FIELD_CACHING_LABEL"
                    description="COM_MODULES_FIELD_CACHING_DESC">
                    <option value="1">JGLOBAL_USE_GLOBAL</option>
                    <option value="0">COM_MODULES_FIELD_VALUE_NOCACHING</option>
                </field>
                
                <field
                    name="cache_time"
                    type="text"
                    default="900"
                    label="COM_MODULES_FIELD_CACHE_TIME_LABEL"
                    description="COM_MODULES_FIELD_CACHE_TIME_DESC" />
            </fieldset>
            
            <fieldset name="advanced">
                <field
                    name="layout"
                    type="modulelayout"
                    label="JFIELD_ALT_LAYOUT_LABEL"
                    description="JFIELD_ALT_MODULE_LAYOUT_DESC" />
                    
                <field
                    name="moduleclass_sfx"
                    type="textarea"
                    rows="3"
                    label="COM_MODULES_FIELD_MODULECLASS_SFX_LABEL"
                    description="COM_MODULES_FIELD_MODULECLASS_SFX_DESC" />
                    
                <field
                    name="header_tag"
                    type="list"
                    default="h3"
                    label="COM_MODULES_FIELD_HEADER_TAG_LABEL"
                    description="COM_MODULES_FIELD_HEADER_TAG_DESC">
                    <option value="h1">H1</option>
                    <option value="h2">H2</option>
                    <option value="h3">H3</option>
                    <option value="h4">H4</option>
                    <option value="h5">H5</option>
                    <option value="h6">H6</option>
                </field>
                
                <field
                    name="header_class"
                    type="text"
                    default=""
                    label="COM_MODULES_FIELD_HEADER_CLASS_LABEL"
                    description="COM_MODULES_FIELD_HEADER_CLASS_DESC" />
                    
                <field
                    name="style"
                    type="list"
                    default="0"
                    label="COM_MODULES_FIELD_MODULE_STYLE_LABEL"
                    description="COM_MODULES_FIELD_MODULE_STYLE_DESC">
                    <option value="0">COM_MODULES_FIELD_VALUE_USE_GLOBAL</option>
                    <option value="table">table</option>
                    <option value="horz">horz</option>
                    <option value="xhtml">xhtml</option>
                    <option value="rounded">rounded</option>
                    <option value="outline">outline</option>
                    <option value="none">none</option>
                </field>
            </fieldset>
        </fields>
    </config>
</extension>
