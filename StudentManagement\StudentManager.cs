using System;
using System.Collections.Generic;
using System.Linq;

namespace StudentManagement
{
    /// <summary>
    /// Lớp quản lý sinh viên với các chức năng CRUD
    /// </summary>
    public class StudentManager
    {
        private SingleLinkedList singleList;
        private DoublyLinkedList doublyList;
        private bool useSingleList;

        public StudentManager(bool useSingleLinkedList = true)
        {
            singleList = new SingleLinkedList();
            doublyList = new DoublyLinkedList();
            useSingleList = useSingleLinkedList;
        }

        /// <summary>
        /// Chuyển đổi giữa danh sách liên kết đơn và kép
        /// </summary>
        public void SwitchListType()
        {
            useSingleList = !useSingleList;
            Console.WriteLine($"Đ<PERSON> chuyển sang sử dụng danh sách liên kết {(useSingleList ? "đơn" : "kép")}");
        }

        /// <summary>
        /// Thêm sinh viên mới
        /// </summary>
        public void AddStudent(Student student)
        {
            if (useSingleList)
                singleList.AddLast(student);
            else
                doublyList.AddLast(student);
        }

        /// <summary>
        /// Xóa sinh viên theo mã
        /// </summary>
        public bool RemoveStudent(string maSinhVien)
        {
            if (useSingleList)
                return singleList.Remove(maSinhVien);
            else
                return doublyList.Remove(maSinhVien);
        }

        /// <summary>
        /// Tìm sinh viên theo mã
        /// </summary>
        public Student FindStudent(string maSinhVien)
        {
            if (useSingleList)
                return singleList.Find(maSinhVien);
            else
                return doublyList.Find(maSinhVien);
        }

        /// <summary>
        /// Tìm sinh viên theo tên
        /// </summary>
        public List<Student> FindStudentsByName(string hoTen)
        {
            if (useSingleList)
                return singleList.FindByName(hoTen);
            else
                return doublyList.FindByName(hoTen);
        }

        /// <summary>
        /// Lấy tất cả sinh viên
        /// </summary>
        public List<Student> GetAllStudents()
        {
            if (useSingleList)
                return singleList.GetAll();
            else
                return doublyList.GetAll();
        }

        /// <summary>
        /// Lấy tất cả sinh viên theo thứ tự ngược (chỉ cho danh sách kép)
        /// </summary>
        public List<Student> GetAllStudentsReverse()
        {
            if (!useSingleList)
                return doublyList.GetAllReverse();
            else
            {
                var list = singleList.GetAll();
                list.Reverse();
                return list;
            }
        }

        /// <summary>
        /// Sắp xếp sinh viên theo điểm trung bình
        /// </summary>
        public void SortStudentsByGPA()
        {
            if (useSingleList)
                singleList.SortByGPA();
            else
                doublyList.SortByGPA();
        }

        /// <summary>
        /// Lấy số lượng sinh viên
        /// </summary>
        public int GetStudentCount()
        {
            if (useSingleList)
                return singleList.Count;
            else
                return doublyList.Count;
        }

        /// <summary>
        /// Xóa tất cả sinh viên
        /// </summary>
        public void ClearAllStudents()
        {
            if (useSingleList)
                singleList.Clear();
            else
                doublyList.Clear();
        }

        /// <summary>
        /// Kiểm tra danh sách có rỗng không
        /// </summary>
        public bool IsEmpty()
        {
            if (useSingleList)
                return singleList.IsEmpty();
            else
                return doublyList.IsEmpty();
        }

        /// <summary>
        /// Lấy thống kê sinh viên
        /// </summary>
        public void GetStatistics()
        {
            var students = GetAllStudents();
            if (students.Count == 0)
            {
                Console.WriteLine("Không có sinh viên nào trong danh sách.");
                return;
            }

            double avgGPA = students.Average(s => s.DiemTrungBinh);
            double maxGPA = students.Max(s => s.DiemTrungBinh);
            double minGPA = students.Min(s => s.DiemTrungBinh);
            
            var maleCount = students.Count(s => s.GioiTinh.ToLower() == "nam");
            var femaleCount = students.Count(s => s.GioiTinh.ToLower() == "nữ");

            Console.WriteLine("╔══════════════════════════════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                                  THỐNG KÊ SINH VIÊN                                 ║");
            Console.WriteLine("╠══════════════════════════════════════════════════════════════════════════════════════╣");
            Console.WriteLine($"║ Tổng số sinh viên        : {students.Count,-20}                                ║");
            Console.WriteLine($"║ Sinh viên nam            : {maleCount,-20}                                ║");
            Console.WriteLine($"║ Sinh viên nữ             : {femaleCount,-20}                                ║");
            Console.WriteLine($"║ Điểm trung bình cao nhất : {maxGPA:F2}                                                      ║");
            Console.WriteLine($"║ Điểm trung bình thấp nhất: {minGPA:F2}                                                      ║");
            Console.WriteLine($"║ Điểm trung bình chung    : {avgGPA:F2}                                                      ║");
            Console.WriteLine($"║ Loại danh sách đang dùng : {(useSingleList ? "Danh sách liên kết đơn" : "Danh sách liên kết kép"),-20} ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════════════════════════════╝");
        }

        /// <summary>
        /// Tìm sinh viên có điểm cao nhất
        /// </summary>
        public Student FindTopStudent()
        {
            var students = GetAllStudents();
            if (students.Count == 0) return null;
            
            return students.OrderByDescending(s => s.DiemTrungBinh).First();
        }

        /// <summary>
        /// Tìm sinh viên theo lớp
        /// </summary>
        public List<Student> FindStudentsByClass(string lop)
        {
            var students = GetAllStudents();
            return students.Where(s => s.Lop.ToLower().Contains(lop.ToLower())).ToList();
        }
    }
}
