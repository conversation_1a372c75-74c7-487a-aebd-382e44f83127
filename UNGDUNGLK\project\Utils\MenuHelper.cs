using System;

namespace DoublyLinkedListApp.Utils
{
    /// <summary>
    /// Helper class để hiển thị menu và xử lý input
    /// </summary>
    public static class MenuHelper
    {
        /// <summary>
        /// Hiển thị menu chính
        /// </summary>
        public static void HienThiMenuChinh()
        {
            Console.Clear();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║              QUẢN LÝ HÀNG HÓA - DANH SÁCH LIÊN KẾT KÉP       ║");
            Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
            Console.WriteLine("║  1. Thêm hàng hóa                                            ║");
            Console.WriteLine("║  2. Xóa hàng hóa                                             ║");
            Console.WriteLine("║  3. Tìm kiếm hàng hóa                                        ║");
            Console.WriteLine("║  4. Hiển thị danh sách                                       ║");
            Console.WriteLine("║  5. Sắp xếp dữ liệu                                          ║");
            Console.WriteLine("║  6. L<PERSON>c dữ liệu                                              ║");
            Console.WriteLine("║  7. Thống kê                                                 ║");
            Console.WriteLine("║  8. Cập nhật thông tin                                       ║");
            Console.WriteLine("║  9. Tạo dữ liệu mẫu                                          ║");
            Console.WriteLine("║  0. Thoát chương trình                                       ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
            Console.Write("Chọn chức năng (0-9): ");
        }

        /// <summary>
        /// Hiển thị menu tìm kiếm
        /// </summary>
        public static void HienThiMenuTimKiem()
        {
            Console.Clear();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                        TÌM KIẾM HÀNG HÓA                     ║");
            Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
            Console.WriteLine("║  1. Tìm kiếm theo mã số                                      ║");
            Console.WriteLine("║  2. Tìm kiếm theo tên hàng                                   ║");
            Console.WriteLine("║  0. Quay lại menu chính                                      ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
            Console.Write("Chọn chức năng (0-2): ");
        }

        /// <summary>
        /// Hiển thị menu sắp xếp
        /// </summary>
        public static void HienThiMenuSapXep()
        {
            Console.Clear();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                        SẮP XẾP DỮ LIỆU                       ║");
            Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
            Console.WriteLine("║  1. Sắp xếp theo đơn giá (tăng dần)                          ║");
            Console.WriteLine("║  2. Sắp xếp theo đơn giá (giảm dần)                          ║");
            Console.WriteLine("║  3. Sắp xếp theo tên hàng (A-Z)                              ║");
            Console.WriteLine("║  4. Sắp xếp theo tên hàng (Z-A)                              ║");
            Console.WriteLine("║  5. Sắp xếp theo số lượng (tăng dần)                         ║");
            Console.WriteLine("║  6. Sắp xếp theo số lượng (giảm dần)                         ║");
            Console.WriteLine("║  0. Quay lại menu chính                                      ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
            Console.Write("Chọn chức năng (0-6): ");
        }

        /// <summary>
        /// Hiển thị menu lọc dữ liệu
        /// </summary>
        public static void HienThiMenuLoc()
        {
            Console.Clear();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                         LỌC DỮ LIỆU                          ║");
            Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
            Console.WriteLine("║  1. Lọc theo đơn vị tính                                     ║");
            Console.WriteLine("║  2. Lọc theo khoảng giá                                      ║");
            Console.WriteLine("║  3. Lọc theo trạng thái                                      ║");
            Console.WriteLine("║  4. Lọc theo phân loại giá                                   ║");
            Console.WriteLine("║  0. Quay lại menu chính                                      ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
            Console.Write("Chọn chức năng (0-4): ");
        }

        /// <summary>
        /// Hiển thị menu thống kê
        /// </summary>
        public static void HienThiMenuThongKe()
        {
            Console.Clear();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                          THỐNG KÊ                            ║");
            Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
            Console.WriteLine("║  1. Thống kê số lượng theo đơn vị tính                      ║");
            Console.WriteLine("║  2. Thống kê số lượng theo trạng thái                        ║");
            Console.WriteLine("║  3. Tính tổng giá trị hàng hóa                               ║");
            Console.WriteLine("║  4. Tìm hàng hóa giá cao nhất                                ║");
            Console.WriteLine("║  5. Tìm hàng hóa giá thấp nhất                               ║");
            Console.WriteLine("║  6. Hiển thị tổng số hàng hóa                                ║");
            Console.WriteLine("║  0. Quay lại menu chính                                      ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
            Console.Write("Chọn chức năng (0-6): ");
        }

        /// <summary>
        /// Đọc số nguyên từ input
        /// </summary>
        public static int DocSoNguyen(string prompt, int min = int.MinValue, int max = int.MaxValue)
        {
            int result;
            while (true)
            {
                Console.Write(prompt);
                if (int.TryParse(Console.ReadLine(), out result) && result >= min && result <= max)
                {
                    return result;
                }
                Console.WriteLine($"Vui lòng nhập số nguyên từ {min} đến {max}!");
            }
        }

        /// <summary>
        /// Đọc số thực từ input
        /// </summary>
        public static double DocSoThuc(string prompt, double min = double.MinValue, double max = double.MaxValue)
        {
            double result;
            while (true)
            {
                Console.Write(prompt);
                if (double.TryParse(Console.ReadLine(), out result) && result >= min && result <= max)
                {
                    return result;
                }
                Console.WriteLine($"Vui lòng nhập số thực từ {min} đến {max}!");
            }
        }

        /// <summary>
        /// Đọc chuỗi không rỗng từ input
        /// </summary>
        public static string DocChuoiKhongRong(string prompt)
        {
            string? result;
            while (true)
            {
                Console.Write(prompt);
                result = Console.ReadLine()?.Trim();
                if (!string.IsNullOrEmpty(result))
                {
                    return result;
                }
                Console.WriteLine("Vui lòng nhập thông tin hợp lệ!");
            }
        }

        /// <summary>
        /// Tạm dừng và chờ người dùng nhấn phím
        /// </summary>
        public static void TamDung()
        {
            Console.WriteLine("\nNhấn phím bất kỳ để tiếp tục...");
            Console.ReadKey();
        }

        /// <summary>
        /// Hiển thị tiêu đề
        /// </summary>
        public static void HienThiTieuDe(string tieuDe)
        {
            Console.Clear();
            int width = Math.Max(tieuDe.Length + 4, 50);
            string border = new string('═', width - 2);
            
            Console.WriteLine($"╔{border}╗");
            Console.WriteLine($"║{tieuDe.PadLeft((width + tieuDe.Length) / 2).PadRight(width - 2)}║");
            Console.WriteLine($"╚{border}╝");
            Console.WriteLine();
        }
    }
}
