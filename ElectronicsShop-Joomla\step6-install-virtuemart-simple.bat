@echo off
title Step 6 - Install VirtueMart E-commerce
color 0A

echo ========================================
echo    STEP 6: INSTALL VIRTUEMART
echo ========================================
echo.

echo VirtueMart will add e-commerce functionality:
echo - Product catalog
echo - Shopping cart
echo - Order management
echo - Payment methods
echo - Shipping options
echo.

echo ========================================
echo    DOWNLOAD VIRTUEMART
echo ========================================
echo.

echo Opening VirtueMart download page...
start "" "https://virtuemart.net/downloads"

echo.
echo DOWNLOAD THESE FILES:
echo =====================
echo.
echo 1. VirtueMart 4.x Core Component
echo    - File: com_virtuemart_4.x.x.zip
echo    - This is the main VirtueMart component
echo.
echo 2. Standard Payment Methods
echo    - File: plg_vmpayment_standard.zip
echo    - For COD, Bank Transfer payments
echo.
echo 3. Standard Shipment Methods  
echo    - File: plg_vmshipment_standard.zip
echo    - For shipping calculations
echo.
echo 4. Vietnamese Language Pack (if available)
echo    - File: virtuemart_vi-VN.zip
echo    - For Vietnamese interface
echo.

echo Press any key when downloads are complete...
pause

echo ========================================
echo    INSTALL VIRTUEMART
echo ========================================
echo.

echo Opening Joomla Admin Panel...
start "" "http://localhost/electronics-shop/administrator"

echo.
echo INSTALLATION STEPS IN JOOMLA ADMIN:
echo ===================================
echo.
echo 1. LOGIN:
echo    - Username: admin
echo    - Password: admin123
echo.
echo 2. INSTALL EXTENSIONS:
echo    - Go to: System > Install > Extensions
echo    - Click "Upload Package File" tab
echo    - Install in this order:
echo.
echo    a) Upload: com_virtuemart_4.x.x.zip
echo       Click "Upload & Install"
echo       Wait for success message
echo.
echo    b) Upload: plg_vmpayment_standard.zip
echo       Click "Upload & Install"
echo.
echo    c) Upload: plg_vmshipment_standard.zip
echo       Click "Upload & Install"
echo.
echo    d) Upload: virtuemart_vi-VN.zip (if downloaded)
echo       Click "Upload & Install"
echo.
echo 3. CONFIGURE VIRTUEMART:
echo    - Go to: Components > VirtueMart
echo    - Follow the setup wizard
echo    - Install sample data if prompted
echo.

echo Press any key when VirtueMart is installed...
pause

echo ========================================
echo    TEST VIRTUEMART
echo ========================================
echo.

echo Testing VirtueMart shop...
start "" "http://localhost/electronics-shop/index.php?option=com_virtuemart"

echo.
echo You should see the VirtueMart shop page.
echo If you see products and categories, VirtueMart is working!
echo.

echo ========================================
echo    STEP 6 COMPLETE!
echo ========================================
echo.
echo [SUCCESS] VirtueMart e-commerce is ready!
echo.
echo Your website now has:
echo - Joomla CMS: http://localhost/electronics-shop
echo - VirtueMart Shop: http://localhost/electronics-shop/index.php?option=com_virtuemart
echo - Admin Panel: http://localhost/electronics-shop/administrator
echo.
echo Next: Configure products, categories, and payment methods
echo.
pause
