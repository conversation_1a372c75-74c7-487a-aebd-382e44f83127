-- =============================================
-- Database Design for Electronics Shop
-- Website bán thiết bị điện tử cũ
-- =============================================

-- Tạo database
CREATE DATABASE IF NOT EXISTS electronics_shop 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE electronics_shop;

-- =============================================
-- Bảng mở rộng cho VirtueMart
-- =============================================

-- Bảng tình trạng sản phẩm điện tử cũ
CREATE TABLE IF NOT EXISTS `vm_product_condition` (
    `condition_id` int(11) NOT NULL AUTO_INCREMENT,
    `condition_name` varchar(100) NOT NULL,
    `condition_name_en` varchar(100) NOT NULL,
    `condition_description` text,
    `condition_percentage` int(3) NOT NULL DEFAULT 100,
    `condition_color` varchar(7) DEFAULT '#28a745',
    `ordering` int(11) NOT NULL DEFAULT 0,
    `published` tinyint(1) NOT NULL DEFAULT 1,
    `created_on` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `modified_on` datetime DEFAULT NULL,
    PRIMARY KEY (`condition_id`),
    KEY `idx_published` (`published`),
    KEY `idx_ordering` (`ordering`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dữ liệu mẫu cho tình trạng sản phẩm
INSERT INTO `vm_product_condition` (`condition_name`, `condition_name_en`, `condition_description`, `condition_percentage`, `condition_color`, `ordering`) VALUES
('Như mới', 'Like New', 'Sản phẩm ít sử dụng, không có vết xước, hoạt động hoàn hảo', 95, '#28a745', 1),
('Tốt', 'Good', 'Hoạt động bình thường, có thể có vết xước nhẹ không ảnh hưởng chức năng', 85, '#17a2b8', 2),
('Khá tốt', 'Fair', 'Hoạt động ổn định, có dấu hiệu sử dụng rõ ràng', 75, '#ffc107', 3),
('Cần sửa chữa', 'Needs Repair', 'Có lỗi nhỏ, cần khắc phục một số vấn đề', 60, '#dc3545', 4);

-- Bảng thông tin bảo hành mở rộng
CREATE TABLE IF NOT EXISTS `vm_product_warranty` (
    `warranty_id` int(11) NOT NULL AUTO_INCREMENT,
    `virtuemart_product_id` int(11) NOT NULL,
    `warranty_type` enum('shop','manufacturer','none') NOT NULL DEFAULT 'shop',
    `warranty_duration` int(11) NOT NULL DEFAULT 0, -- Số tháng bảo hành
    `warranty_description` text,
    `warranty_terms` text,
    `warranty_start_date` date DEFAULT NULL,
    `warranty_end_date` date DEFAULT NULL,
    `is_transferable` tinyint(1) NOT NULL DEFAULT 1,
    `created_on` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `modified_on` datetime DEFAULT NULL,
    PRIMARY KEY (`warranty_id`),
    KEY `idx_product_id` (`virtuemart_product_id`),
    KEY `idx_warranty_type` (`warranty_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bảng thông số kỹ thuật chi tiết
CREATE TABLE IF NOT EXISTS `vm_product_specifications` (
    `spec_id` int(11) NOT NULL AUTO_INCREMENT,
    `virtuemart_product_id` int(11) NOT NULL,
    `spec_category` varchar(50) NOT NULL, -- 'hardware', 'display', 'battery', 'camera', etc.
    `spec_name` varchar(100) NOT NULL,
    `spec_value` text NOT NULL,
    `spec_unit` varchar(20) DEFAULT NULL,
    `ordering` int(11) NOT NULL DEFAULT 0,
    `published` tinyint(1) NOT NULL DEFAULT 1,
    PRIMARY KEY (`spec_id`),
    KEY `idx_product_id` (`virtuemart_product_id`),
    KEY `idx_category` (`spec_category`),
    KEY `idx_published` (`published`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bảng lịch sử giá sản phẩm
CREATE TABLE IF NOT EXISTS `vm_product_price_history` (
    `history_id` int(11) NOT NULL AUTO_INCREMENT,
    `virtuemart_product_id` int(11) NOT NULL,
    `old_price` decimal(15,5) NOT NULL,
    `new_price` decimal(15,5) NOT NULL,
    `price_change_reason` varchar(255) DEFAULT NULL,
    `changed_by` int(11) NOT NULL, -- User ID
    `changed_on` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`history_id`),
    KEY `idx_product_id` (`virtuemart_product_id`),
    KEY `idx_changed_on` (`changed_on`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bảng đánh giá chi tiết sản phẩm
CREATE TABLE IF NOT EXISTS `vm_product_detailed_reviews` (
    `review_id` int(11) NOT NULL AUTO_INCREMENT,
    `virtuemart_product_id` int(11) NOT NULL,
    `user_id` int(11) NOT NULL,
    `order_id` int(11) DEFAULT NULL,
    `overall_rating` decimal(2,1) NOT NULL DEFAULT 0.0,
    `condition_rating` decimal(2,1) NOT NULL DEFAULT 0.0,
    `performance_rating` decimal(2,1) NOT NULL DEFAULT 0.0,
    `value_rating` decimal(2,1) NOT NULL DEFAULT 0.0,
    `review_title` varchar(255) NOT NULL,
    `review_content` text NOT NULL,
    `pros` text DEFAULT NULL,
    `cons` text DEFAULT NULL,
    `verified_purchase` tinyint(1) NOT NULL DEFAULT 0,
    `helpful_votes` int(11) NOT NULL DEFAULT 0,
    `total_votes` int(11) NOT NULL DEFAULT 0,
    `published` tinyint(1) NOT NULL DEFAULT 0,
    `created_on` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `modified_on` datetime DEFAULT NULL,
    PRIMARY KEY (`review_id`),
    KEY `idx_product_id` (`virtuemart_product_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_published` (`published`),
    KEY `idx_overall_rating` (`overall_rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bảng hình ảnh chi tiết sản phẩm
CREATE TABLE IF NOT EXISTS `vm_product_detailed_images` (
    `image_id` int(11) NOT NULL AUTO_INCREMENT,
    `virtuemart_product_id` int(11) NOT NULL,
    `image_type` enum('main','gallery','condition','packaging','accessories') NOT NULL DEFAULT 'gallery',
    `image_url` varchar(500) NOT NULL,
    `image_title` varchar(255) DEFAULT NULL,
    `image_description` text DEFAULT NULL,
    `ordering` int(11) NOT NULL DEFAULT 0,
    `published` tinyint(1) NOT NULL DEFAULT 1,
    `created_on` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`image_id`),
    KEY `idx_product_id` (`virtuemart_product_id`),
    KEY `idx_image_type` (`image_type`),
    KEY `idx_published` (`published`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bảng phụ kiện đi kèm
CREATE TABLE IF NOT EXISTS `vm_product_accessories` (
    `accessory_id` int(11) NOT NULL AUTO_INCREMENT,
    `virtuemart_product_id` int(11) NOT NULL,
    `accessory_name` varchar(255) NOT NULL,
    `accessory_description` text DEFAULT NULL,
    `is_included` tinyint(1) NOT NULL DEFAULT 1,
    `condition_note` varchar(255) DEFAULT NULL,
    `ordering` int(11) NOT NULL DEFAULT 0,
    PRIMARY KEY (`accessory_id`),
    KEY `idx_product_id` (`virtuemart_product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bảng lịch sử sở hữu (cho thiết bị cũ)
CREATE TABLE IF NOT EXISTS `vm_product_ownership_history` (
    `ownership_id` int(11) NOT NULL AUTO_INCREMENT,
    `virtuemart_product_id` int(11) NOT NULL,
    `previous_owner_info` text DEFAULT NULL,
    `purchase_date` date DEFAULT NULL,
    `usage_duration` varchar(100) DEFAULT NULL,
    `usage_purpose` varchar(255) DEFAULT NULL,
    `reason_for_selling` text DEFAULT NULL,
    `original_price` decimal(15,5) DEFAULT NULL,
    `original_warranty_info` text DEFAULT NULL,
    PRIMARY KEY (`ownership_id`),
    KEY `idx_product_id` (`virtuemart_product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- Views để truy vấn dữ liệu dễ dàng
-- =============================================

-- View sản phẩm với thông tin đầy đủ
CREATE OR REPLACE VIEW `view_products_full_info` AS
SELECT 
    p.virtuemart_product_id,
    p.product_name,
    p.product_s_desc,
    p.product_desc,
    p.product_sku,
    p.product_weight,
    p.product_length,
    p.product_width,
    p.product_height,
    p.published,
    p.created_on,
    p.modified_on,
    pr.product_price,
    pr.product_currency,
    pc.condition_name,
    pc.condition_percentage,
    pc.condition_color,
    pw.warranty_type,
    pw.warranty_duration,
    pw.warranty_description,
    AVG(pdr.overall_rating) as avg_rating,
    COUNT(pdr.review_id) as review_count
FROM `#__virtuemart_products` p
LEFT JOIN `#__virtuemart_product_prices` pr ON p.virtuemart_product_id = pr.virtuemart_product_id
LEFT JOIN `vm_product_condition` pc ON p.condition_id = pc.condition_id
LEFT JOIN `vm_product_warranty` pw ON p.virtuemart_product_id = pw.virtuemart_product_id
LEFT JOIN `vm_product_detailed_reviews` pdr ON p.virtuemart_product_id = pdr.virtuemart_product_id AND pdr.published = 1
WHERE p.published = 1
GROUP BY p.virtuemart_product_id;

-- =============================================
-- Indexes để tối ưu hiệu suất
-- =============================================

-- Indexes cho bảng VirtueMart products (nếu chưa có)
ALTER TABLE `#__virtuemart_products` 
ADD INDEX `idx_published_created` (`published`, `created_on`),
ADD INDEX `idx_sku` (`product_sku`);

-- Indexes cho bảng VirtueMart categories
ALTER TABLE `#__virtuemart_categories` 
ADD INDEX `idx_published_ordering` (`published`, `ordering`);

-- =============================================
-- Stored Procedures
-- =============================================

DELIMITER //

-- Procedure để cập nhật rating trung bình
CREATE PROCEDURE UpdateProductAverageRating(IN product_id INT)
BEGIN
    DECLARE avg_rating DECIMAL(2,1);
    DECLARE review_count INT;
    
    SELECT AVG(overall_rating), COUNT(*) 
    INTO avg_rating, review_count
    FROM vm_product_detailed_reviews 
    WHERE virtuemart_product_id = product_id AND published = 1;
    
    -- Cập nhật vào bảng sản phẩm (cần thêm cột nếu chưa có)
    UPDATE `#__virtuemart_products` 
    SET average_rating = IFNULL(avg_rating, 0),
        review_count = IFNULL(review_count, 0)
    WHERE virtuemart_product_id = product_id;
END //

DELIMITER ;

-- =============================================
-- Triggers
-- =============================================

DELIMITER //

-- Trigger để tự động cập nhật rating khi có review mới
CREATE TRIGGER after_review_insert 
AFTER INSERT ON vm_product_detailed_reviews
FOR EACH ROW
BEGIN
    CALL UpdateProductAverageRating(NEW.virtuemart_product_id);
END //

CREATE TRIGGER after_review_update 
AFTER UPDATE ON vm_product_detailed_reviews
FOR EACH ROW
BEGIN
    CALL UpdateProductAverageRating(NEW.virtuemart_product_id);
END //

CREATE TRIGGER after_review_delete 
AFTER DELETE ON vm_product_detailed_reviews
FOR EACH ROW
BEGIN
    CALL UpdateProductAverageRating(OLD.virtuemart_product_id);
END //

DELIMITER ;
