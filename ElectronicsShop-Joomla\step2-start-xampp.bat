@echo off
title Step 2 - Start XAMPP Services
color 0A

echo ========================================
echo    STEP 2: START XAMPP SERVICES
echo ========================================
echo.

echo Checking XAMPP installation...
if exist "C:\xampp\xampp-control.exe" (
    echo [OK] XAMPP found at C:\xampp\
) else (
    echo [ERROR] XAMPP not found!
    echo Please make sure XAMPP is installed correctly.
    pause
    exit
)

echo.
echo ========================================
echo    STARTING XAMPP CONTROL PANEL
echo ========================================
echo.

echo Opening XAMPP Control Panel...
start "" "C:\xampp\xampp-control.exe"

echo.
echo XAMPP Control Panel should now be open.
echo.
echo IMPORTANT STEPS:
echo ================
echo.
echo In XAMPP Control Panel, you need to:
echo.
echo 1. Click [Start] button next to "Apache"
echo    - It should turn GREEN and show "Running"
echo    - Port should show 80, 443
echo.
echo 2. Click [Start] button next to "MySQL" 
echo    - It should turn GREEN and show "Running"
echo    - Port should show 3306
echo.
echo 3. If you see any RED errors:
echo    - Click [Config] button to change ports
echo    - Or close other programs using those ports
echo.
echo Press any key when BOTH Apache and MySQL are GREEN...
pause

echo ========================================
echo    TESTING XAMPP
echo ========================================
echo.

echo Testing if Apache is working...
echo Opening http://localhost...
start "" "http://localhost"

echo.
echo You should see the XAMPP welcome page.
echo If you see it, Apache is working correctly!
echo.
echo Press any key if you can see the XAMPP welcome page...
pause

echo ========================================
echo    TESTING PHPMYADMIN
echo ========================================
echo.

echo Testing if MySQL is working...
echo Opening phpMyAdmin...
start "" "http://localhost/phpmyadmin"

echo.
echo You should see the phpMyAdmin login page.
echo If you see it, MySQL is working correctly!
echo.
echo Press any key if you can see phpMyAdmin...
pause

echo ========================================
echo    STEP 2 COMPLETE!
echo ========================================
echo.
echo [SUCCESS] XAMPP services are running!
echo.
echo What's working now:
echo - Apache web server: http://localhost
echo - MySQL database: http://localhost/phpmyadmin
echo - Ready for Joomla installation
echo.
echo Next step: Create database and install Joomla
echo.
echo Press any key to continue to Step 3...
pause
