# TÀI LIỆU THAM KHẢO

## <PERSON><PERSON>ch giáo khoa

### 1. Introduction to Algorithms (CLRS)
- **T<PERSON>c giả**: <PERSON>, <PERSON>, <PERSON>, <PERSON>
- **<PERSON><PERSON><PERSON> bản**: Third Edition (2009)
- **Chương liên quan**: 
  - Chapter 24: Single-Source Shortest Paths
  - Chapter 25: All-Pairs Shortest Paths
- **Nội dung**: Tr<PERSON><PERSON> bày chi tiết các gi<PERSON><PERSON>u<PERSON>, <PERSON><PERSON><PERSON>, Floyd-Warshall với chứng minh toán học đầy đủ

### 2. Algorithms (Sedgewick & Wayne)
- **Tác giả**: <PERSON>, <PERSON>
- **<PERSON><PERSON><PERSON> bản**: Fourth Edition (2011)
- **Chương liên quan**: 
  - Chapter 4.4: Shortest Paths
- **Nội dung**: <PERSON><PERSON><PERSON> tiế<PERSON> cận thực tế với nhiều ví dụ code và ứng dụng

### 3. Algorithm Design (<PERSON>berg & <PERSON>rdos)
- **<PERSON><PERSON><PERSON> g<PERSON>**: <PERSON>, <PERSON><PERSON>
- **<PERSON>ăm xuất bản**: 2005
- **Chương liên quan**: 
  - Chapter 4: Greedy Algorithms (Dijkstra)
  - Chapter 6: Dynamic Programming (Bellman-Ford, Floyd-Warshall)
- **Nội dung**: Phân tích thiết kế giải thuật và kỹ thuật tối ưu hóa

## Bài báo khoa học

### 1. "A Note on Two Problems in Connexion with Graphs"
- **Tác giả**: Edsger W. Dijkstra
- **Năm**: 1959
- **Tạp chí**: Numerische Mathematik, Vol. 1, pp. 269-271
- **Nội dung**: Bài báo gốc giới thiệu giải thuật Dijkstra

### 2. "On a Routing Problem"
- **Tác giả**: Richard Bellman
- **Năm**: 1958
- **Tạp chí**: Quarterly of Applied Mathematics, Vol. 16, pp. 87-90
- **Nội dung**: Giới thiệu nguyên lý của giải thuật Bellman-Ford

### 3. "Algorithm 97: Shortest Path"
- **Tác giả**: Robert W. Floyd
- **Năm**: 1962
- **Tạp chí**: Communications of the ACM, Vol. 5, No. 6, p. 345
- **Nội dung**: Mô tả giải thuật Floyd-Warshall

## Tài liệu trực tuyến

### 1. GeeksforGeeks
- **URL**: https://www.geeksforgeeks.org/
- **Bài viết liên quan**:
  - Dijkstra's shortest path algorithm
  - Bellman–Ford Algorithm
  - Floyd Warshall Algorithm
- **Ưu điểm**: Giải thích dễ hiểu với nhiều ví dụ code

### 2. Wikipedia
- **URL**: https://en.wikipedia.org/
- **Bài viết**:
  - Shortest path problem
  - Dijkstra's algorithm
  - Bellman–Ford algorithm
  - Floyd–Warshall algorithm
- **Ưu điểm**: Thông tin toàn diện với tham khảo học thuật

### 3. Coursera - Algorithms Specialization
- **Tác giả**: Stanford University (Tim Roughgarden)
- **URL**: https://www.coursera.org/specializations/algorithms
- **Nội dung**: Video lectures về graph algorithms và shortest paths

### 4. MIT OpenCourseWare
- **Khóa học**: 6.006 Introduction to Algorithms
- **URL**: https://ocw.mit.edu/courses/electrical-engineering-and-computer-science/6-006-introduction-to-algorithms-fall-2011/
- **Lecture**: Dijkstra, Bellman-Ford algorithms

## Tài liệu tiếng Việt

### 1. "Cấu trúc dữ liệu và giải thuật"
- **Tác giả**: Lê Minh Hoàng
- **Nhà xuất bản**: Đại học Quốc gia Hà Nội
- **Chương**: Đồ thị và các giải thuật trên đồ thị

### 2. "Giải thuật và lập trình"
- **Tác giả**: Nguyễn Đức Nghĩa, Nguyễn Tô Thành
- **Nhà xuất bản**: Đại học Bách khoa Hà Nội
- **Nội dung**: Ứng dụng thực tế của các giải thuật đồ thị

### 3. VNOI Wiki
- **URL**: https://vnoi.info/wiki/
- **Bài viết**:
  - Đường đi ngắn nhất trên đồ thị
  - Thuật toán Dijkstra
  - Thuật toán Bellman-Ford
  - Thuật toán Floyd-Warshall

## Công cụ và thư viện

### 1. NetworkX (Python)
- **URL**: https://networkx.org/
- **Mô tả**: Thư viện Python cho phân tích đồ thị
- **Tính năng**: Cài đặt sẵn các giải thuật shortest path

### 2. Boost Graph Library (C++)
- **URL**: https://www.boost.org/doc/libs/1_78_0/libs/graph/doc/
- **Mô tả**: Thư viện C++ cho xử lý đồ thị
- **Tính năng**: Hiệu suất cao cho đồ thị lớn

### 3. igraph (R/Python)
- **URL**: https://igraph.org/
- **Mô tả**: Thư viện đa ngôn ngữ cho phân tích mạng
- **Tính năng**: Visualization và analysis tools

## Ứng dụng thực tế

### 1. Google Maps / GPS Navigation
- **Công nghệ**: Biến thể của Dijkstra với A* heuristic
- **Tài liệu**: "Route Planning in Transportation Networks" (Geisberger et al., 2008)

### 2. Internet Routing Protocols
- **OSPF**: Open Shortest Path First protocol
- **BGP**: Border Gateway Protocol
- **Tài liệu**: RFC 2328 (OSPF), RFC 4271 (BGP)

### 3. Social Network Analysis
- **Ứng dụng**: Tìm mức độ kết nối giữa người dùng
- **Tài liệu**: "Networks, Crowds, and Markets" (Easley & Kleinberg, 2010)

## Video tutorials

### 1. YouTube - Abdul Bari
- **Channel**: Abdul Bari
- **Video**: "Dijkstra Algorithm", "Bellman Ford Algorithm", "Floyd Warshall Algorithm"
- **Ưu điểm**: Giải thích rõ ràng với animation

### 2. YouTube - MIT 6.006
- **Channel**: MIT OpenCourseWare
- **Playlist**: Introduction to Algorithms
- **Nội dung**: Lectures chính thức từ MIT

### 3. Coursera - Algorithms on Graphs
- **Tác giả**: UC San Diego
- **Nội dung**: Khóa học chuyên sâu về graph algorithms

## Datasets để test

### 1. DIMACS Shortest Path Challenge
- **URL**: http://www.diag.uniroma1.it/challenge9/
- **Nội dung**: Datasets chuẩn cho benchmark shortest path algorithms

### 2. Stanford Large Network Dataset Collection
- **URL**: http://snap.stanford.edu/data/
- **Nội dung**: Real-world networks để test scalability

### 3. OpenStreetMap
- **URL**: https://www.openstreetmap.org/
- **Nội dung**: Dữ liệu đường thực tế để test routing algorithms

## Công cụ visualization

### 1. Graphviz
- **URL**: https://graphviz.org/
- **Mô tả**: Tool vẽ đồ thị chuyên nghiệp

### 2. Cytoscape
- **URL**: https://cytoscape.org/
- **Mô tả**: Platform cho network analysis và visualization

### 3. D3.js
- **URL**: https://d3js.org/
- **Mô tả**: JavaScript library cho interactive data visualization

## Ghi chú

- Tất cả tài liệu được truy cập vào tháng 12/2024
- Ưu tiên sử dụng tài liệu từ các nguồn uy tín và có peer-review
- Kết hợp giữa lý thuyết và thực hành để hiểu sâu về algorithms
- Thường xuyên cập nhật kiến thức từ các nghiên cứu mới nhất
