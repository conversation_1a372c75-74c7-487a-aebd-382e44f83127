"""
Cài đặt gi<PERSON><PERSON> thuật Bellman-Ford tìm đường đi ngắn nhất
Author: Student
Date: 2025
"""

import sys
from typing import List, Tuple

class Edge:
    """Lớp biểu diễn cạnh trong đồ thị"""
    def __init__(self, src: int, dest: int, weight: int):
        self.src = src
        self.dest = dest
        self.weight = weight

class Graph:
    """Lớp biểu diễn đồ thị có trọng số cho Bellman-Ford"""
    
    def __init__(self, vertices: int):
        """
        Khởi tạo đồ thị
        Args:
            vertices: Số lượng đỉnh
        """
        self.V = vertices
        self.edges = []
    
    def add_edge(self, u: int, v: int, weight: int):
        """
        Thêm cạnh vào đồ thị
        Args:
            u: Đỉnh nguồn
            v: Đỉnh đích
            weight: Trọng số của cạnh
        """
        self.edges.append(Edge(u, v, weight))
    
    def bellman_ford(self, src: int) -> <PERSON><PERSON>[List[int], List[int], bool]:
        """
        <PERSON><PERSON><PERSON><PERSON> thuật Bellman-Ford tìm đường đi ngắn nhất từ đỉnh nguồn
        Args:
            src: Đỉnh nguồn
        Returns:
            Tuple chứa mảng khoảng cách, mảng đỉnh cha, và có chu trình âm hay không
        """
        # Bước 1: Khởi tạo khoảng cách
        dist = [sys.maxsize] * self.V
        dist[src] = 0
        parent = [-1] * self.V
        
        # Bước 2: Thực hiện V-1 lần lặp để tìm đường đi ngắn nhất
        for i in range(self.V - 1):
            # Duyệt qua tất cả các cạnh
            for edge in self.edges:
                u = edge.src
                v = edge.dest
                weight = edge.weight
                
                # Nếu tìm được đường đi ngắn hơn qua cạnh (u, v)
                if dist[u] != sys.maxsize and dist[u] + weight < dist[v]:
                    dist[v] = dist[u] + weight
                    parent[v] = u
        
        # Bước 3: Kiểm tra chu trình âm
        has_negative_cycle = False
        for edge in self.edges:
            u = edge.src
            v = edge.dest
            weight = edge.weight
            
            if dist[u] != sys.maxsize and dist[u] + weight < dist[v]:
                has_negative_cycle = True
                break
        
        return dist, parent, has_negative_cycle
    
    def print_solution(self, dist: List[int], parent: List[int], src: int, has_negative_cycle: bool):
        """
        In kết quả đường đi ngắn nhất
        Args:
            dist: Mảng khoảng cách
            parent: Mảng đỉnh cha
            src: Đỉnh nguồn
            has_negative_cycle: Có chu trình âm hay không
        """
        if has_negative_cycle:
            print("Đồ thị chứa chu trình âm! Không thể tìm đường đi ngắn nhất.")
            return
        
        print(f"Khoảng cách ngắn nhất từ đỉnh {src} (Bellman-Ford):")
        print("Đỉnh\tKhoảng cách\tĐường đi")
        
        for i in range(self.V):
            if dist[i] == sys.maxsize:
                print(f"{i}\t∞\t\tKhông có đường đi")
            else:
                path = self.get_path(parent, i)
                print(f"{i}\t{dist[i]}\t\t{' -> '.join(map(str, path))}")
    
    def get_path(self, parent: List[int], target: int) -> List[int]:
        """
        Truy vết đường đi từ nguồn đến đích
        Args:
            parent: Mảng đỉnh cha
            target: Đỉnh đích
        Returns:
            Danh sách các đỉnh trong đường đi
        """
        path = []
        current = target
        
        while current != -1:
            path.append(current)
            current = parent[current]
        
        path.reverse()
        return path

def create_example_graph() -> Graph:
    """Tạo đồ thị ví dụ để minh họa"""
    # Tạo đồ thị có 5 đỉnh
    g = Graph(5)
    
    # Thêm các cạnh (u, v, trọng số) - bao gồm trọng số âm
    g.add_edge(0, 1, -1)
    g.add_edge(0, 2, 4)
    g.add_edge(1, 2, 3)
    g.add_edge(1, 3, 2)
    g.add_edge(1, 4, 2)
    g.add_edge(3, 2, 5)
    g.add_edge(3, 1, 1)
    g.add_edge(4, 3, -3)
    
    return g

def create_negative_cycle_graph() -> Graph:
    """Tạo đồ thị có chu trình âm để minh họa"""
    g = Graph(3)
    
    # Tạo chu trình âm: 0 -> 1 -> 2 -> 0 với tổng trọng số âm
    g.add_edge(0, 1, 1)
    g.add_edge(1, 2, -3)
    g.add_edge(2, 0, 1)
    
    return g

def main():
    """Hàm chính để chạy chương trình"""
    print("=== GIẢI THUẬT BELLMAN-FORD - TÌM ĐƯỜNG ĐI NGẮN NHẤT ===\n")
    
    # Test 1: Đồ thị có trọng số âm nhưng không có chu trình âm
    print("TEST 1: Đồ thị có trọng số âm (không có chu trình âm)")
    graph1 = create_example_graph()
    
    print("Các cạnh: (0,1,-1), (0,2,4), (1,2,3), (1,3,2), (1,4,2), (3,2,5), (3,1,1), (4,3,-3)")
    print()
    
    source = 0
    distances, parents, has_neg_cycle = graph1.bellman_ford(source)
    graph1.print_solution(distances, parents, source, has_neg_cycle)
    
    print("\n" + "="*60 + "\n")
    
    # Test 2: Đồ thị có chu trình âm
    print("TEST 2: Đồ thị có chu trình âm")
    graph2 = create_negative_cycle_graph()
    
    print("Các cạnh: (0,1,1), (1,2,-3), (2,0,1)")
    print("Chu trình: 0 -> 1 -> 2 -> 0 có tổng trọng số = 1 + (-3) + 1 = -1 < 0")
    print()
    
    distances2, parents2, has_neg_cycle2 = graph2.bellman_ford(0)
    graph2.print_solution(distances2, parents2, 0, has_neg_cycle2)
    
    print("\n=== PHÂN TÍCH KẾT QUẢ ===")
    print("- Giải thuật Bellman-Ford có thể xử lý đồ thị có trọng số âm")
    print("- Có thể phát hiện chu trình âm trong đồ thị")
    print("- Độ phức tạp thời gian: O(VE)")
    print("- Độ phức tạp không gian: O(V)")
    print("- Chậm hơn Dijkstra nhưng linh hoạt hơn")

if __name__ == "__main__":
    main()
