"""
<PERSON><PERSON><PERSON><PERSON> tr<PERSON>nh ch<PERSON>h để chạy và so s<PERSON>h các giải thuật tìm đường đi ngắn nhất
Author: Student
Date: 2025
"""

import time
from dijkstra import Graph as DijkstraGraph
from bellman_ford import Graph as BellmanFordGraph
from floyd_warshall import Graph as FloydWarshallGraph

def create_test_graph_dijkstra():
    """Tạo đồ thị test cho Dijkstra"""
    g = DijkstraGraph(6)
    g.add_edge(0, 1, 4)
    g.add_edge(0, 2, 2)
    g.add_edge(1, 2, 1)
    g.add_edge(1, 3, 5)
    g.add_edge(2, 3, 8)
    g.add_edge(2, 4, 10)
    g.add_edge(3, 4, 2)
    g.add_edge(3, 5, 6)
    g.add_edge(4, 5, 3)
    return g

def create_test_graph_bellman_ford():
    """Tạo đồ thị test cho Bellman-Ford"""
    g = BellmanFordGraph(5)
    g.add_edge(0, 1, -1)
    g.add_edge(0, 2, 4)
    g.add_edge(1, 2, 3)
    g.add_edge(1, 3, 2)
    g.add_edge(1, 4, 2)
    g.add_edge(3, 2, 5)
    g.add_edge(3, 1, 1)
    g.add_edge(4, 3, -3)
    return g

def create_test_graph_floyd_warshall():
    """Tạo đồ thị test cho Floyd-Warshall"""
    g = FloydWarshallGraph(4)
    g.add_edge(0, 1, 5)
    g.add_edge(0, 3, 10)
    g.add_edge(1, 2, 3)
    g.add_edge(2, 3, 1)
    return g

def run_dijkstra_demo():
    """Chạy demo giải thuật Dijkstra"""
    print("=" * 70)
    print("DEMO GIẢI THUẬT DIJKSTRA")
    print("=" * 70)
    
    graph = create_test_graph_dijkstra()
    source = 0
    
    print(f"Đồ thị có {graph.V} đỉnh")
    print("Các cạnh: (0,1,4), (0,2,2), (1,2,1), (1,3,5), (2,3,8), (2,4,10), (3,4,2), (3,5,6), (4,5,3)")
    print(f"Tìm đường đi ngắn nhất từ đỉnh {source}")
    print()
    
    start_time = time.time()
    distances, parents = graph.dijkstra(source)
    end_time = time.time()
    
    graph.print_solution(distances, parents, source)
    print(f"\nThời gian thực thi: {(end_time - start_time) * 1000:.4f} ms")

def run_bellman_ford_demo():
    """Chạy demo giải thuật Bellman-Ford"""
    print("\n" + "=" * 70)
    print("DEMO GIẢI THUẬT BELLMAN-FORD")
    print("=" * 70)
    
    graph = create_test_graph_bellman_ford()
    source = 0
    
    print(f"Đồ thị có {graph.V} đỉnh")
    print("Các cạnh: (0,1,-1), (0,2,4), (1,2,3), (1,3,2), (1,4,2), (3,2,5), (3,1,1), (4,3,-3)")
    print(f"Tìm đường đi ngắn nhất từ đỉnh {source}")
    print("Lưu ý: Đồ thị có trọng số âm")
    print()
    
    start_time = time.time()
    distances, parents, has_negative_cycle = graph.bellman_ford(source)
    end_time = time.time()
    
    graph.print_solution(distances, parents, source, has_negative_cycle)
    print(f"\nThời gian thực thi: {(end_time - start_time) * 1000:.4f} ms")

def run_floyd_warshall_demo():
    """Chạy demo giải thuật Floyd-Warshall"""
    print("\n" + "=" * 70)
    print("DEMO GIẢI THUẬT FLOYD-WARSHALL")
    print("=" * 70)
    
    graph = create_test_graph_floyd_warshall()
    
    print(f"Đồ thị có {graph.V} đỉnh")
    print("Các cạnh: (0,1,5), (0,3,10), (1,2,3), (2,3,1)")
    print("Tìm đường đi ngắn nhất giữa TẤT CẢ các cặp đỉnh")
    print()
    
    start_time = time.time()
    result = graph.floyd_warshall()
    end_time = time.time()
    
    graph.print_solution(result)
    
    if graph.has_negative_cycle(result):
        print("\nCảnh báo: Đồ thị chứa chu trình âm!")
    else:
        print("\nĐồ thị không chứa chu trình âm.")
    
    print(f"\nThời gian thực thi: {(end_time - start_time) * 1000:.4f} ms")

def print_comparison_table():
    """In bảng so sánh các giải thuật"""
    print("\n" + "=" * 70)
    print("BẢNG SO SÁNH CÁC GIẢI THUẬT")
    print("=" * 70)
    
    print(f"{'Giải thuật':<15} {'Độ phức tạp':<15} {'Trọng số âm':<12} {'Chu trình âm':<12} {'Ứng dụng'}")
    print("-" * 70)
    print(f"{'Dijkstra':<15} {'O((V+E)logV)':<15} {'Không':<12} {'Không':<12} {'SSSP, trọng số dương'}")
    print(f"{'Bellman-Ford':<15} {'O(VE)':<15} {'Có':<12} {'Phát hiện':<12} {'SSSP, có trọng số âm'}")
    print(f"{'Floyd-Warshall':<15} {'O(V³)':<15} {'Có':<12} {'Phát hiện':<12} {'APSP'}")
    
    print("\nGhi chú:")
    print("- SSSP: Single-Source Shortest Path (đường đi ngắn nhất từ một nguồn)")
    print("- APSP: All-Pairs Shortest Path (đường đi ngắn nhất giữa tất cả các cặp)")
    print("- V: số đỉnh, E: số cạnh")

def main():
    """Hàm chính"""
    print("CHƯƠNG TRÌNH MINH HỌA CÁC GIẢI THUẬT TÌM ĐƯỜNG ĐI NGẮN NHẤT")
    print("Sinh viên thực hiện: [Tên sinh viên]")
    print("Lớp: [Tên lớp]")
    print("Môn học: Cấu trúc dữ liệu và giải thuật")
    
    # Chạy các demo
    run_dijkstra_demo()
    run_bellman_ford_demo()
    run_floyd_warshall_demo()
    
    # In bảng so sánh
    print_comparison_table()
    
    print("\n" + "=" * 70)
    print("KẾT LUẬN")
    print("=" * 70)
    print("1. Dijkstra: Nhanh nhất cho đồ thị có trọng số không âm")
    print("2. Bellman-Ford: Xử lý được trọng số âm, phát hiện chu trình âm")
    print("3. Floyd-Warshall: Tìm đường đi ngắn nhất giữa tất cả các cặp đỉnh")
    print("4. Lựa chọn giải thuật phụ thuộc vào đặc điểm của bài toán cụ thể")
    
    print("\nCảm ơn bạn đã sử dụng chương trình!")

if __name__ == "__main__":
    main()
