// Category Page JavaScript

// Global variables
let currentCategory = '';
let filteredProducts = [];
let currentPage = 1;
let productsPerPage = 12;
let currentView = 'grid';
let currentSort = 'default';
let activeFilters = {
    priceRange: '',
    brands: [],
    conditions: []
};

// Category information
const categoryInfo = {
    'laptop': {
        title: 'Laptop Cũ',
        description: 'Bộ sưu tập laptop cũ chất lượng cao cho công việc và giải trí',
        icon: 'fas fa-laptop'
    },
    'dien-thoai': {
        title: 'Điện Thoại Cũ',
        description: 'Đi<PERSON>n thoại cũ từ các thương hiệu nổi tiếng với giá tốt nhất',
        icon: 'fas fa-mobile-alt'
    },
    'tablet': {
        title: 'Tablet & iPad',
        description: 'Tablet và iPad cũ cho học tập và giải trí',
        icon: 'fas fa-tablet-alt'
    },
    'phu-kien': {
        title: '<PERSON><PERSON>',
        description: '<PERSON><PERSON> kiện điện tử: tai nghe, s<PERSON><PERSON>, ốp lưng và nhiều hơn nữa',
        icon: 'fas fa-headphones'
    },
    'linh-kien': {
        title: 'Linh Kiện',
        description: 'Linh kiện máy tính và điện thoại chính hãng',
        icon: 'fas fa-microchip'
    }
};

// Extended sample products for category
const extendedProducts = [
    ...sampleProducts,
    {
        id: 7,
        name: "HP Pavilion 15",
        category: "laptop",
        brand: "HP",
        price: 9800000,
        originalPrice: 12000000,
        condition: 87,
        image: "https://images.unsplash.com/photo-1588872657578-7efd1f1555ed?w=400&h=300&fit=crop",
        description: "Core i3-10110U, 4GB RAM, HDD 1TB",
        rating: 4.2,
        reviews: 15,
        inStock: true
    },
    {
        id: 8,
        name: "Xiaomi Redmi Note 10",
        category: "dien-thoai",
        brand: "Xiaomi",
        price: 3800000,
        originalPrice: 5000000,
        condition: 92,
        image: "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop",
        description: "64GB, Xanh dương, Fullbox",
        rating: 4.3,
        reviews: 32,
        inStock: true
    },
    {
        id: 9,
        name: "Samsung Galaxy Tab A7",
        category: "tablet",
        brand: "Samsung",
        price: 4200000,
        originalPrice: 6000000,
        condition: 89,
        image: "https://images.unsplash.com/photo-1561154464-82e9adf32764?w=400&h=300&fit=crop",
        description: "32GB WiFi, Màn hình 10.4 inch",
        rating: 4.1,
        reviews: 21,
        inStock: true
    },
    {
        id: 10,
        name: "Sony WH-1000XM4",
        category: "phu-kien",
        brand: "Sony",
        price: 6500000,
        originalPrice: 8500000,
        condition: 94,
        image: "https://images.unsplash.com/photo-1546435770-a3e426bf472b?w=400&h=300&fit=crop",
        description: "Chống ồn chủ động, Bluetooth 5.0",
        rating: 4.7,
        reviews: 28,
        inStock: true
    }
];

// Initialize category page
document.addEventListener('DOMContentLoaded', function() {
    initializeCategoryPage();
});

function initializeCategoryPage() {
    // Get category from URL
    const urlParams = new URLSearchParams(window.location.search);
    currentCategory = urlParams.get('cat') || '';
    
    // Initialize AOS
    AOS.init({
        duration: 1000,
        once: true
    });
    
    // Load category info
    loadCategoryInfo();
    
    // Load products
    loadProducts();
    
    // Initialize filters
    initializeFilters();
    
    // Initialize event listeners
    initializeEventListeners();
    
    console.log('Category page initialized for:', currentCategory);
}

function loadCategoryInfo() {
    const categoryTitle = document.getElementById('categoryTitle');
    const categoryDescription = document.getElementById('categoryDescription');
    const categoryBreadcrumb = document.getElementById('categoryBreadcrumb');
    
    if (currentCategory && categoryInfo[currentCategory]) {
        const info = categoryInfo[currentCategory];
        categoryTitle.innerHTML = `<i class="${info.icon} me-2"></i>${info.title}`;
        categoryDescription.textContent = info.description;
        categoryBreadcrumb.textContent = info.title;
        
        // Update page title
        document.title = `${info.title} - Electronics Shop`;
    } else {
        categoryTitle.innerHTML = '<i class="fas fa-th-large me-2"></i>Tất cả sản phẩm';
        categoryDescription.textContent = 'Khám phá bộ sưu tập thiết bị điện tử cũ chất lượng cao';
        categoryBreadcrumb.textContent = 'Tất cả sản phẩm';
    }
}

function loadProducts() {
    showLoading();
    
    // Filter products by category
    let products = extendedProducts;
    if (currentCategory) {
        products = products.filter(product => product.category === currentCategory);
    }
    
    // Apply filters
    filteredProducts = applyFiltersToProducts(products);
    
    // Sort products
    sortProducts();
    
    // Update UI
    updateProductCount();
    renderProducts();
    renderPagination();
    
    hideLoading();
}

function applyFiltersToProducts(products) {
    let filtered = [...products];
    
    // Price range filter
    if (activeFilters.priceRange) {
        const [min, max] = activeFilters.priceRange.split('-').map(Number);
        filtered = filtered.filter(product => 
            product.price >= min && product.price <= max
        );
    }
    
    // Brand filter
    if (activeFilters.brands.length > 0) {
        filtered = filtered.filter(product => 
            activeFilters.brands.includes(product.brand)
        );
    }
    
    // Condition filter
    if (activeFilters.conditions.length > 0) {
        filtered = filtered.filter(product => {
            return activeFilters.conditions.some(range => {
                const [min, max] = range.split('-').map(Number);
                return product.condition >= min && product.condition <= max;
            });
        });
    }
    
    return filtered;
}

function sortProducts() {
    switch (currentSort) {
        case 'price-asc':
            filteredProducts.sort((a, b) => a.price - b.price);
            break;
        case 'price-desc':
            filteredProducts.sort((a, b) => b.price - a.price);
            break;
        case 'name-asc':
            filteredProducts.sort((a, b) => a.name.localeCompare(b.name));
            break;
        case 'name-desc':
            filteredProducts.sort((a, b) => b.name.localeCompare(a.name));
            break;
        case 'condition-desc':
            filteredProducts.sort((a, b) => b.condition - a.condition);
            break;
        case 'rating-desc':
            filteredProducts.sort((a, b) => b.rating - a.rating);
            break;
        default:
            // Default sorting (featured products first)
            break;
    }
}

function renderProducts() {
    const container = document.getElementById('productsGrid');
    const startIndex = (currentPage - 1) * productsPerPage;
    const endIndex = startIndex + productsPerPage;
    const productsToShow = filteredProducts.slice(startIndex, endIndex);
    
    if (productsToShow.length === 0) {
        container.innerHTML = `
            <div class="col-12">
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h3>Không tìm thấy sản phẩm</h3>
                    <p>Không có sản phẩm nào phù hợp với bộ lọc của bạn</p>
                    <button class="btn btn-primary" onclick="clearFilters()">
                        <i class="fas fa-times me-2"></i>Xóa bộ lọc
                    </button>
                </div>
            </div>
        `;
        return;
    }
    
    if (currentView === 'grid') {
        container.className = 'row products-grid';
        container.innerHTML = productsToShow.map(product => 
            createProductCard(product)
        ).join('');
    } else {
        container.className = 'products-list';
        container.innerHTML = productsToShow.map(product => 
            createProductListItem(product)
        ).join('');
    }
    
    // Update results info
    updateResultsInfo(startIndex, endIndex);
}

function createProductListItem(product) {
    const discountPercent = Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);
    const conditionClass = getConditionClass(product.condition);
    const conditionText = getConditionText(product.condition);
    
    return `
        <div class="card product-card" data-aos="fade-up">
            <div class="row g-0">
                <div class="col-md-3">
                    <div class="position-relative">
                        <img src="${product.image}" class="product-image" alt="${product.name}">
                        <span class="badge condition-badge ${conditionClass}">${conditionText}</span>
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h5 class="card-title">
                                    <a href="product.html?id=${product.id}" class="text-decoration-none">${product.name}</a>
                                </h5>
                                <p class="card-text text-muted">${product.description}</p>
                                
                                <div class="product-rating mb-2">
                                    ${generateStarRating(product.rating)}
                                    <span class="text-muted small">(${product.reviews})</span>
                                </div>
                                
                                <div class="product-price mb-3">
                                    <span class="price">${formatCurrency(product.price)}</span>
                                    ${product.originalPrice > product.price ? `
                                        <span class="original-price ms-2">${formatCurrency(product.originalPrice)}</span>
                                        <span class="discount-badge ms-2">-${discountPercent}%</span>
                                    ` : ''}
                                </div>
                            </div>
                            
                            <div class="product-actions">
                                <button class="btn btn-primary btn-sm" onclick="addToCart(${product.id})">
                                    <i class="fas fa-cart-plus me-1"></i>Thêm vào giỏ
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="toggleWishlist(${product.id})">
                                    <i class="fas fa-heart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderPagination() {
    const totalPages = Math.ceil(filteredProducts.length / productsPerPage);
    const pagination = document.getElementById('pagination');
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // Previous button
    paginationHTML += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;
    
    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    if (startPage > 1) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(1)">1</a></li>`;
        if (startPage > 2) {
            paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages})">${totalPages}</a></li>`;
    }
    
    // Next button
    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;
    
    pagination.innerHTML = paginationHTML;
}

function updateProductCount() {
    const productCount = document.getElementById('productCount');
    productCount.textContent = `${filteredProducts.length} sản phẩm`;
}

function updateResultsInfo(startIndex, endIndex) {
    const resultsInfo = document.getElementById('resultsInfo');
    const total = filteredProducts.length;
    const showing = Math.min(endIndex, total);
    
    resultsInfo.textContent = `Hiển thị ${startIndex + 1}-${showing} trong tổng số ${total} sản phẩm`;
}

function initializeFilters() {
    // Price range filters
    const priceFilters = document.querySelectorAll('input[name="priceRange"]');
    priceFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            activeFilters.priceRange = this.value;
            applyFilters();
        });
    });
    
    // Brand filters
    const brandFilters = document.querySelectorAll('.brand-filter input[type="checkbox"]');
    brandFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            if (this.checked) {
                activeFilters.brands.push(this.value);
            } else {
                activeFilters.brands = activeFilters.brands.filter(brand => brand !== this.value);
            }
            applyFilters();
        });
    });
    
    // Condition filters
    const conditionFilters = document.querySelectorAll('.condition-filter input[type="checkbox"]');
    conditionFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            if (this.checked) {
                activeFilters.conditions.push(this.value);
            } else {
                activeFilters.conditions = activeFilters.conditions.filter(condition => condition !== this.value);
            }
            applyFilters();
        });
    });
}

function initializeEventListeners() {
    // Sort select
    const sortSelect = document.getElementById('sortSelect');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            currentSort = this.value;
            loadProducts();
        });
    }
}

// Public functions
function applyFilters() {
    currentPage = 1;
    loadProducts();
}

function clearFilters() {
    // Reset active filters
    activeFilters = {
        priceRange: '',
        brands: [],
        conditions: []
    };
    
    // Reset form controls
    document.querySelectorAll('.filter-group input').forEach(input => {
        if (input.type === 'radio') {
            input.checked = input.value === '';
        } else {
            input.checked = false;
        }
    });
    
    // Reset sort
    currentSort = 'default';
    document.getElementById('sortSelect').value = 'default';
    
    // Reload products
    loadProducts();
    
    showNotification('Đã xóa tất cả bộ lọc', 'info');
}

function changeView(view) {
    currentView = view;
    
    // Update view buttons
    document.querySelectorAll('.view-options .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');
    
    // Re-render products
    renderProducts();
}

function changePage(page) {
    if (page < 1 || page > Math.ceil(filteredProducts.length / productsPerPage)) {
        return;
    }
    
    currentPage = page;
    renderProducts();
    renderPagination();
    
    // Scroll to top of products
    document.querySelector('.products-section').scrollIntoView({
        behavior: 'smooth'
    });
}

// Export functions for global access
window.applyFilters = applyFilters;
window.clearFilters = clearFilters;
window.changeView = changeView;
window.changePage = changePage;
window.sortProducts = function() {
    const sortSelect = document.getElementById('sortSelect');
    currentSort = sortSelect.value;
    loadProducts();
};
