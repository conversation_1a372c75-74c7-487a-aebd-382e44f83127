// Search Page JavaScript

// Global variables
let searchQuery = '';
let searchResults = [];
let filteredSearchResults = [];
let currentSearchPage = 1;
let searchResultsPerPage = 12;
let currentSearchView = 'grid';
let currentSearchSort = 'relevance';
let searchFilters = {
    categories: [],
    priceRange: '',
    brands: []
};

// Initialize search page
document.addEventListener('DOMContentLoaded', function() {
    initializeSearchPage();
});

function initializeSearchPage() {
    // Initialize AOS
    AOS.init({
        duration: 1000,
        once: true
    });
    
    // Get search query from URL
    const urlParams = new URLSearchParams(window.location.search);
    searchQuery = urlParams.get('q') || '';
    
    // Update search input
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = searchQuery;
    }
    
    // Perform search
    if (searchQuery) {
        performSearchQuery(searchQuery);
    } else {
        showNoSearchQuery();
    }
    
    // Initialize filters
    initializeSearchFilters();
    
    console.log('Search page initialized for query:', searchQuery);
}

function performSearchQuery(query) {
    showLoading();
    
    // Update page title and header
    updateSearchHeader(query);
    
    // Simulate search API call
    setTimeout(() => {
        searchResults = searchProducts(query);
        filteredSearchResults = [...searchResults];
        
        // Apply any existing filters
        applySearchFilters();
        
        hideLoading();
    }, 800);
}

function searchProducts(query) {
    const allProducts = [
        ...sampleProducts,
        // Add more products from category.js if available
        ...(window.extendedProducts || [])
    ];
    
    if (!query) return [];
    
    const searchTerms = query.toLowerCase().split(' ');
    
    return allProducts.filter(product => {
        const searchableText = [
            product.name,
            product.brand,
            product.description,
            product.category
        ].join(' ').toLowerCase();
        
        // Check if all search terms are found
        return searchTerms.every(term => searchableText.includes(term));
    }).map(product => {
        // Calculate relevance score
        const nameMatches = searchTerms.filter(term => 
            product.name.toLowerCase().includes(term)
        ).length;
        const brandMatches = searchTerms.filter(term => 
            product.brand.toLowerCase().includes(term)
        ).length;
        
        return {
            ...product,
            relevanceScore: (nameMatches * 3) + (brandMatches * 2) + 1
        };
    }).sort((a, b) => b.relevanceScore - a.relevanceScore);
}

function updateSearchHeader(query) {
    const searchTitle = document.getElementById('searchTitle');
    const searchDescription = document.getElementById('searchDescription');
    
    searchTitle.innerHTML = `Kết quả cho "<span class="text-warning">${query}</span>"`;
    searchDescription.textContent = `Tìm thấy các sản phẩm liên quan đến "${query}"`;
    
    // Update page title
    document.title = `Tìm kiếm "${query}" - Electronics Shop`;
}

function showNoSearchQuery() {
    const container = document.getElementById('searchResultsGrid');
    container.innerHTML = `
        <div class="col-12">
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h3>Nhập từ khóa tìm kiếm</h3>
                <p>Vui lòng nhập từ khóa để tìm kiếm sản phẩm</p>
                <div class="search-suggestions">
                    <h6>Gợi ý tìm kiếm:</h6>
                    <div class="d-flex flex-wrap gap-2 justify-content-center">
                        <button class="btn btn-outline-primary btn-sm" onclick="searchSuggestion('iPhone')">iPhone</button>
                        <button class="btn btn-outline-primary btn-sm" onclick="searchSuggestion('Samsung')">Samsung</button>
                        <button class="btn btn-outline-primary btn-sm" onclick="searchSuggestion('Laptop Dell')">Laptop Dell</button>
                        <button class="btn btn-outline-primary btn-sm" onclick="searchSuggestion('iPad')">iPad</button>
                        <button class="btn btn-outline-primary btn-sm" onclick="searchSuggestion('AirPods')">AirPods</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    updateSearchResultCount(0);
}

function renderSearchResults() {
    const container = document.getElementById('searchResultsGrid');
    const startIndex = (currentSearchPage - 1) * searchResultsPerPage;
    const endIndex = startIndex + searchResultsPerPage;
    const resultsToShow = filteredSearchResults.slice(startIndex, endIndex);
    
    if (resultsToShow.length === 0) {
        container.innerHTML = `
            <div class="col-12">
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h3>Không tìm thấy kết quả</h3>
                    <p>Không có sản phẩm nào phù hợp với từ khóa "<strong>${searchQuery}</strong>"</p>
                    <div class="search-suggestions">
                        <h6>Gợi ý:</h6>
                        <ul class="list-unstyled">
                            <li>• Kiểm tra lại chính tả</li>
                            <li>• Thử sử dụng từ khóa khác</li>
                            <li>• Sử dụng từ khóa tổng quát hơn</li>
                            <li>• Xóa bộ lọc để mở rộng kết quả</li>
                        </ul>
                        <button class="btn btn-primary" onclick="clearSearchFilters()">
                            <i class="fas fa-times me-2"></i>Xóa bộ lọc
                        </button>
                    </div>
                </div>
            </div>
        `;
        return;
    }
    
    if (currentSearchView === 'grid') {
        container.className = 'row products-grid';
        container.innerHTML = resultsToShow.map(product => 
            createProductCard(product)
        ).join('');
    } else {
        container.className = 'products-list';
        container.innerHTML = resultsToShow.map(product => 
            createSearchResultListItem(product)
        ).join('');
    }
    
    // Update results info
    updateSearchResultsInfo(startIndex, endIndex);
    
    // Render pagination
    renderSearchPagination();
}

function createSearchResultListItem(product) {
    const discountPercent = Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);
    const conditionClass = getConditionClass(product.condition);
    const conditionText = getConditionText(product.condition);
    
    // Highlight search terms in product name
    const highlightedName = highlightSearchTerms(product.name, searchQuery);
    
    return `
        <div class="card product-card mb-3" data-aos="fade-up">
            <div class="row g-0">
                <div class="col-md-3">
                    <div class="position-relative">
                        <img src="${product.image}" class="product-image" alt="${product.name}">
                        <span class="badge condition-badge ${conditionClass}">${conditionText}</span>
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h5 class="card-title">
                                    <a href="product.html?id=${product.id}" class="text-decoration-none">${highlightedName}</a>
                                </h5>
                                <p class="card-text text-muted">${product.description}</p>
                                
                                <div class="product-meta mb-2">
                                    <span class="badge bg-secondary me-2">${product.brand}</span>
                                    <span class="badge bg-info">${getCategoryName(product.category)}</span>
                                </div>
                                
                                <div class="product-rating mb-2">
                                    ${generateStarRating(product.rating)}
                                    <span class="text-muted small">(${product.reviews} đánh giá)</span>
                                </div>
                                
                                <div class="product-price mb-3">
                                    <span class="price">${formatCurrency(product.price)}</span>
                                    ${product.originalPrice > product.price ? `
                                        <span class="original-price ms-2">${formatCurrency(product.originalPrice)}</span>
                                        <span class="discount-badge ms-2">-${discountPercent}%</span>
                                    ` : ''}
                                </div>
                            </div>
                            
                            <div class="product-actions">
                                <button class="btn btn-primary btn-sm mb-2" onclick="addToCart(${product.id})">
                                    <i class="fas fa-cart-plus me-1"></i>Thêm vào giỏ
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="toggleWishlist(${product.id})">
                                    <i class="fas fa-heart me-1"></i>Yêu thích
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function highlightSearchTerms(text, query) {
    if (!query) return text;
    
    const searchTerms = query.toLowerCase().split(' ');
    let highlightedText = text;
    
    searchTerms.forEach(term => {
        const regex = new RegExp(`(${term})`, 'gi');
        highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
    });
    
    return highlightedText;
}

function getCategoryName(category) {
    const categoryNames = {
        'laptop': 'Laptop',
        'dien-thoai': 'Điện thoại',
        'tablet': 'Tablet',
        'phu-kien': 'Phụ kiện',
        'linh-kien': 'Linh kiện'
    };
    return categoryNames[category] || 'Khác';
}

function initializeSearchFilters() {
    // Category filters
    const categoryFilters = document.querySelectorAll('.category-filter input[type="checkbox"]');
    categoryFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            if (this.checked) {
                searchFilters.categories.push(this.value);
            } else {
                searchFilters.categories = searchFilters.categories.filter(cat => cat !== this.value);
            }
        });
    });
    
    // Price range filters
    const priceFilters = document.querySelectorAll('input[name="priceRange"]');
    priceFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            searchFilters.priceRange = this.value;
        });
    });
    
    // Brand filters
    const brandFilters = document.querySelectorAll('.brand-filter input[type="checkbox"]');
    brandFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            if (this.checked) {
                searchFilters.brands.push(this.value);
            } else {
                searchFilters.brands = searchFilters.brands.filter(brand => brand !== this.value);
            }
        });
    });
}

function applySearchFilters() {
    filteredSearchResults = searchResults.filter(product => {
        // Category filter
        if (searchFilters.categories.length > 0) {
            if (!searchFilters.categories.includes(product.category)) {
                return false;
            }
        }
        
        // Price range filter
        if (searchFilters.priceRange) {
            const [min, max] = searchFilters.priceRange.split('-').map(Number);
            if (product.price < min || product.price > max) {
                return false;
            }
        }
        
        // Brand filter
        if (searchFilters.brands.length > 0) {
            if (!searchFilters.brands.includes(product.brand)) {
                return false;
            }
        }
        
        return true;
    });
    
    // Sort results
    sortSearchResults();
    
    // Reset to first page
    currentSearchPage = 1;
    
    // Update display
    updateSearchResultCount(filteredSearchResults.length);
    renderSearchResults();
}

function clearSearchFilters() {
    // Reset filters
    searchFilters = {
        categories: [],
        priceRange: '',
        brands: []
    };
    
    // Reset form controls
    document.querySelectorAll('.filter-group input').forEach(input => {
        if (input.type === 'radio') {
            input.checked = input.value === '';
        } else {
            input.checked = false;
        }
    });
    
    // Reset sort
    currentSearchSort = 'relevance';
    document.getElementById('searchSortSelect').value = 'relevance';
    
    // Reapply filters (which will show all results)
    applySearchFilters();
    
    showNotification('Đã xóa tất cả bộ lọc', 'info');
}

function sortSearchResults() {
    switch (currentSearchSort) {
        case 'price-asc':
            filteredSearchResults.sort((a, b) => a.price - b.price);
            break;
        case 'price-desc':
            filteredSearchResults.sort((a, b) => b.price - a.price);
            break;
        case 'name-asc':
            filteredSearchResults.sort((a, b) => a.name.localeCompare(b.name));
            break;
        case 'rating-desc':
            filteredSearchResults.sort((a, b) => b.rating - a.rating);
            break;
        case 'relevance':
        default:
            filteredSearchResults.sort((a, b) => b.relevanceScore - a.relevanceScore);
            break;
    }
}

function updateSearchResultCount(count) {
    const countElement = document.getElementById('searchResultCount');
    countElement.textContent = `${count} kết quả`;
}

function updateSearchResultsInfo(startIndex, endIndex) {
    const resultsInfo = document.getElementById('searchResultsInfo');
    const total = filteredSearchResults.length;
    const showing = Math.min(endIndex, total);
    
    if (total === 0) {
        resultsInfo.textContent = 'Không tìm thấy kết quả';
    } else {
        resultsInfo.textContent = `Hiển thị ${startIndex + 1}-${showing} trong tổng số ${total} kết quả`;
    }
}

function renderSearchPagination() {
    const totalPages = Math.ceil(filteredSearchResults.length / searchResultsPerPage);
    const pagination = document.getElementById('searchPagination');
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // Previous button
    paginationHTML += `
        <li class="page-item ${currentSearchPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changeSearchPage(${currentSearchPage - 1})">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;
    
    // Page numbers
    const startPage = Math.max(1, currentSearchPage - 2);
    const endPage = Math.min(totalPages, currentSearchPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === currentSearchPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changeSearchPage(${i})">${i}</a>
            </li>
        `;
    }
    
    // Next button
    paginationHTML += `
        <li class="page-item ${currentSearchPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changeSearchPage(${currentSearchPage + 1})">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;
    
    pagination.innerHTML = paginationHTML;
}

// Public functions
function refineSearchResults() {
    const refineInput = document.getElementById('refineSearch');
    const refineQuery = refineInput.value.trim();
    
    if (refineQuery) {
        const newQuery = `${searchQuery} ${refineQuery}`;
        window.location.href = `search.html?q=${encodeURIComponent(newQuery)}`;
    }
}

function searchSuggestion(suggestion) {
    window.location.href = `search.html?q=${encodeURIComponent(suggestion)}`;
}

function changeSearchView(view) {
    currentSearchView = view;
    
    // Update view buttons
    document.querySelectorAll('.view-options .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');
    
    // Re-render results
    renderSearchResults();
}

function changeSearchPage(page) {
    if (page < 1 || page > Math.ceil(filteredSearchResults.length / searchResultsPerPage)) {
        return;
    }
    
    currentSearchPage = page;
    renderSearchResults();
    
    // Scroll to top of results
    document.querySelector('.products-section').scrollIntoView({
        behavior: 'smooth'
    });
}

// Export functions for global access
window.refineSearchResults = refineSearchResults;
window.searchSuggestion = searchSuggestion;
window.applySearchFilters = applySearchFilters;
window.clearSearchFilters = clearSearchFilters;
window.changeSearchView = changeSearchView;
window.changeSearchPage = changeSearchPage;
window.sortSearchResults = function() {
    const sortSelect = document.getElementById('searchSortSelect');
    currentSearchSort = sortSelect.value;
    sortSearchResults();
    renderSearchResults();
};
