@echo off
title Step 1 - XAMPP Installation Helper
color 0B

echo ========================================
echo    STEP 1: XAMPP INSTALLATION
echo ========================================
echo.

echo Checking if XAMPP is already installed...
if exist "C:\xampp\xampp-control.exe" (
    echo [OK] XAMPP is already installed!
    echo Location: C:\xampp\
    echo.
    echo Do you want to start XAMPP Control Panel? (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        start "" "C:\xampp\xampp-control.exe"
        echo XAMPP Control Panel opened.
        echo Please start Apache and MySQL services.
    )
    goto :end
)

echo [INFO] XAMPP not found. Need to install.
echo.

echo ========================================
echo    DOWNLOAD XAMPP
echo ========================================
echo.

echo Please follow these steps:
echo.
echo 1. A browser window will open to XAMPP download page
echo 2. Click "Download" for XAMPP Windows (PHP 8.1 or 8.2)
echo 3. Wait for download to complete
echo 4. Come back here and press any key
echo.

echo Opening XAMPP download page...
start "" "https://www.apachefriends.org/download.html"

echo.
echo Press any key when download is complete...
pause

echo ========================================
echo    INSTALL XAMPP
echo ========================================
echo.

echo Now let's install XAMPP:
echo.
echo 1. Go to your Downloads folder
echo 2. Find the XAMPP installer file (xampp-windows-x64-*.exe)
echo 3. RIGHT-CLICK on the file
echo 4. Select "Run as administrator" (IMPORTANT!)
echo 5. Follow the installation wizard:
echo    - Welcome: Click Next
echo    - Components: Select Apache, MySQL, PHP, phpMyAdmin
echo    - Installation folder: Keep default C:\xampp
echo    - Bitnami: Uncheck the box
echo    - Click Install and wait
echo.

echo Press any key when installation is complete...
pause

echo ========================================
echo    START XAMPP SERVICES
echo ========================================
echo.

echo Starting XAMPP Control Panel...
if exist "C:\xampp\xampp-control.exe" (
    start "" "C:\xampp\xampp-control.exe"
    echo.
    echo IMPORTANT: In XAMPP Control Panel:
    echo 1. Click START next to Apache (should turn GREEN)
    echo 2. Click START next to MySQL (should turn GREEN)
    echo 3. If you see errors, click Config to change ports
    echo.
    echo Press any key when both services are running...
    pause
) else (
    echo [ERROR] XAMPP Control Panel not found!
    echo Please make sure XAMPP is installed correctly.
    goto :end
)

echo ========================================
echo    TEST XAMPP
echo ========================================
echo.

echo Testing XAMPP installation...
echo Opening http://localhost in browser...
start "" "http://localhost"

echo.
echo You should see the XAMPP welcome page.
echo If you see it, XAMPP is working correctly!
echo.
echo Press any key if you can see the XAMPP page...
pause

echo ========================================
echo    STEP 1 COMPLETE!
echo ========================================
echo.
echo [SUCCESS] XAMPP is installed and running!
echo.
echo What's working now:
echo - Web server (Apache) is running
echo - Database server (MySQL) is running  
echo - You can access http://localhost
echo.
echo Next step: Create database for the website
echo.
echo Press any key to continue to Step 2...
pause

:end
echo.
echo Step 1 completed. You can now proceed to Step 2.
pause
