# 🐍 Python GUI Demo - Ứng Dụng Quản Lý Học Sinh

## 📋 Mô tả

Đây là phiên bản **Python GUI** của ứng dụng "Quản Lý Học Sinh sử dụng Danh Sách Liên Kết Đơn". Ứng dụng được xây dựng bằng **Python** và **Tkinter**, cung cấp giao diện đồ họa trực quan và dễ sử dụng.

## 👨‍🎓 Thông tin sinh viên thực hiện

- **Họ tên**: Hu<PERSON>nh Thái Bảo
- **Ngày sinh**: 28/04/1994
- **Email**: <EMAIL>
- **Điện thoại**: 0355771075
- **Tài khoản**: baoht280494
- **Lớp**: DX23TT11
- **Mã sinh viên**: 170123488

## 🚀 Tính năng chính

### 🎨 **Giao diện đồ họa hiện đại**
- 🖼️ **Layout 3 panel**: Form nhập liệu, đi<PERSON><PERSON> khiển, visualization
- 🎨 **Màu sắc**: Thiết kế với màu sắc chuyên nghiệp
- 📱 **Responsive**: Tự động điều chỉnh kích thước
- 🖱️ **Interactive**: Context menu, double-click, drag & drop

### 🔧 **Quản lý học sinh đầy đủ**
- ➕ **Thêm học sinh**: Form validation đầy đủ
- ✏️ **Chỉnh sửa**: Double-click hoặc context menu
- 🗑️ **Xóa học sinh**: Xác nhận trước khi xóa
- 👁️ **Hiển thị**: Bảng dữ liệu với TreeView

### 🔍 **Tìm kiếm và lọc nâng cao**
- 🔎 **Tìm kiếm**: Theo mã số với highlight kết quả
- 🎯 **Lọc theo khối**: Dropdown selection
- 📊 **Sắp xếp**: Theo mã số hoặc điểm số
- 🔄 **Reset**: Khôi phục hiển thị ban đầu

### 📈 **Thống kê chi tiết**
- 📊 **Modal thống kê**: Cửa sổ popup với thông tin đầy đủ
- 🏆 **Học sinh xuất sắc**: Điểm cao nhất
- ⚠️ **Học sinh cần hỗ trợ**: Điểm thấp nhất
- 📈 **Phân tích**: Tỉ lệ đạt/không đạt, phân bố khối

### 🔗 **Visualization cấu trúc dữ liệu**
- 🎯 **Canvas drawing**: Vẽ nodes và arrows
- ➡️ **Linked List**: Hiển thị con trỏ next
- 🔄 **Real-time update**: Cập nhật khi thay đổi dữ liệu
- 📜 **Scrollable**: Cuộn ngang khi có nhiều node

### 💾 **Lưu trữ dữ liệu**
- 📁 **Export JSON**: Lưu dữ liệu ra file
- 📂 **Import JSON**: Tải dữ liệu từ file
- 📋 **Copy**: Sao chép thông tin học sinh
- 🔄 **Auto-backup**: Tự động đặt tên file theo thời gian

## 📁 Cấu trúc files

```
📂 Ung-Dung-Danh-Sach-Lien-Ket-Don-Quan-Ly-Hoc-Sinh/
├── 🐍 QuanLyHocSinh_GUI.py    # Chương trình Python GUI chính
├── 📄 requirements.txt        # Dependencies (chỉ built-in libs)
├── 📖 README-PYTHON-GUI.md    # Hướng dẫn Python GUI
├── 🌐 index.html              # Phiên bản web demo
├── 🎨 style.css               # Web styling
├── ⚡ script.js               # Web JavaScript
├── 💻 QuanLyHocSinh.cpp       # Phiên bản C++
├── 📄 BaoCaoDoAn.md           # Báo cáo đồ án
└── 📖 README.md               # Hướng dẫn tổng quan
```

## 🛠️ Yêu cầu hệ thống

### 📋 **Phần mềm cần thiết**
- **Python**: 3.6 trở lên
- **Tkinter**: Có sẵn trong Python (built-in)
- **Hệ điều hành**: Windows, macOS, Linux

### 📦 **Thư viện sử dụng**
```python
import tkinter as tk          # GUI framework
from tkinter import ttk       # Themed widgets
from tkinter import messagebox # Dialog boxes
from tkinter import simpledialog # Input dialogs
import json                   # JSON data handling
from datetime import datetime # Date/time utilities
```

**Lưu ý**: Tất cả thư viện đều có sẵn trong Python, không cần cài đặt thêm!

## 🚀 Hướng dẫn chạy ứng dụng

### 🔧 **Cách 1: Chạy trực tiếp**
```bash
# Mở terminal/command prompt
cd "Ung-Dung-Danh-Sach-Lien-Ket-Don-Quan-Ly-Hoc-Sinh"

# Chạy chương trình
python QuanLyHocSinh_GUI.py

# Hoặc trên một số hệ thống
python3 QuanLyHocSinh_GUI.py
```

### 🖱️ **Cách 2: Double-click (Windows)**
1. Mở File Explorer
2. Navigate đến thư mục chứa file
3. Double-click vào `QuanLyHocSinh_GUI.py`
4. Chọn "Open with Python" nếu được hỏi

### 🐧 **Cách 3: Linux/macOS**
```bash
# Cấp quyền thực thi
chmod +x QuanLyHocSinh_GUI.py

# Chạy
./QuanLyHocSinh_GUI.py
```

## 🎮 Hướng dẫn sử dụng

### 1️⃣ **Thêm học sinh mới**
- Điền thông tin vào form bên trái
- Click "➕ Thêm học sinh"
- Dữ liệu sẽ được validate tự động

### 2️⃣ **Tìm kiếm học sinh**
- Nhập mã số vào ô "Tìm theo mã số"
- Click nút 🔍 hoặc nhấn Enter
- Học sinh sẽ được highlight trong bảng

### 3️⃣ **Chỉnh sửa học sinh**
- **Cách 1**: Double-click vào dòng trong bảng
- **Cách 2**: Right-click → "✏️ Chỉnh sửa"
- Form sẽ được điền sẵn dữ liệu

### 4️⃣ **Xóa học sinh**
- Right-click vào dòng cần xóa
- Chọn "🗑️ Xóa"
- Xác nhận trong dialog

### 5️⃣ **Lọc và sắp xếp**
- **Lọc**: Chọn khối từ dropdown
- **Sắp xếp**: Click các nút sắp xếp
- **Reset**: Để về trạng thái ban đầu

### 6️⃣ **Xem thống kê**
- Click "📊 Thống kê"
- Cửa sổ popup hiển thị thông tin chi tiết

### 7️⃣ **Lưu/Tải dữ liệu**
- **Menu File** → "💾 Lưu dữ liệu"
- **Menu File** → "📂 Tải dữ liệu"
- File được lưu dạng JSON

## 🎨 Giao diện chi tiết

### 📐 **Layout Structure**
```
┌─────────────────────────────────────────────────────────┐
│                    🎓 Header                            │
├─────────────────────────────────────────────────────────┤
│              👨‍🎓 Student Info Card                      │
├─────────────┬─────────────────┬─────────────────────────┤
│   ➕ Form   │  🔍 Controls    │  🔗 Visualization       │
│   Thêm HS   │  Tìm & Lọc     │  Linked List            │
├─────────────┴─────────────────┴─────────────────────────┤
│              📋 Student Table (TreeView)               │
└─────────────────────────────────────────────────────────┘
```

### 🎨 **Color Scheme**
- **Header**: Dark blue (#2c3e50)
- **Form Panel**: Green accent (#27ae60)
- **Control Panel**: Blue accent (#3498db)
- **Visualization**: Orange accent (#e67e22)
- **Table Panel**: Purple accent (#8e44ad)

### 🖱️ **Interactive Elements**
- **Hover effects** trên buttons
- **Context menu** (right-click)
- **Double-click** để edit
- **Keyboard shortcuts** (Enter, Escape)

## 🔧 Cấu trúc kỹ thuật

### 📊 **Classes chính**
```python
class HocSinh:          # Đối tượng học sinh
class Node:             # Node trong linked list  
class LinkedList:       # Cấu trúc dữ liệu chính
class StudentManagementGUI:  # Giao diện chính
```

### 🎯 **Design Patterns**
- **MVC Pattern**: Model (LinkedList), View (GUI), Controller (Event handlers)
- **Observer Pattern**: GUI tự động cập nhật khi data thay đổi
- **Command Pattern**: Menu actions và button commands

### ⚡ **Performance**
- **O(n) operations**: Search, display, filter
- **O(1) operations**: Add to end (with tail pointer)
- **Memory efficient**: Chỉ lưu trữ dữ liệu cần thiết

## 🌟 Tính năng nâng cao

### 📊 **Data Validation**
```python
# Kiểm tra mã số (≥3 ký tự)
# Kiểm tra họ tên (≥2 ký tự)  
# Kiểm tra năm sinh (1990-2010)
# Kiểm tra khối (10, 11, 12)
# Kiểm tra điểm (0.0-10.0)
```

### 🎨 **GUI Enhancements**
- **TreeView**: Bảng dữ liệu chuyên nghiệp
- **Canvas**: Vẽ visualization tùy chỉnh
- **Modal dialogs**: Thống kê và confirmations
- **Menu system**: File operations và help

### 💾 **File Operations**
```python
# Export: JSON format với encoding UTF-8
# Import: Tự động validate dữ liệu
# Auto-naming: Timestamp trong tên file
# Error handling: Try-catch cho file I/O
```

## 🐛 Troubleshooting

### ❌ **Lỗi thường gặp**

1. **"No module named 'tkinter'"**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install python3-tk
   
   # CentOS/RHEL
   sudo yum install tkinter
   
   # macOS (với Homebrew)
   brew install python-tk
   ```

2. **"Permission denied"**
   ```bash
   chmod +x QuanLyHocSinh_GUI.py
   ```

3. **"Python not found"**
   - Cài đặt Python từ python.org
   - Thêm Python vào PATH

### 🔧 **Debug Mode**
```python
# Thêm vào đầu file để debug
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📊 So sánh các phiên bản

| Tính năng | C++ Console | Python GUI | Web Demo |
|-----------|-------------|------------|----------|
| **Giao diện** | Text-based | Desktop GUI | Web Browser |
| **Platform** | Cross-platform | Cross-platform | Universal |
| **Visualization** | Text art | Canvas drawing | HTML/CSS |
| **File I/O** | Manual | JSON auto | LocalStorage |
| **User Experience** | Command-line | Point & click | Touch-friendly |
| **Performance** | Fastest | Fast | Good |

## 🔮 Hướng phát triển

### 📈 **Tính năng mới**
- 📊 **Charts**: Matplotlib integration cho biểu đồ
- 🖼️ **Images**: Thêm ảnh đại diện học sinh
- 🔔 **Notifications**: Toast messages
- 🌙 **Dark mode**: Theme switching

### 🎨 **UI/UX Improvements**
- 🎭 **Animations**: Smooth transitions
- 🎨 **Themes**: Multiple color schemes
- 📱 **Responsive**: Better window resizing
- ♿ **Accessibility**: Screen reader support

### ⚡ **Performance**
- 🗄️ **Database**: SQLite integration
- 🔄 **Async**: Non-blocking operations
- 💾 **Caching**: Smart data caching
- 🔍 **Indexing**: Fast search algorithms

## 📞 Liên hệ

**Sinh viên thực hiện**: Huỳnh Thái Bảo
- 📧 **Email**: <EMAIL>
- 📱 **Phone**: 0355771075
- 🎓 **Lớp**: DX23TT11
- 🆔 **MSSV**: 170123488

---

🎉 **Cảm ơn bạn đã sử dụng ứng dụng Python GUI demo!**

⭐ **Nếu thấy hữu ích, hãy cho feedback để cải thiện!**
