/* Authentication Pages Styles */

/* Auth Section */
.auth-section {
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.auth-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(0,123,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
}

/* Auth Card */
.auth-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 3rem;
    position: relative;
    z-index: 1;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.auth-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.auth-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 123, 255, 0.3);
}

.auth-icon i {
    font-size: 2rem;
    color: white;
}

.auth-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: var(--secondary-color);
    font-size: 1.125rem;
    margin-bottom: 0;
}

/* Auth Form */
.auth-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.form-label i {
    color: var(--primary-color);
    width: 16px;
}

.form-control {
    padding: 0.875rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: var(--transition-base);
    background: #f8f9fa;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.1);
    background: white;
}

.form-control.is-invalid {
    border-color: var(--danger-color);
    background: #fff5f5;
}

.form-control.is-valid {
    border-color: var(--success-color);
    background: #f0fff4;
}

/* Password Input */
.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: var(--transition-base);
}

.password-toggle:hover {
    color: var(--primary-color);
    background: rgba(0, 123, 255, 0.1);
}

/* Password Strength */
.password-strength {
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

.strength-weak {
    color: var(--danger-color);
}

.strength-medium {
    color: var(--warning-color);
}

.strength-strong {
    color: var(--success-color);
}

.strength-bar {
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    margin-top: 0.25rem;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    transition: var(--transition-base);
    border-radius: 2px;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.form-check-label {
    font-size: 0.9rem;
    color: var(--dark-color);
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition-base);
}

.forgot-password:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* Auth Button */
.btn-auth {
    width: 100%;
    padding: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
    border-radius: 0.5rem;
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

.btn-auth:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3);
}

.btn-auth:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
}

/* Auth Divider */
.auth-divider {
    text-align: center;
    margin: 2rem 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e9ecef;
}

.auth-divider span {
    background: white;
    padding: 0 1rem;
    color: var(--secondary-color);
    font-size: 0.9rem;
    position: relative;
    z-index: 1;
}

/* Social Auth */
.social-auth {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.btn-social {
    flex: 1;
    padding: 0.875rem;
    font-weight: 600;
    border-radius: 0.5rem;
    transition: var(--transition-base);
    border: 2px solid transparent;
}

.btn-facebook {
    background: #1877f2;
    color: white;
}

.btn-facebook:hover {
    background: #166fe5;
    color: white;
    transform: translateY(-2px);
}

.btn-google {
    background: white;
    color: #333;
    border-color: #ddd;
}

.btn-google:hover {
    background: #f8f9fa;
    color: #333;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Auth Footer */
.auth-footer {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.auth-footer p {
    margin: 0;
    color: var(--secondary-color);
}

.auth-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-base);
}

.auth-footer a:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* Demo Accounts */
.demo-accounts {
    background: var(--light-color);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1.5rem;
    border: 1px solid #e9ecef;
}

.demo-accounts h6 {
    color: var(--primary-color);
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.demo-account {
    background: white;
    border: 1px solid #ddd;
    border-radius: 0.25rem;
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: var(--transition-base);
    font-size: 0.875rem;
}

.demo-account:last-child {
    margin-bottom: 0;
}

.demo-account:hover {
    border-color: var(--primary-color);
    background: rgba(0, 123, 255, 0.05);
}

/* Auth Image */
.auth-image {
    position: relative;
    height: 100%;
    min-height: 600px;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.auth-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.auth-image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 3rem 2rem 2rem;
}

.auth-image-overlay h3 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.benefits-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.benefits-list li {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.benefits-list li:last-child {
    margin-bottom: 0;
}

.benefits-list i {
    color: var(--success-color);
    width: 20px;
    flex-shrink: 0;
}

/* Invalid Feedback */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--danger-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-section {
        padding: 2rem 0;
    }
    
    .auth-card {
        padding: 2rem 1.5rem;
        margin: 1rem;
        border-radius: 0.75rem;
    }
    
    .auth-title {
        font-size: 1.75rem;
    }
    
    .auth-subtitle {
        font-size: 1rem;
    }
    
    .social-auth {
        flex-direction: column;
    }
    
    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .auth-image {
        min-height: 400px;
    }
    
    .auth-image-overlay {
        padding: 2rem 1.5rem 1.5rem;
    }
    
    .auth-image-overlay h3 {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .auth-card {
        padding: 1.5rem 1rem;
    }
    
    .auth-icon {
        width: 60px;
        height: 60px;
    }
    
    .auth-icon i {
        font-size: 1.5rem;
    }
    
    .auth-title {
        font-size: 1.5rem;
    }
    
    .form-control {
        padding: 0.75rem;
    }
    
    .btn-auth {
        padding: 0.875rem;
        font-size: 1rem;
    }
}

/* Animation for form validation */
.form-control.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Loading animation for buttons */
.btn-auth.loading .btn-text {
    opacity: 0;
}

.btn-auth.loading .btn-loading {
    display: block;
}

/* Success state */
.form-control.success {
    border-color: var(--success-color);
    background: #f0fff4;
}

.form-control.success + .invalid-feedback {
    color: var(--success-color);
}
