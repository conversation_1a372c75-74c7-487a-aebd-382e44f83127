# Ứng Dụng Quản Lý Hàng Hóa - <PERSON><PERSON> Sách Liên Kết Kép

## Mô tả
Ứng dụng console C# sử dụng cấu trúc dữ liệu danh sách liên kết kép để quản lý thông tin hàng hóa (họ<PERSON> sinh) bao gồm: mã số, họ tên, n<PERSON><PERSON> sinh, khối, lớp và điểm trung bình.

## Cấu trúc dữ liệu
- **Danh sách liên kết kép (Doubly Linked List)**: Mỗi node chứa thông tin hàng hóa và có con trỏ đến node trước và sau
- **Node**: Chứa dữ liệu HangHoa và 2 con trỏ (Previous, Next)

## Tính năng chính

### 1. Quản lý cơ bản
- ✅ Thêm hàng hóa (vào đầu, cuố<PERSON>, vị trí chỉ định)
- ✅ <PERSON><PERSON><PERSON> hàng hóa (the<PERSON> <PERSON><PERSON> số, vị trí)
- ✅ Cập nhật thông tin hàng hóa
- ✅ Hiển thị danh sách

### 2. Tì<PERSON> kiếm
- ✅ Tìm kiếm theo mã số
- ✅ Tìm kiếm theo họ tên (tìm kiếm gần đúng)

### 3. Lọc dữ liệu
- ✅ Lọc theo khối (10, 11, 12)
- ✅ Lọc theo lớp
- ✅ Lọc theo khoảng điểm
- ✅ Lọc theo xếp loại học lực

### 4. Sắp xếp
- ✅ Sắp xếp theo điểm trung bình (tăng/giảm dần)
- ✅ Sắp xếp theo họ tên (A-Z, Z-A)
- ✅ Sắp xếp theo năm sinh (tăng/giảm dần)

### 5. Thống kê
- ✅ Thống kê số lượng theo khối
- ✅ Thống kê số lượng theo xếp loại
- ✅ Tính điểm trung bình chung
- ✅ Tìm học sinh điểm cao nhất/thấp nhất
- ✅ Hiển thị tổng số học sinh

### 6. Tiện ích
- ✅ Tạo dữ liệu mẫu
- ✅ Giao diện menu đẹp với khung viền
- ✅ Hiển thị dữ liệu dạng bảng
- ✅ Xác nhận trước khi xóa

## Cấu trúc thư mục
```
DoublyLinkedListApp/
├── Models/
│   └── HangHoa.cs              # Model dữ liệu hàng hóa
├── DataStructures/
│   ├── DoublyLinkedListNode.cs # Node của danh sách liên kết kép
│   └── DoublyLinkedList.cs     # Cài đặt danh sách liên kết kép
├── Services/
│   └── HangHoaService.cs       # Service xử lý logic nghiệp vụ
├── Utils/
│   ├── MenuHelper.cs           # Helper cho menu và input
│   └── DataHelper.cs           # Helper cho hiển thị và dữ liệu mẫu
├── Program.cs                  # Entry point của ứng dụng
└── README.md                   # Tài liệu hướng dẫn
```

## Cách chạy ứng dụng

### Yêu cầu hệ thống
- .NET 8.0 SDK
- Windows/Linux/macOS

### Các bước chạy
1. Mở terminal/command prompt
2. Di chuyển đến thư mục dự án:
   ```bash
   cd DoublyLinkedListApp
   ```
3. Chạy ứng dụng:
   ```bash
   dotnet run
   ```

## Hướng dẫn sử dụng

### Menu chính
```
╔══════════════════════════════════════════════════════════════╗
║              QUẢN LÝ HÀNG HÓA - DANH SÁCH LIÊN KẾT KÉP       ║
╠══════════════════════════════════════════════════════════════╣
║  1. Thêm hàng hóa                                            ║
║  2. Xóa hàng hóa                                             ║
║  3. Tìm kiếm hàng hóa                                        ║
║  4. Hiển thị danh sách                                       ║
║  5. Sắp xếp dữ liệu                                          ║
║  6. Lọc dữ liệu                                              ║
║  7. Thống kê                                                 ║
║  8. Cập nhật thông tin                                       ║
║  9. Tạo dữ liệu mẫu                                          ║
║  0. Thoát chương trình                                       ║
╚══════════════════════════════════════════════════════════════╝
```

### Xếp loại học lực
- **Xuất sắc**: Điểm ≥ 9.0
- **Giỏi**: 8.0 ≤ Điểm < 9.0
- **Khá**: 6.5 ≤ Điểm < 8.0
- **Trung bình**: 5.0 ≤ Điểm < 6.5
- **Yếu**: Điểm < 5.0

## Đặc điểm kỹ thuật

### Danh sách liên kết kép
- **Ưu điểm**: 
  - Duyệt được cả 2 chiều (tiến/lùi)
  - Thêm/xóa hiệu quả ở đầu và cuối
  - Tối ưu khi truy cập từ 2 đầu
- **Độ phức tạp**:
  - Thêm/xóa đầu/cuối: O(1)
  - Tìm kiếm: O(n)
  - Truy cập theo index: O(n)

### Tính năng nâng cao
- **Tìm kiếm thông minh**: Tìm kiếm gần đúng cho họ tên
- **Sắp xếp linh hoạt**: Hỗ trợ nhiều tiêu chí sắp xếp
- **Thống kê đa dạng**: Nhiều loại thống kê hữu ích
- **Giao diện thân thiện**: Menu đẹp, hiển thị dạng bảng

## Dữ liệu mẫu
Ứng dụng cung cấp 15 bản ghi dữ liệu mẫu với đầy đủ thông tin học sinh từ khối 10-12 với các mức điểm khác nhau.

## Tác giả
Ứng dụng được phát triển để minh họa việc sử dụng cấu trúc dữ liệu danh sách liên kết kép trong quản lý dữ liệu thực tế.
