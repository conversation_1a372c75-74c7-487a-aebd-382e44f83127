// Electronics Shop - Main JavaScript

// Global Variables
let cart = JSON.parse(localStorage.getItem('cart')) || [];
let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
let user = JSON.parse(localStorage.getItem('user')) || null;

// Sample Products Data
const sampleProducts = [
    {
        id: 1,
        name: "Dell Latitude E7450",
        category: "laptop",
        brand: "Dell",
        price: 12500000,
        originalPrice: 15000000,
        condition: 95,
        image: "https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop",
        description: "Core i5-5300U, 8GB RAM, SSD 256GB",
        rating: 4.5,
        reviews: 23,
        inStock: true
    },
    {
        id: 2,
        name: "iPhone 12 Pro Max",
        category: "dien-thoai",
        brand: "Apple",
        price: 22500000,
        originalPrice: 28000000,
        condition: 90,
        image: "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop",
        description: "128GB, Pacific Blue, Fullbox",
        rating: 4.8,
        reviews: 45,
        inStock: true
    },
    {
        id: 3,
        name: "iPad Air 4th Gen",
        category: "tablet",
        brand: "Apple",
        price: 14800000,
        originalPrice: 18000000,
        condition: 98,
        image: "https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop",
        description: "64GB WiFi, Space Gray, Apple Pencil",
        rating: 4.7,
        reviews: 18,
        inStock: true
    },
    {
        id: 4,
        name: "AirPods Pro",
        category: "phu-kien",
        brand: "Apple",
        price: 4200000,
        originalPrice: 5500000,
        condition: 88,
        image: "https://images.unsplash.com/photo-1583394838336-acd977736f90?w=400&h=300&fit=crop",
        description: "Chống ồn chủ động, Wireless Charging",
        rating: 4.6,
        reviews: 67,
        inStock: true
    },
    {
        id: 5,
        name: "MacBook Pro 13\"",
        category: "laptop",
        brand: "Apple",
        price: 28500000,
        originalPrice: 35000000,
        condition: 92,
        image: "https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=400&h=300&fit=crop",
        description: "M1 Chip, 8GB RAM, 256GB SSD",
        rating: 4.9,
        reviews: 34,
        inStock: true
    },
    {
        id: 6,
        name: "Samsung Galaxy S21",
        category: "dien-thoai",
        brand: "Samsung",
        price: 15800000,
        originalPrice: 20000000,
        condition: 89,
        image: "https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=400&h=300&fit=crop",
        description: "128GB, Phantom Gray, Fullbox",
        rating: 4.4,
        reviews: 29,
        inStock: true
    }
];

// Initialize App
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Initialize AOS
    AOS.init({
        duration: 1000,
        once: true,
        offset: 100
    });
    
    // Load user state
    updateUserInterface();
    
    // Load cart and wishlist
    updateCartDisplay();
    updateWishlistDisplay();
    
    // Load featured products
    loadFeaturedProducts();
    
    // Initialize search
    initializeSearch();
    
    // Initialize scroll events
    initializeScrollEvents();
    
    // Initialize tooltips
    initializeTooltips();
    
    console.log('Electronics Shop initialized successfully!');
}

// User Management
function updateUserInterface() {
    const userDisplayName = document.getElementById('userDisplayName');
    const loginLink = document.getElementById('loginLink');
    const registerLink = document.getElementById('registerLink');
    const profileLink = document.getElementById('profileLink');
    const ordersLink = document.getElementById('ordersLink');
    const logoutLink = document.getElementById('logoutLink');
    
    if (user) {
        userDisplayName.textContent = user.name;
        loginLink.style.display = 'none';
        registerLink.style.display = 'none';
        profileLink.style.display = 'block';
        ordersLink.style.display = 'block';
        logoutLink.style.display = 'block';
    } else {
        userDisplayName.textContent = 'Tài khoản';
        loginLink.style.display = 'block';
        registerLink.style.display = 'block';
        profileLink.style.display = 'none';
        ordersLink.style.display = 'none';
        logoutLink.style.display = 'none';
    }
}

function logout() {
    user = null;
    localStorage.removeItem('user');
    updateUserInterface();
    showNotification('Đã đăng xuất thành công', 'success');
}

// Cart Management
function addToCart(productId, quantity = 1) {
    const product = sampleProducts.find(p => p.id === productId);
    if (!product) {
        showNotification('Sản phẩm không tồn tại', 'error');
        return;
    }
    
    if (!product.inStock) {
        showNotification('Sản phẩm đã hết hàng', 'error');
        return;
    }
    
    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        existingItem.quantity += quantity;
    } else {
        cart.push({
            id: product.id,
            name: product.name,
            price: product.price,
            image: product.image,
            quantity: quantity,
            condition: product.condition
        });
    }
    
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartDisplay();
    showNotification('Đã thêm sản phẩm vào giỏ hàng', 'success');
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartDisplay();
    showNotification('Đã xóa sản phẩm khỏi giỏ hàng', 'info');
}

function updateCartQuantity(productId, quantity) {
    const item = cart.find(item => item.id === productId);
    if (item) {
        if (quantity <= 0) {
            removeFromCart(productId);
        } else {
            item.quantity = quantity;
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartDisplay();
        }
    }
}

function updateCartDisplay() {
    const cartCount = document.getElementById('cartCount');
    const cartItems = document.getElementById('cartItems');
    const cartTotal = document.getElementById('cartTotal');
    const cartTotalAmount = document.getElementById('cartTotalAmount');
    
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    const totalAmount = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    cartCount.textContent = totalItems;
    
    if (cart.length === 0) {
        cartItems.innerHTML = `
            <div class="text-center py-3 text-muted">
                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                <p>Giỏ hàng trống</p>
            </div>
        `;
        cartTotal.style.display = 'none';
    } else {
        cartItems.innerHTML = cart.map(item => `
            <div class="cart-item">
                <img src="${item.image}" alt="${item.name}">
                <div class="cart-item-info">
                    <div class="cart-item-name">${item.name}</div>
                    <div class="cart-item-price">${formatCurrency(item.price)}</div>
                    <div class="cart-item-quantity">Số lượng: ${item.quantity}</div>
                </div>
                <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${item.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
        
        cartTotalAmount.textContent = formatCurrency(totalAmount);
        cartTotal.style.display = 'block';
    }
}

// Wishlist Management
function toggleWishlist(productId) {
    if (!productId) {
        // Show wishlist page
        window.location.href = 'wishlist.html';
        return;
    }
    
    const existingIndex = wishlist.findIndex(item => item.id === productId);
    
    if (existingIndex > -1) {
        wishlist.splice(existingIndex, 1);
        showNotification('Đã xóa khỏi danh sách yêu thích', 'info');
    } else {
        const product = sampleProducts.find(p => p.id === productId);
        if (product) {
            wishlist.push({
                id: product.id,
                name: product.name,
                price: product.price,
                image: product.image,
                condition: product.condition
            });
            showNotification('Đã thêm vào danh sách yêu thích', 'success');
        }
    }
    
    localStorage.setItem('wishlist', JSON.stringify(wishlist));
    updateWishlistDisplay();
}

function updateWishlistDisplay() {
    const wishlistCount = document.getElementById('wishlistCount');
    wishlistCount.textContent = wishlist.length;
}

// Product Display
function loadFeaturedProducts() {
    const container = document.getElementById('featuredProductsContainer');
    if (!container) return;
    
    const featuredProducts = sampleProducts.slice(0, 4);
    
    container.innerHTML = featuredProducts.map(product => createProductCard(product)).join('');
}

function createProductCard(product) {
    const discountPercent = Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);
    const conditionClass = getConditionClass(product.condition);
    const conditionText = getConditionText(product.condition);
    
    return `
        <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
            <div class="card product-card">
                <div class="position-relative">
                    <img src="${product.image}" class="card-img-top product-image" alt="${product.name}">
                    <span class="badge condition-badge ${conditionClass}">${conditionText}</span>
                    <div class="product-overlay">
                        <button class="btn btn-primary btn-sm me-2" onclick="addToCart(${product.id})">
                            <i class="fas fa-cart-plus"></i>
                        </button>
                        <button class="btn btn-outline-light btn-sm me-2" onclick="toggleWishlist(${product.id})">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button class="btn btn-info btn-sm" onclick="viewProduct(${product.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <h6 class="card-title">
                        <a href="product.html?id=${product.id}" class="text-decoration-none">${product.name}</a>
                    </h6>
                    <p class="card-text text-muted small">${product.description}</p>
                    
                    <div class="product-rating mb-2">
                        ${generateStarRating(product.rating)}
                        <span class="text-muted small">(${product.reviews})</span>
                    </div>
                    
                    <div class="product-price mb-3">
                        <span class="price">${formatCurrency(product.price)}</span>
                        ${product.originalPrice > product.price ? `
                            <br>
                            <span class="original-price">${formatCurrency(product.originalPrice)}</span>
                            <span class="discount-badge ms-2">-${discountPercent}%</span>
                        ` : ''}
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="addToCart(${product.id})">
                            <i class="fas fa-cart-plus me-2"></i>Thêm vào giỏ
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function getConditionClass(condition) {
    if (condition >= 95) return 'condition-excellent';
    if (condition >= 85) return 'condition-good';
    if (condition >= 70) return 'condition-fair';
    return 'condition-poor';
}

function getConditionText(condition) {
    if (condition >= 95) return `Như mới ${condition}%`;
    if (condition >= 85) return `Tốt ${condition}%`;
    if (condition >= 70) return `Khá tốt ${condition}%`;
    return `Cần sửa ${condition}%`;
}

function generateStarRating(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let stars = '';
    
    for (let i = 0; i < fullStars; i++) {
        stars += '<i class="fas fa-star text-warning"></i>';
    }
    
    if (hasHalfStar) {
        stars += '<i class="fas fa-star-half-alt text-warning"></i>';
    }
    
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
        stars += '<i class="far fa-star text-muted"></i>';
    }
    
    return stars;
}

function viewProduct(productId) {
    window.location.href = `product.html?id=${productId}`;
}

// Search Functionality
function initializeSearch() {
    const searchInput = document.getElementById('searchInput');
    const searchSuggestions = document.getElementById('searchSuggestions');
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            if (query.length >= 2) {
                showSearchSuggestions(query);
            } else {
                hideSearchSuggestions();
            }
        });
        
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        
        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchSuggestions.contains(e.target)) {
                hideSearchSuggestions();
            }
        });
    }
}

function showSearchSuggestions(query) {
    const searchSuggestions = document.getElementById('searchSuggestions');
    const suggestions = sampleProducts
        .filter(product => 
            product.name.toLowerCase().includes(query.toLowerCase()) ||
            product.brand.toLowerCase().includes(query.toLowerCase()) ||
            product.description.toLowerCase().includes(query.toLowerCase())
        )
        .slice(0, 5);
    
    if (suggestions.length > 0) {
        searchSuggestions.innerHTML = suggestions.map(product => `
            <div class="search-suggestion-item" onclick="selectSuggestion('${product.name}')">
                <div class="d-flex align-items-center">
                    <img src="${product.image}" alt="${product.name}" style="width: 40px; height: 40px; object-fit: cover;" class="rounded me-3">
                    <div>
                        <div class="fw-bold">${product.name}</div>
                        <small class="text-muted">${formatCurrency(product.price)}</small>
                    </div>
                </div>
            </div>
        `).join('');
        searchSuggestions.style.display = 'block';
    } else {
        searchSuggestions.innerHTML = `
            <div class="search-suggestion-item">
                <div class="text-muted">Không tìm thấy sản phẩm nào</div>
            </div>
        `;
        searchSuggestions.style.display = 'block';
    }
}

function hideSearchSuggestions() {
    const searchSuggestions = document.getElementById('searchSuggestions');
    searchSuggestions.style.display = 'none';
}

function selectSuggestion(productName) {
    const searchInput = document.getElementById('searchInput');
    searchInput.value = productName;
    hideSearchSuggestions();
    performSearch();
}

function performSearch() {
    const searchInput = document.getElementById('searchInput');
    const query = searchInput.value.trim();
    
    if (query) {
        window.location.href = `search.html?q=${encodeURIComponent(query)}`;
    }
}

// Newsletter Subscription
function subscribeNewsletter(event) {
    event.preventDefault();
    const email = event.target.querySelector('input[type="email"]').value;
    
    // Simulate API call
    showLoading();
    setTimeout(() => {
        hideLoading();
        showNotification('Đăng ký nhận tin thành công!', 'success');
        event.target.reset();
    }, 1000);
}

// Utility Functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
    }).format(amount);
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

function showLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.classList.add('show');
    }
}

function hideLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.classList.remove('show');
    }
}

// Scroll Events
function initializeScrollEvents() {
    const backToTopBtn = document.getElementById('backToTop');
    
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.classList.add('show');
        } else {
            backToTopBtn.classList.remove('show');
        }
    });
}

function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Initialize Tooltips
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Export functions for global access
window.addToCart = addToCart;
window.removeFromCart = removeFromCart;
window.updateCartQuantity = updateCartQuantity;
window.toggleWishlist = toggleWishlist;
window.viewProduct = viewProduct;
window.performSearch = performSearch;
window.selectSuggestion = selectSuggestion;
window.subscribeNewsletter = subscribeNewsletter;
window.scrollToTop = scrollToTop;
window.logout = logout;
window.sampleProducts = sampleProducts;
window.formatCurrency = formatCurrency;
window.generateStarRating = generateStarRating;
window.getConditionClass = getConditionClass;
window.getConditionText = getConditionText;
window.createProductCard = createProductCard;
window.showNotification = showNotification;
window.showLoading = showLoading;
window.hideLoading = hideLoading;
