@model GocPho.Models.Order
@{
    ViewData["Title"] = $"Chi tiết đơn hàng #{Model.Id}";
    Layout = "_AdminLayout";
}

<!-- Order Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow border-0">
            <div class="card-header" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-receipt me-2"></i>Chi tiết đơn hàng #@Model.Id
                    </h5>
                    <a href="@Url.Action("Orders", "Admin")" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Quay lại
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Thông tin khách hàng</h6>
                        <div class="mb-2">
                            <strong><i class="fas fa-user me-2 text-primary"></i>@Model.CustomerName</strong>
                        </div>
                        <div class="mb-2">
                            <i class="fas fa-phone me-2 text-success"></i>@Model.PhoneNumber
                        </div>
                        <div class="mb-2">
                            <i class="fas fa-map-marker-alt me-2 text-danger"></i>@Model.DeliveryAddress
                        </div>
                        @if (!string.IsNullOrEmpty(Model.Notes))
                        {
                            <div class="mb-2">
                                <i class="fas fa-sticky-note me-2 text-warning"></i>
                                <em>@Model.Notes</em>
                            </div>
                        }
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Thông tin đơn hàng</h6>
                        <div class="mb-2">
                            <strong>Ngày đặt:</strong> @Model.OrderDate.ToString("dd/MM/yyyy HH:mm")
                        </div>
                        @if (Model.DeliveryDate.HasValue)
                        {
                            <div class="mb-2">
                                <strong>Ngày giao:</strong> @Model.DeliveryDate.Value.ToString("dd/MM/yyyy HH:mm")
                            </div>
                        }
                        <div class="mb-2">
                            <strong>Trạng thái:</strong>
                            <span class="badge <EMAIL>().ToLower() fs-6 ms-2">
                                @switch (Model.Status)
                                {
                                    case GocPho.Models.OrderStatus.Pending:
                                        <text>Chờ xác nhận</text>
                                        break;
                                    case GocPho.Models.OrderStatus.Confirmed:
                                        <text>Đã xác nhận</text>
                                        break;
                                    case GocPho.Models.OrderStatus.Preparing:
                                        <text>Đang chuẩn bị</text>
                                        break;
                                    case GocPho.Models.OrderStatus.Delivering:
                                        <text>Đang giao hàng</text>
                                        break;
                                    case GocPho.Models.OrderStatus.Delivered:
                                        <text>Đã giao hàng</text>
                                        break;
                                    case GocPho.Models.OrderStatus.Cancelled:
                                        <text>Đã hủy</text>
                                        break;
                                }
                            </span>
                        </div>
                        <div class="mb-2">
                            <strong>Tổng tiền:</strong> 
                            <span class="text-success fw-bold fs-5">@Model.TotalAmount.ToString("N0") VNĐ</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Items -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow border-0">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-bag me-2 text-warning"></i>Sản phẩm đã đặt
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th>Sản phẩm</th>
                                <th>Hình ảnh</th>
                                <th class="text-center">Số lượng</th>
                                <th class="text-end">Đơn giá</th>
                                <th class="text-end">Thành tiền</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.OrderItems)
                            {
                                <tr>
                                    <td>
                                        <div>
                                            <strong>@item.Product.Name</strong>
                                            <br>
                                            <small class="text-muted">@item.Product.Description</small>
                                        </div>
                                    </td>
                                    <td>
                                        <img src="@item.Product.ImageUrl" alt="@item.Product.Name" 
                                             class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-primary fs-6">@item.Quantity</span>
                                    </td>
                                    <td class="text-end">@item.UnitPrice.ToString("N0") VNĐ</td>
                                    <td class="text-end fw-bold">@((item.Quantity * item.UnitPrice).ToString("N0")) VNĐ</td>
                                </tr>
                            }
                        </tbody>
                        <tfoot class="bg-light">
                            <tr>
                                <th colspan="4" class="text-end">Tổng cộng:</th>
                                <th class="text-end text-success fs-5">@Model.TotalAmount.ToString("N0") VNĐ</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Actions -->
@if (Model.Status != GocPho.Models.OrderStatus.Delivered && Model.Status != GocPho.Models.OrderStatus.Cancelled)
{
    <div class="row">
        <div class="col-12">
            <div class="card shadow border-0">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2 text-warning"></i>Cập nhật trạng thái
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        @if (Model.Status == GocPho.Models.OrderStatus.Pending)
                        {
                            <div class="col-md-3">
                                <form method="post" asp-action="UpdateOrderStatus">
                                    <input type="hidden" name="orderId" value="@Model.Id" />
                                    <input type="hidden" name="status" value="@((int)GocPho.Models.OrderStatus.Confirmed)" />
                                    <button type="submit" class="btn btn-info w-100 py-2">
                                        <i class="fas fa-check me-2"></i>Xác nhận đơn hàng
                                    </button>
                                </form>
                            </div>
                        }
                        @if (Model.Status == GocPho.Models.OrderStatus.Confirmed)
                        {
                            <div class="col-md-3">
                                <form method="post" asp-action="UpdateOrderStatus">
                                    <input type="hidden" name="orderId" value="@Model.Id" />
                                    <input type="hidden" name="status" value="@((int)GocPho.Models.OrderStatus.Preparing)" />
                                    <button type="submit" class="btn btn-primary w-100 py-2">
                                        <i class="fas fa-cog me-2"></i>Bắt đầu chuẩn bị
                                    </button>
                                </form>
                            </div>
                        }
                        @if (Model.Status == GocPho.Models.OrderStatus.Preparing)
                        {
                            <div class="col-md-3">
                                <form method="post" asp-action="UpdateOrderStatus">
                                    <input type="hidden" name="orderId" value="@Model.Id" />
                                    <input type="hidden" name="status" value="@((int)GocPho.Models.OrderStatus.Delivering)" />
                                    <button type="submit" class="btn btn-warning w-100 py-2">
                                        <i class="fas fa-truck me-2"></i>Bắt đầu giao hàng
                                    </button>
                                </form>
                            </div>
                        }
                        @if (Model.Status == GocPho.Models.OrderStatus.Delivering)
                        {
                            <div class="col-md-3">
                                <form method="post" asp-action="UpdateOrderStatus">
                                    <input type="hidden" name="orderId" value="@Model.Id" />
                                    <input type="hidden" name="status" value="@((int)GocPho.Models.OrderStatus.Delivered)" />
                                    <button type="submit" class="btn btn-success w-100 py-2">
                                        <i class="fas fa-check-circle me-2"></i>Hoàn thành giao hàng
                                    </button>
                                </form>
                            </div>
                        }
                        <div class="col-md-3">
                            <form method="post" asp-action="UpdateOrderStatus" onsubmit="return confirm('Bạn có chắc muốn hủy đơn hàng này?')">
                                <input type="hidden" name="orderId" value="@Model.Id" />
                                <input type="hidden" name="status" value="@((int)GocPho.Models.OrderStatus.Cancelled)" />
                                <button type="submit" class="btn btn-danger w-100 py-2">
                                    <i class="fas fa-times me-2"></i>Hủy đơn hàng
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .status-pending { background-color: #dc3545; }
    .status-confirmed { background-color: #17a2b8; }
    .status-preparing { background-color: #007bff; }
    .status-delivering { background-color: #ffc107; color: #000; }
    .status-delivered { background-color: #28a745; }
    .status-cancelled { background-color: #6c757d; }
</style>
