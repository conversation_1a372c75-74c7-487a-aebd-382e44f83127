// C<PERSON>u trúc dữ liệu <PERSON>de cho <PERSON>t <PERSON>n
class Node {
    constructor(student) {
        this.data = student;
        this.next = null;
    }
}

// Cấu trúc dữ liệu Danh S<PERSON>ch <PERSON>t <PERSON>n
class LinkedList {
    constructor() {
        this.head = null;
        this.size = 0;
    }

    // Thêm học sinh vào cuối danh sách
    append(student) {
        const newNode = new Node(student);
        
        if (!this.head) {
            this.head = newNode;
        } else {
            let current = this.head;
            while (current.next) {
                current = current.next;
            }
            current.next = newNode;
        }
        this.size++;
    }

    // X<PERSON>a học sinh theo mã số
    deleteByMaSo(maSo) {
        if (!this.head) return false;

        if (this.head.data.maSo === maSo) {
            this.head = this.head.next;
            this.size--;
            return true;
        }

        let current = this.head;
        while (current.next && current.next.data.maSo !== maSo) {
            current = current.next;
        }

        if (current.next) {
            current.next = current.next.next;
            this.size--;
            return true;
        }
        return false;
    }

    // Tìm kiếm học sinh theo mã số
    findByMaSo(maSo) {
        let current = this.head;
        while (current) {
            if (current.data.maSo === maSo) {
                return current.data;
            }
            current = current.next;
        }
        return null;
    }

    // Chuyển đổi thành mảng để dễ xử lý
    toArray() {
        const result = [];
        let current = this.head;
        while (current) {
            result.push(current.data);
            current = current.next;
        }
        return result;
    }

    // Xóa tất cả
    clear() {
        this.head = null;
        this.size = 0;
    }

    // Cập nhật học sinh
    updateStudent(maSo, newData) {
        let current = this.head;
        while (current) {
            if (current.data.maSo === maSo) {
                current.data = { ...current.data, ...newData };
                return true;
            }
            current = current.next;
        }
        return false;
    }
}

// Khởi tạo danh sách liên kết
const studentList = new LinkedList();

// Dữ liệu mẫu
const sampleData = [
    { maSo: 'HS001', hoTen: 'Nguyễn Văn An', namSinh: 2005, khoi: 12, lop: 'A1', diemTB: 8.5 },
    { maSo: 'HS002', hoTen: 'Trần Thị Bình', namSinh: 2006, khoi: 11, lop: 'B2', diemTB: 7.2 },
    { maSo: 'HS003', hoTen: 'Lê Văn Cường', namSinh: 2007, khoi: 10, lop: 'C3', diemTB: 6.8 },
    { maSo: 'HS004', hoTen: 'Phạm Thị Dung', namSinh: 2005, khoi: 12, lop: 'A2', diemTB: 9.1 },
    { maSo: 'HS005', hoTen: 'Hoàng Văn Em', namSinh: 2006, khoi: 11, lop: 'B1', diemTB: 5.5 }
];

// Biến toàn cục
let currentFilter = null;
let editingStudent = null;

// Khởi tạo ứng dụng
document.addEventListener('DOMContentLoaded', function() {
    // Thêm dữ liệu mẫu
    sampleData.forEach(student => studentList.append(student));
    
    // Hiển thị dữ liệu ban đầu
    displayStudents();
    updateVisualization();
    updateStats();

    // Xử lý form thêm học sinh
    document.getElementById('studentForm').addEventListener('submit', handleAddStudent);
});

// Xử lý thêm học sinh
function handleAddStudent(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const student = {
        maSo: document.getElementById('maSo').value.trim(),
        hoTen: document.getElementById('hoTen').value.trim(),
        namSinh: parseInt(document.getElementById('namSinh').value),
        khoi: parseInt(document.getElementById('khoi').value),
        lop: document.getElementById('lop').value.trim(),
        diemTB: parseFloat(document.getElementById('diemTB').value)
    };

    // Kiểm tra trùng mã số
    if (studentList.findByMaSo(student.maSo)) {
        showNotification('Mã số học sinh đã tồn tại!', 'error');
        return;
    }

    // Validation
    if (!validateStudent(student)) {
        return;
    }

    if (editingStudent) {
        // Cập nhật học sinh
        studentList.updateStudent(editingStudent, student);
        editingStudent = null;
        document.querySelector('#studentForm button[type="submit"]').innerHTML = 
            '<i class="fas fa-plus"></i> Thêm học sinh';
        showNotification('Cập nhật học sinh thành công!', 'success');
    } else {
        // Thêm học sinh mới
        studentList.append(student);
        showNotification('Thêm học sinh thành công!', 'success');
    }

    // Reset form
    e.target.reset();
    
    // Cập nhật hiển thị
    displayStudents();
    updateVisualization();
    updateStats();
}

// Validation dữ liệu học sinh
function validateStudent(student) {
    if (!student.maSo || student.maSo.length < 3) {
        showNotification('Mã số học sinh phải có ít nhất 3 ký tự!', 'error');
        return false;
    }
    
    if (!student.hoTen || student.hoTen.length < 2) {
        showNotification('Họ tên phải có ít nhất 2 ký tự!', 'error');
        return false;
    }
    
    if (student.namSinh < 1990 || student.namSinh > 2010) {
        showNotification('Năm sinh phải từ 1990 đến 2010!', 'error');
        return false;
    }
    
    if (![10, 11, 12].includes(student.khoi)) {
        showNotification('Khối phải là 10, 11 hoặc 12!', 'error');
        return false;
    }
    
    if (!student.lop || student.lop.length < 1) {
        showNotification('Lớp không được để trống!', 'error');
        return false;
    }
    
    if (student.diemTB < 0 || student.diemTB > 10) {
        showNotification('Điểm trung bình phải từ 0 đến 10!', 'error');
        return false;
    }
    
    return true;
}

// Hiển thị danh sách học sinh
function displayStudents(students = null) {
    const tbody = document.getElementById('studentTableBody');
    const data = students || studentList.toArray();
    
    tbody.innerHTML = '';
    
    if (data.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center" style="padding: 2rem; color: #6c757d; font-style: italic;">
                    <i class="fas fa-info-circle"></i> Không có dữ liệu học sinh
                </td>
            </tr>
        `;
        return;
    }
    
    data.forEach((student, index) => {
        const row = document.createElement('tr');
        row.className = 'fade-in';
        row.innerHTML = `
            <td>${index + 1}</td>
            <td><strong>${student.maSo}</strong></td>
            <td>${student.hoTen}</td>
            <td>${student.namSinh}</td>
            <td>
                <span class="badge badge-khoi-${student.khoi}">Khối ${student.khoi}</span>
            </td>
            <td>${student.lop}</td>
            <td>
                <span class="badge ${getDiemBadgeClass(student.diemTB)}">
                    ${student.diemTB.toFixed(1)}
                </span>
            </td>
            <td>
                <button onclick="editStudent('${student.maSo}')" class="btn btn-edit">
                    <i class="fas fa-edit"></i>
                </button>
                <button onclick="deleteStudent('${student.maSo}')" class="btn btn-delete">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Lấy class badge cho điểm
function getDiemBadgeClass(diem) {
    if (diem >= 8.0) return 'text-success';
    if (diem >= 6.5) return 'text-warning';
    return 'text-danger';
}

// Cập nhật visualization của linked list
function updateVisualization() {
    const container = document.getElementById('linkedListVisualization');
    const students = studentList.toArray();
    
    if (students.length === 0) {
        container.innerHTML = `
            <div class="empty-list">
                <i class="fas fa-info-circle"></i>
                Danh sách rỗng - Hãy thêm học sinh để xem cấu trúc liên kết
            </div>
        `;
        return;
    }
    
    let html = '';
    students.forEach((student, index) => {
        html += `
            <div class="node">
                <div class="node-box">
                    <div class="student-id">${student.maSo}</div>
                    <div class="student-name">${student.hoTen}</div>
                </div>
                ${index < students.length - 1 ? '<div class="arrow">→</div>' : '<div class="arrow">∅</div>'}
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Cập nhật thống kê
function updateStats() {
    const total = studentList.size;
    document.getElementById('totalCount').textContent = `Tổng: ${total} học sinh`;
}

// Tìm kiếm học sinh
function searchStudent() {
    const maSo = document.getElementById('searchMaSo').value.trim();
    if (!maSo) {
        showNotification('Vui lòng nhập mã số cần tìm!', 'warning');
        return;
    }
    
    const student = studentList.findByMaSo(maSo);
    if (student) {
        displayStudents([student]);
        showNotification(`Tìm thấy học sinh: ${student.hoTen}`, 'success');
        
        // Highlight trong visualization
        highlightNodeInVisualization(maSo);
    } else {
        displayStudents([]);
        showNotification('Không tìm thấy học sinh!', 'error');
    }
}

// Highlight node trong visualization
function highlightNodeInVisualization(maSo) {
    const nodes = document.querySelectorAll('.node-box');
    nodes.forEach(node => {
        const studentId = node.querySelector('.student-id').textContent;
        if (studentId === maSo) {
            node.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
            node.style.transform = 'scale(1.1)';
            setTimeout(() => {
                node.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                node.style.transform = 'scale(1)';
            }, 2000);
        }
    });
}

// Lọc theo khối
function filterByKhoi() {
    const khoi = document.getElementById('filterKhoi').value;
    if (!khoi) {
        displayStudents();
        currentFilter = null;
        return;
    }
    
    const filtered = studentList.toArray().filter(student => student.khoi == khoi);
    displayStudents(filtered);
    currentFilter = { type: 'khoi', value: khoi };
    showNotification(`Hiển thị ${filtered.length} học sinh khối ${khoi}`, 'info');
}

// Lọc theo điểm
function filterByDiem() {
    const diem = parseFloat(document.getElementById('filterDiem').value);
    const operator = document.getElementById('filterOperator').value;
    
    if (isNaN(diem)) {
        showNotification('Vui lòng nhập điểm hợp lệ!', 'warning');
        return;
    }
    
    const filtered = studentList.toArray().filter(student => {
        return operator === 'gte' ? student.diemTB >= diem : student.diemTB < diem;
    });
    
    displayStudents(filtered);
    currentFilter = { type: 'diem', value: diem, operator };
    
    const operatorText = operator === 'gte' ? '>=' : '<';
    showNotification(`Hiển thị ${filtered.length} học sinh có điểm ${operatorText} ${diem}`, 'info');
}

// Sắp xếp theo mã số
function sortByMaSo() {
    const students = studentList.toArray();
    students.sort((a, b) => a.maSo.localeCompare(b.maSo));
    displayStudents(students);
    showNotification('Đã sắp xếp theo mã số!', 'success');
}

// Sắp xếp theo điểm
function sortByDiem() {
    const students = studentList.toArray();
    students.sort((a, b) => b.diemTB - a.diemTB);
    displayStudents(students);
    showNotification('Đã sắp xếp theo điểm (giảm dần)!', 'success');
}

// Chỉnh sửa học sinh
function editStudent(maSo) {
    const student = studentList.findByMaSo(maSo);
    if (!student) {
        showNotification('Không tìm thấy học sinh!', 'error');
        return;
    }
    
    // Điền dữ liệu vào form
    document.getElementById('maSo').value = student.maSo;
    document.getElementById('hoTen').value = student.hoTen;
    document.getElementById('namSinh').value = student.namSinh;
    document.getElementById('khoi').value = student.khoi;
    document.getElementById('lop').value = student.lop;
    document.getElementById('diemTB').value = student.diemTB;
    
    // Đổi nút thành "Cập nhật"
    document.querySelector('#studentForm button[type="submit"]').innerHTML = 
        '<i class="fas fa-save"></i> Cập nhật học sinh';
    
    editingStudent = maSo;
    
    // Scroll to form
    document.getElementById('studentForm').scrollIntoView({ behavior: 'smooth' });
    
    showNotification(`Đang chỉnh sửa học sinh: ${student.hoTen}`, 'info');
}

// Xóa học sinh
function deleteStudent(maSo) {
    const student = studentList.findByMaSo(maSo);
    if (!student) {
        showNotification('Không tìm thấy học sinh!', 'error');
        return;
    }
    
    if (confirm(`Bạn có chắc chắn muốn xóa học sinh ${student.hoTen} (${maSo})?`)) {
        if (studentList.deleteByMaSo(maSo)) {
            displayStudents();
            updateVisualization();
            updateStats();
            showNotification(`Đã xóa học sinh: ${student.hoTen}`, 'success');
        } else {
            showNotification('Có lỗi khi xóa học sinh!', 'error');
        }
    }
}

// Xóa tất cả
function clearAll() {
    if (studentList.size === 0) {
        showNotification('Danh sách đã rỗng!', 'warning');
        return;
    }
    
    if (confirm(`Bạn có chắc chắn muốn xóa tất cả ${studentList.size} học sinh?`)) {
        studentList.clear();
        displayStudents();
        updateVisualization();
        updateStats();
        
        // Reset filters
        document.getElementById('filterKhoi').value = '';
        document.getElementById('searchMaSo').value = '';
        document.getElementById('filterDiem').value = '';
        currentFilter = null;
        
        showNotification('Đã xóa tất cả học sinh!', 'success');
    }
}

// Hiển thị thống kê
function showStatistics() {
    const students = studentList.toArray();

    if (students.length === 0) {
        showNotification('Không có dữ liệu để thống kê!', 'warning');
        return;
    }

    // Thống kê theo khối
    const khoiStats = {
        10: students.filter(s => s.khoi === 10).length,
        11: students.filter(s => s.khoi === 11).length,
        12: students.filter(s => s.khoi === 12).length
    };

    // Thống kê điểm
    const diemArray = students.map(s => s.diemTB);
    const maxDiem = Math.max(...diemArray);
    const minDiem = Math.min(...diemArray);
    const avgDiem = diemArray.reduce((sum, d) => sum + d, 0) / diemArray.length;

    // Học sinh điểm cao nhất và thấp nhất
    const topStudent = students.find(s => s.diemTB === maxDiem);
    const lowStudent = students.find(s => s.diemTB === minDiem);

    // Thống kê đạt/không đạt (điểm chuẩn 5.0)
    const passed = students.filter(s => s.diemTB >= 5.0).length;
    const failed = students.length - passed;
    const passRate = (passed / students.length * 100).toFixed(1);

    const statsContent = `
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">${students.length}</div>
                <div class="stat-label">Tổng học sinh</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${avgDiem.toFixed(2)}</div>
                <div class="stat-label">Điểm TB chung</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${passRate}%</div>
                <div class="stat-label">Tỉ lệ đạt</div>
            </div>
        </div>

        <h4><i class="fas fa-chart-pie"></i> Thống kê theo khối</h4>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">${khoiStats[10]}</div>
                <div class="stat-label">Khối 10</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${khoiStats[11]}</div>
                <div class="stat-label">Khối 11</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${khoiStats[12]}</div>
                <div class="stat-label">Khối 12</div>
            </div>
        </div>

        <h4><i class="fas fa-trophy"></i> Học sinh xuất sắc</h4>
        <div class="student-highlight">
            <div class="highlight-card bg-success">
                <strong>Điểm cao nhất: ${maxDiem.toFixed(1)}</strong><br>
                ${topStudent.hoTen} (${topStudent.maSo}) - Lớp ${topStudent.lop}
            </div>
        </div>

        <h4><i class="fas fa-exclamation-triangle"></i> Học sinh cần hỗ trợ</h4>
        <div class="student-highlight">
            <div class="highlight-card bg-warning">
                <strong>Điểm thấp nhất: ${minDiem.toFixed(1)}</strong><br>
                ${lowStudent.hoTen} (${lowStudent.maSo}) - Lớp ${lowStudent.lop}
            </div>
        </div>

        <h4><i class="fas fa-chart-bar"></i> Phân tích kết quả học tập</h4>
        <div class="stats-grid">
            <div class="stat-card bg-success">
                <div class="stat-number">${passed}</div>
                <div class="stat-label">Học sinh đạt (≥5.0)</div>
            </div>
            <div class="stat-card bg-danger">
                <div class="stat-number">${failed}</div>
                <div class="stat-label">Học sinh không đạt (<5.0)</div>
            </div>
        </div>
    `;

    document.getElementById('statisticsContent').innerHTML = statsContent;
    document.getElementById('statisticsModal').style.display = 'block';
}

// Đóng modal
function closeModal() {
    document.getElementById('statisticsModal').style.display = 'none';
}

// Đóng modal khi click bên ngoài
window.onclick = function(event) {
    const modal = document.getElementById('statisticsModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}

// Hiển thị thông báo
function showNotification(message, type = 'info') {
    // Xóa thông báo cũ nếu có
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // Thêm CSS cho notification nếu chưa có
    if (!document.querySelector('#notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1001;
                min-width: 300px;
                max-width: 500px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                animation: slideInRight 0.3s ease-out;
            }

            .notification-content {
                padding: 1rem 1.5rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                color: white;
                font-weight: 500;
            }

            .notification-success { background: linear-gradient(135deg, #28a745, #20c997); }
            .notification-error { background: linear-gradient(135deg, #dc3545, #e74c3c); }
            .notification-warning { background: linear-gradient(135deg, #ffc107, #f39c12); color: #212529; }
            .notification-info { background: linear-gradient(135deg, #17a2b8, #3498db); }

            .notification-close {
                background: none;
                border: none;
                color: inherit;
                cursor: pointer;
                margin-left: auto;
                padding: 0.25rem;
                border-radius: 4px;
                transition: background-color 0.2s;
            }

            .notification-close:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }

            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // Tự động xóa sau 5 giây
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOutRight 0.3s ease-in forwards';
            setTimeout(() => notification.remove(), 300);
        }
    }, 5000);
}

// Lấy icon cho notification
function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'fa-check-circle';
        case 'error': return 'fa-exclamation-circle';
        case 'warning': return 'fa-exclamation-triangle';
        case 'info': return 'fa-info-circle';
        default: return 'fa-info-circle';
    }
}

// Reset tất cả filters
function resetFilters() {
    document.getElementById('searchMaSo').value = '';
    document.getElementById('filterKhoi').value = '';
    document.getElementById('filterDiem').value = '';
    document.getElementById('filterOperator').value = 'gte';
    currentFilter = null;
    displayStudents();
    updateVisualization();
    showNotification('Đã reset tất cả bộ lọc!', 'info');
}

// Export dữ liệu ra CSV
function exportToCSV() {
    const students = studentList.toArray();
    if (students.length === 0) {
        showNotification('Không có dữ liệu để xuất!', 'warning');
        return;
    }

    const headers = ['Mã số', 'Họ tên', 'Năm sinh', 'Khối', 'Lớp', 'Điểm TB'];
    const csvContent = [
        headers.join(','),
        ...students.map(s => [
            s.maSo,
            `"${s.hoTen}"`,
            s.namSinh,
            s.khoi,
            s.lop,
            s.diemTB
        ].join(','))
    ].join('\n');

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'danh_sach_hoc_sinh.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showNotification('Đã xuất dữ liệu ra file CSV!', 'success');
}

// Thêm các phím tắt
document.addEventListener('keydown', function(e) {
    // Ctrl + S: Thêm học sinh
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        document.getElementById('maSo').focus();
    }

    // Ctrl + F: Tìm kiếm
    if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        document.getElementById('searchMaSo').focus();
    }

    // Escape: Đóng modal
    if (e.key === 'Escape') {
        closeModal();
    }
});

// Thêm animation khi load trang
window.addEventListener('load', function() {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease-in';
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// Thêm CSS cho badge
const badgeStyles = document.createElement('style');
badgeStyles.textContent = `
    .badge {
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .badge-khoi-10 {
        background-color: #e3f2fd;
        color: #1976d2;
    }

    .badge-khoi-11 {
        background-color: #f3e5f5;
        color: #7b1fa2;
    }

    .badge-khoi-12 {
        background-color: #fff3e0;
        color: #f57c00;
    }

    .highlight-card {
        padding: 1rem;
        border-radius: 8px;
        margin: 0.5rem 0;
        border-left: 4px solid #667eea;
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(badgeStyles);
