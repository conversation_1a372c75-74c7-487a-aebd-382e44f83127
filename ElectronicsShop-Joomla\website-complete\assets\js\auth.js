// Authentication JavaScript

// Demo users database
const demoUsers = [
    {
        id: 1,
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'Admin',
        lastName: 'Electronics Shop',
        phone: '0123456789',
        role: 'admin',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'
    },
    {
        id: 2,
        email: '<EMAIL>',
        password: 'user123',
        firstName: 'Nguyễn',
        lastName: 'Văn A',
        phone: '0987654321',
        role: 'user',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face'
    }
];

// Initialize auth page
document.addEventListener('DOMContentLoaded', function() {
    initializeAuthPage();
});

function initializeAuthPage() {
    // Initialize AOS
    AOS.init({
        duration: 1000,
        once: true
    });
    
    // Add password strength checker if on register page
    const passwordInput = document.getElementById('password');
    if (passwordInput) {
        passwordInput.addEventListener('input', checkPasswordStrength);
    }
    
    // Add confirm password validation
    const confirmPasswordInput = document.getElementById('confirmPassword');
    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', validatePasswordMatch);
    }
    
    // Add real-time email validation
    const emailInputs = document.querySelectorAll('input[type="email"]');
    emailInputs.forEach(input => {
        input.addEventListener('blur', validateEmail);
    });
    
    // Add phone number formatting
    const phoneInput = document.getElementById('phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', formatPhoneNumber);
    }
    
    console.log('Auth page initialized');
}

// Login handler
function handleLogin(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const email = formData.get('email').trim();
    const password = formData.get('password');
    const rememberMe = formData.get('rememberMe') === 'on';
    
    // Validate inputs
    if (!validateLoginForm(email, password)) {
        return;
    }
    
    // Show loading state
    setButtonLoading('loginBtn', true);
    
    // Simulate API call
    setTimeout(() => {
        const user = authenticateUser(email, password);
        
        if (user) {
            // Login successful
            loginUser(user, rememberMe);
            showNotification('Đăng nhập thành công!', 'success');
            
            // Redirect after short delay
            setTimeout(() => {
                const returnUrl = new URLSearchParams(window.location.search).get('return') || 'index.html';
                window.location.href = returnUrl;
            }, 1000);
        } else {
            // Login failed
            showNotification('Email hoặc mật khẩu không đúng!', 'error');
            shakeForm(form);
        }
        
        setButtonLoading('loginBtn', false);
    }, 1500);
}

// Register handler
function handleRegister(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    
    const userData = {
        firstName: formData.get('firstName').trim(),
        lastName: formData.get('lastName').trim(),
        email: formData.get('email').trim(),
        phone: formData.get('phone').trim(),
        password: formData.get('password'),
        confirmPassword: formData.get('confirmPassword'),
        birthDate: formData.get('birthDate'),
        gender: formData.get('gender'),
        agreeTerms: formData.get('agreeTerms') === 'on',
        subscribeNewsletter: formData.get('subscribeNewsletter') === 'on'
    };
    
    // Validate inputs
    if (!validateRegisterForm(userData)) {
        return;
    }
    
    // Show loading state
    setButtonLoading('registerBtn', true);
    
    // Simulate API call
    setTimeout(() => {
        // Check if email already exists
        const existingUser = demoUsers.find(user => user.email === userData.email);
        
        if (existingUser) {
            showNotification('Email đã được sử dụng!', 'error');
            setFieldError('email', 'Email này đã được đăng ký');
            shakeForm(form);
        } else {
            // Registration successful
            const newUser = {
                id: demoUsers.length + 1,
                email: userData.email,
                password: userData.password,
                firstName: userData.firstName,
                lastName: userData.lastName,
                phone: userData.phone,
                role: 'user',
                avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face'
            };
            
            demoUsers.push(newUser);
            
            showNotification('Đăng ký thành công! Đang chuyển đến trang đăng nhập...', 'success');
            
            // Redirect to login page
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 2000);
        }
        
        setButtonLoading('registerBtn', false);
    }, 2000);
}

// Validation functions
function validateLoginForm(email, password) {
    let isValid = true;
    
    // Clear previous errors
    clearFieldErrors();
    
    // Validate email
    if (!email) {
        setFieldError('loginEmail', 'Vui lòng nhập email hoặc số điện thoại');
        isValid = false;
    }
    
    // Validate password
    if (!password) {
        setFieldError('loginPassword', 'Vui lòng nhập mật khẩu');
        isValid = false;
    }
    
    return isValid;
}

function validateRegisterForm(userData) {
    let isValid = true;
    
    // Clear previous errors
    clearFieldErrors();
    
    // Validate first name
    if (!userData.firstName) {
        setFieldError('firstName', 'Vui lòng nhập họ');
        isValid = false;
    }
    
    // Validate last name
    if (!userData.lastName) {
        setFieldError('lastName', 'Vui lòng nhập tên');
        isValid = false;
    }
    
    // Validate email
    if (!userData.email) {
        setFieldError('email', 'Vui lòng nhập email');
        isValid = false;
    } else if (!isValidEmail(userData.email)) {
        setFieldError('email', 'Email không hợp lệ');
        isValid = false;
    }
    
    // Validate phone
    if (!userData.phone) {
        setFieldError('phone', 'Vui lòng nhập số điện thoại');
        isValid = false;
    } else if (!isValidPhone(userData.phone)) {
        setFieldError('phone', 'Số điện thoại không hợp lệ');
        isValid = false;
    }
    
    // Validate password
    if (!userData.password) {
        setFieldError('password', 'Vui lòng nhập mật khẩu');
        isValid = false;
    } else if (userData.password.length < 6) {
        setFieldError('password', 'Mật khẩu phải có ít nhất 6 ký tự');
        isValid = false;
    }
    
    // Validate confirm password
    if (userData.password !== userData.confirmPassword) {
        setFieldError('confirmPassword', 'Mật khẩu xác nhận không khớp');
        isValid = false;
    }
    
    // Validate terms agreement
    if (!userData.agreeTerms) {
        showNotification('Vui lòng đồng ý với điều khoản sử dụng', 'warning');
        isValid = false;
    }
    
    return isValid;
}

// Helper functions
function authenticateUser(email, password) {
    return demoUsers.find(user => 
        (user.email === email || user.phone === email) && user.password === password
    );
}

function loginUser(user, rememberMe) {
    const userData = {
        id: user.id,
        email: user.email,
        name: `${user.firstName} ${user.lastName}`,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        role: user.role,
        avatar: user.avatar
    };
    
    // Store user data
    localStorage.setItem('user', JSON.stringify(userData));
    
    if (rememberMe) {
        localStorage.setItem('rememberLogin', 'true');
    }
    
    // Update global user variable
    window.user = userData;
}

function setFieldError(fieldId, message) {
    const field = document.getElementById(fieldId);
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    
    field.classList.add('is-invalid');
    field.classList.remove('is-valid');
    
    if (feedback) {
        feedback.textContent = message;
    }
}

function clearFieldErrors() {
    const fields = document.querySelectorAll('.form-control');
    const feedbacks = document.querySelectorAll('.invalid-feedback');
    
    fields.forEach(field => {
        field.classList.remove('is-invalid', 'is-valid');
    });
    
    feedbacks.forEach(feedback => {
        feedback.textContent = '';
    });
}

function setButtonLoading(buttonId, loading) {
    const button = document.getElementById(buttonId);
    const btnText = button.querySelector('.btn-text');
    const btnLoading = button.querySelector('.btn-loading');
    
    if (loading) {
        button.disabled = true;
        button.classList.add('loading');
        btnText.style.display = 'none';
        btnLoading.style.display = 'inline-block';
    } else {
        button.disabled = false;
        button.classList.remove('loading');
        btnText.style.display = 'inline-block';
        btnLoading.style.display = 'none';
    }
}

function shakeForm(form) {
    form.classList.add('shake');
    setTimeout(() => {
        form.classList.remove('shake');
    }, 500);
}

// Password strength checker
function checkPasswordStrength() {
    const password = this.value;
    const strengthDiv = document.getElementById('passwordStrength');
    
    if (!strengthDiv) return;
    
    let strength = 0;
    let feedback = '';
    
    if (password.length >= 6) strength++;
    if (password.match(/[a-z]/)) strength++;
    if (password.match(/[A-Z]/)) strength++;
    if (password.match(/[0-9]/)) strength++;
    if (password.match(/[^a-zA-Z0-9]/)) strength++;
    
    switch (strength) {
        case 0:
        case 1:
            feedback = '<span class="strength-weak">Yếu</span>';
            break;
        case 2:
        case 3:
            feedback = '<span class="strength-medium">Trung bình</span>';
            break;
        case 4:
        case 5:
            feedback = '<span class="strength-strong">Mạnh</span>';
            break;
    }
    
    strengthDiv.innerHTML = feedback;
}

function validatePasswordMatch() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    const feedback = this.parentNode.parentNode.querySelector('.invalid-feedback');
    
    if (confirmPassword && password !== confirmPassword) {
        this.classList.add('is-invalid');
        this.classList.remove('is-valid');
        feedback.textContent = 'Mật khẩu xác nhận không khớp';
    } else if (confirmPassword) {
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
        feedback.textContent = '';
    }
}

function validateEmail() {
    const email = this.value.trim();
    const feedback = this.parentNode.querySelector('.invalid-feedback');
    
    if (email && !isValidEmail(email)) {
        this.classList.add('is-invalid');
        this.classList.remove('is-valid');
        feedback.textContent = 'Email không hợp lệ';
    } else if (email) {
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
        feedback.textContent = '';
    }
}

function formatPhoneNumber() {
    let value = this.value.replace(/\D/g, '');
    
    if (value.length > 10) {
        value = value.substring(0, 10);
    }
    
    if (value.length >= 7) {
        value = value.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3');
    } else if (value.length >= 4) {
        value = value.replace(/(\d{4})(\d{3})/, '$1 $2');
    }
    
    this.value = value;
}

// Utility functions
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^[0-9]{10}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
}

// Password toggle
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const toggle = input.parentNode.querySelector('.password-toggle i');
    
    if (input.type === 'password') {
        input.type = 'text';
        toggle.classList.remove('fa-eye');
        toggle.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        toggle.classList.remove('fa-eye-slash');
        toggle.classList.add('fa-eye');
    }
}

// Demo account fillers
function fillDemoAccount(type) {
    if (type === 'admin') {
        document.getElementById('loginEmail').value = '<EMAIL>';
        document.getElementById('loginPassword').value = 'admin123';
    } else if (type === 'user') {
        document.getElementById('loginEmail').value = '<EMAIL>';
        document.getElementById('loginPassword').value = 'user123';
    }
}

// Social login functions (demo)
function loginWithFacebook() {
    showNotification('Tính năng đăng nhập Facebook đang được phát triển', 'info');
}

function loginWithGoogle() {
    showNotification('Tính năng đăng nhập Google đang được phát triển', 'info');
}

function registerWithFacebook() {
    showNotification('Tính năng đăng ký Facebook đang được phát triển', 'info');
}

function registerWithGoogle() {
    showNotification('Tính năng đăng ký Google đang được phát triển', 'info');
}

// Export functions for global access
window.handleLogin = handleLogin;
window.handleRegister = handleRegister;
window.togglePassword = togglePassword;
window.fillDemoAccount = fillDemoAccount;
window.loginWithFacebook = loginWithFacebook;
window.loginWithGoogle = loginWithGoogle;
window.registerWithFacebook = registerWithFacebook;
window.registerWithGoogle = registerWithGoogle;
