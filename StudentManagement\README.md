# Chương Trình Quản Lý Sinh Viên Sử Dụng Danh Sách Liên Kết

## <PERSON><PERSON> Tả Dự Án
Đây là một chương trình quản lý sinh viên được xây dựng bằng C# sử dụng cấu trúc dữ liệu danh sách liên kết đơn và danh sách liên kết kép. Chương trình cung cấp các chức năng cơ bản để quản lý thông tin sinh viên trong một hệ thống giáo dục.

## Tính Năng Chính

### 1. Quản Lý Sinh Viên
- ✅ Thêm sinh viên mới
- ✅ Xóa sinh viên theo mã
- ✅ Tìm kiếm sinh viên theo mã
- ✅ Tìm kiếm sinh viên theo tên
- ✅ Tìm kiếm sinh viên theo lớp
- ✅ Hi<PERSON><PERSON> thị tất cả sinh viên
- ✅ Sắp xếp sinh viên theo điểm trung bình

### 2. <PERSON><PERSON><PERSON> Trúc Dữ Liệu
- ✅ **Danh sách liên kết đơn (Single Linked List)**
- ✅ **Danh sách liên kết kép (Doubly Linked List)**
- ✅ Chuyển đổi giữa hai loại danh sách trong runtime

### 3. Thống Kê và Báo Cáo
- ✅ Thống kê tổng quan (số lượng sinh viên, điểm trung bình, v.v.)
- ✅ Tìm sinh viên có điểm cao nhất
- ✅ Phân loại theo giới tính
- ✅ Hiển thị danh sách theo thứ tự ngược

### 4. Giao Diện Người Dùng
- ✅ Giao diện console thân thiện với người dùng
- ✅ Menu điều hướng rõ ràng
- ✅ Hiển thị thông tin đẹp mắt với khung viền ASCII
- ✅ Xử lý lỗi và validation dữ liệu

## Cấu Trúc Dự Án

```
StudentManagement/
├── Student.cs              # Lớp đại diện cho sinh viên
├── StudentNode.cs          # Node cho danh sách liên kết đơn và kép
├── SingleLinkedList.cs     # Cài đặt danh sách liên kết đơn
├── DoublyLinkedList.cs     # Cài đặt danh sách liên kết kép
├── StudentManager.cs       # Lớp quản lý sinh viên
├── Program.cs              # Chương trình chính và giao diện
├── StudentManagement.csproj # File cấu hình dự án
└── README.md               # Tài liệu hướng dẫn
```

## Thông Tin Sinh Viên

Mỗi sinh viên bao gồm các thông tin sau:
- **Mã sinh viên**: Mã định danh duy nhất
- **Họ và tên**: Tên đầy đủ của sinh viên
- **Ngày sinh**: Ngày tháng năm sinh
- **Giới tính**: Nam hoặc Nữ
- **Lớp**: Lớp học của sinh viên
- **Điểm trung bình**: Điểm GPA (0.0 - 10.0)
- **Email**: Địa chỉ email
- **Số điện thoại**: Số điện thoại liên lạc

## Hướng Dẫn Sử Dụng

### 1. Biên Dịch và Chạy
```bash
# Di chuyển vào thư mục dự án
cd StudentManagement

# Biên dịch dự án
dotnet build

# Chạy chương trình
dotnet run
```

### 2. Sử Dụng Chương Trình

#### Menu Chính:
1. **Thêm sinh viên mới** - Nhập thông tin sinh viên mới
2. **Hiển thị tất cả sinh viên** - Xem danh sách toàn bộ sinh viên
3. **Tìm sinh viên theo mã** - Tìm kiếm bằng mã sinh viên
4. **Tìm sinh viên theo tên** - Tìm kiếm bằng tên (hỗ trợ tìm kiếm một phần)
5. **Tìm sinh viên theo lớp** - Tìm kiếm theo lớp học
6. **Xóa sinh viên** - Xóa sinh viên khỏi danh sách
7. **Sắp xếp sinh viên theo điểm** - Sắp xếp theo điểm trung bình
8. **Xem thống kê** - Hiển thị thống kê tổng quan
9. **Xem sinh viên có điểm cao nhất** - Tìm sinh viên xuất sắc nhất
10. **Chuyển đổi loại danh sách** - Chuyển giữa danh sách đơn và kép
11. **Hiển thị sinh viên theo thứ tự ngược** - Xem danh sách đảo ngược
12. **Xóa tất cả sinh viên** - Xóa toàn bộ dữ liệu
0. **Thoát chương trình**

### 3. Dữ Liệu Mẫu
Chương trình tự động tạo 5 sinh viên mẫu khi khởi động:
- SV001 - Nguyễn Văn An (CNTT01, ĐTB: 8.5)
- SV002 - Trần Thị Bình (CNTT01, ĐTB: 9.2)
- SV003 - Lê Văn Cường (CNTT02, ĐTB: 7.8)
- SV004 - Phạm Thị Dung (CNTT02, ĐTB: 8.9)
- SV005 - Hoàng Văn Em (CNTT03, ĐTB: 6.5)

## Đặc Điểm Kỹ Thuật

### Danh Sách Liên Kết Đơn (Single Linked List)
- **Ưu điểm**: Tiết kiệm bộ nhớ, đơn giản
- **Nhược điểm**: Chỉ duyệt một chiều
- **Thao tác**: O(1) cho thêm đầu, O(n) cho tìm kiếm và xóa

### Danh Sách Liên Kết Kép (Doubly Linked List)
- **Ưu điểm**: Duyệt hai chiều, xóa hiệu quả hơn
- **Nhược điểm**: Tốn bộ nhớ hơn
- **Thao tác**: O(1) cho thêm đầu/cuối, O(n) cho tìm kiếm

### Thuật Toán Sắp Xếp
- Sử dụng **Bubble Sort** để sắp xếp theo điểm trung bình
- Độ phức tạp: O(n²)
- Phù hợp cho dữ liệu nhỏ và trung bình

## Yêu Cầu Hệ Thống
- **.NET 6.0** hoặc cao hơn
- **Windows/Linux/macOS** (đa nền tảng)
- **Console/Terminal** hỗ trợ UTF-8

## Mở Rộng Tương Lai
- [ ] Lưu trữ dữ liệu vào file
- [ ] Import/Export dữ liệu Excel
- [ ] Giao diện đồ họa (WPF/WinForms)
- [ ] API REST cho ứng dụng web
- [ ] Cơ sở dữ liệu SQL Server
- [ ] Báo cáo và biểu đồ thống kê

## Tác Giả
Dự án được phát triển như một bài tập thực hành về cấu trúc dữ liệu và giải thuật, tập trung vào việc cài đặt và sử dụng danh sách liên kết trong quản lý dữ liệu thực tế.

## Giấy Phép
Dự án này được phát hành dưới giấy phép MIT - xem file LICENSE để biết thêm chi tiết.
