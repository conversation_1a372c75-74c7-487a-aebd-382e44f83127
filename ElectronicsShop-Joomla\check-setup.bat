@echo off
echo ========================================
echo    Electronics Shop Setup Checker
echo ========================================
echo.

echo Checking XAMPP installation...
if exist "C:\xampp\xampp-control.exe" (
    echo [OK] XAMPP is installed
) else (
    echo [ERROR] XAMPP not found
    echo Please install XAMPP from: https://www.apachefriends.org/
    goto :end
)

echo.
echo Checking XAMPP services...
tasklist /FI "IMAGENAME eq httpd.exe" 2>NUL | find /I /N "httpd.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [OK] Apache is running
) else (
    echo [WARNING] Apache is not running
    echo Please start Apache in XAMPP Control Panel
)

tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [OK] MySQL is running
) else (
    echo [WARNING] MySQL is not running
    echo Please start MySQL in XAMPP Control Panel
)

echo.
echo Checking Joomla installation...
if exist "C:\xampp\htdocs\electronics-shop\index.php" (
    echo [OK] Joomla files found
) else (
    echo [ERROR] Joomla not found
    echo Please extract Joomla to: C:\xampp\htdocs\electronics-shop\
)

echo.
echo Checking database...
if exist "C:\xampp\mysql\bin\mysql.exe" (
    echo [OK] MySQL client found
    echo Please check if database 'electronics_shop' exists in phpMyAdmin
) else (
    echo [WARNING] MySQL client not found
)

echo.
echo ========================================
echo    Test URLs
echo ========================================
echo XAMPP Dashboard: http://localhost
echo phpMyAdmin: http://localhost/phpmyadmin
echo Electronics Shop: http://localhost/electronics-shop
echo Joomla Admin: http://localhost/electronics-shop/administrator
echo.

echo ========================================
echo    Next Steps
echo ========================================
echo 1. Make sure XAMPP services are running (green in Control Panel)
echo 2. Create database 'electronics_shop' in phpMyAdmin
echo 3. Install Joomla at: http://localhost/electronics-shop
echo 4. Follow the setup wizard
echo.

:end
pause
