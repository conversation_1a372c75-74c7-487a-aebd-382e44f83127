#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chương trình demo thuật toán tìm đường đi ngắn nhất trên đồ thị có trọng số
Tác giả: Sinh viên
Ngày: 2025
"""

import heapq
import sys
from collections import defaultdict, deque
import matplotlib.pyplot as plt
import networkx as nx

class Graph:
    """Lớp biểu diễn đồ thị có trọng số"""
    
    def __init__(self):
        self.vertices = set()
        self.edges = defaultdict(list)
        
    def add_edge(self, u, v, weight):
        """Thêm cạnh có trọng số vào đồ thị có hướng"""
        self.vertices.add(u)
        self.vertices.add(v)
        self.edges[u].append((v, weight))
        
    def add_undirected_edge(self, u, v, weight):
        """Thêm cạnh vô hướng (thêm cả hai chiều)"""
        self.add_edge(u, v, weight)
        self.add_edge(v, u, weight)
    
    def dijkstra(self, start, end=None):
        """
        Thuật toán Dijkstra tìm đường đi ngắn nhất
        Args:
            start: Đỉnh bắt đầu
            end: Đỉnh kết thúc (None nếu tìm đến tất cả đỉnh)
        Returns:
            distances: Dictionary chứa khoảng cách ngắn nhất
            previous: Dictionary chứa đỉnh trước đó trong đường đi ngắn nhất
        """
        # Khởi tạo khoảng cách và đỉnh trước đó
        distances = {vertex: float('infinity') for vertex in self.vertices}
        previous = {vertex: None for vertex in self.vertices}
        distances[start] = 0
        
        # Priority queue: (distance, vertex)
        pq = [(0, start)]
        visited = set()
        
        while pq:
            current_distance, current_vertex = heapq.heappop(pq)
            
            # Nếu đã thăm đỉnh này thì bỏ qua
            if current_vertex in visited:
                continue
                
            visited.add(current_vertex)
            
            # Nếu tìm được đỉnh đích thì dừng (tối ưu hóa)
            if end and current_vertex == end:
                break
                
            # Duyệt các đỉnh kề
            for neighbor, weight in self.edges[current_vertex]:
                if neighbor not in visited:
                    new_distance = current_distance + weight
                    
                    # Relaxation: cập nhật nếu tìm được đường đi ngắn hơn
                    if new_distance < distances[neighbor]:
                        distances[neighbor] = new_distance
                        previous[neighbor] = current_vertex
                        heapq.heappush(pq, (new_distance, neighbor))
        
        return distances, previous
    
    def bellman_ford(self, start):
        """
        Thuật toán Bellman-Ford (xử lý được trọng số âm)
        Returns:
            distances: Dictionary chứa khoảng cách ngắn nhất
            previous: Dictionary chứa đỉnh trước đó
            has_negative_cycle: Boolean cho biết có chu trình âm hay không
        """
        # Khởi tạo
        distances = {vertex: float('infinity') for vertex in self.vertices}
        previous = {vertex: None for vertex in self.vertices}
        distances[start] = 0
        
        # Relaxation V-1 lần
        for _ in range(len(self.vertices) - 1):
            for u in self.vertices:
                for v, weight in self.edges[u]:
                    if distances[u] != float('infinity') and distances[u] + weight < distances[v]:
                        distances[v] = distances[u] + weight
                        previous[v] = u
        
        # Kiểm tra chu trình âm
        has_negative_cycle = False
        for u in self.vertices:
            for v, weight in self.edges[u]:
                if distances[u] != float('infinity') and distances[u] + weight < distances[v]:
                    has_negative_cycle = True
                    break
            if has_negative_cycle:
                break
        
        return distances, previous, has_negative_cycle
    
    def get_path(self, previous, start, end):
        """
        Tái tạo đường đi từ start đến end dựa trên mảng previous
        """
        path = []
        current = end
        
        while current is not None:
            path.append(current)
            current = previous[current]
        
        path.reverse()
        
        # Kiểm tra xem có đường đi hay không
        if path[0] != start:
            return None
        
        return path
    
    def print_graph(self):
        """In thông tin đồ thị"""
        print("Thông tin đồ thị:")
        print(f"Số đỉnh: {len(self.vertices)}")
        print(f"Các đỉnh: {sorted(list(self.vertices))}")
        print("Các cạnh:")
        for u in sorted(self.vertices):
            for v, weight in self.edges[u]:
                print(f"  {u} -> {v}: {weight}")

class ShortestPathVisualizer:
    """Lớp để trực quan hóa thuật toán tìm đường đi ngắn nhất"""
    
    def __init__(self):
        self.graph = Graph()
        
    def create_sample_graph(self):
        """Tạo đồ thị mẫu để demo"""
        # Thêm các cạnh vào đồ thị
        edges = [
            ('A', 'B', 4), ('A', 'C', 2),
            ('B', 'C', 1), ('B', 'D', 5),
            ('C', 'D', 8), ('C', 'E', 10),
            ('D', 'E', 2), ('D', 'F', 6),
            ('E', 'F', 3)
        ]
        
        for u, v, w in edges:
            self.graph.add_undirected_edge(u, v, w)
    
    def create_negative_weight_graph(self):
        """Tạo đồ thị có trọng số âm để test Bellman-Ford"""
        self.graph = Graph()  # Reset graph
        edges = [
            ('A', 'B', 1), ('A', 'C', 4),
            ('B', 'C', -3), ('B', 'D', 2),
            ('C', 'D', 3), ('D', 'B', -5)
        ]
        
        for u, v, w in edges:
            self.graph.add_edge(u, v, w)
    
    def visualize_graph(self, path=None, title="Đồ thị"):
        """Vẽ đồ thị bằng matplotlib và networkx"""
        try:
            G = nx.Graph()
            
            # Thêm các cạnh vào NetworkX graph
            for u in self.graph.vertices:
                for v, weight in self.graph.edges[u]:
                    if not G.has_edge(u, v):  # Tránh thêm cạnh trùng lặp cho đồ thị vô hướng
                        G.add_edge(u, v, weight=weight)
            
            plt.figure(figsize=(12, 8))
            pos = nx.spring_layout(G, seed=42)
            
            # Vẽ tất cả các cạnh
            nx.draw_networkx_edges(G, pos, alpha=0.5, width=1, edge_color='gray')
            
            # Vẽ đường đi ngắn nhất nếu có
            if path and len(path) > 1:
                path_edges = [(path[i], path[i+1]) for i in range(len(path)-1)]
                nx.draw_networkx_edges(G, pos, edgelist=path_edges, 
                                     edge_color='red', width=3, alpha=0.8)
            
            # Vẽ các đỉnh
            nx.draw_networkx_nodes(G, pos, node_color='lightblue', 
                                  node_size=1000, alpha=0.9)
            
            # Vẽ nhãn đỉnh
            nx.draw_networkx_labels(G, pos, font_size=16, font_weight='bold')
            
            # Vẽ trọng số cạnh
            edge_labels = nx.get_edge_attributes(G, 'weight')
            nx.draw_networkx_edge_labels(G, pos, edge_labels, font_size=12)
            
            plt.title(title, fontsize=16, fontweight='bold', pad=20)
            plt.axis('off')
            plt.tight_layout()
            plt.show()
            
        except ImportError:
            print("Không thể vẽ đồ thị. Vui lòng cài đặt matplotlib và networkx:")
            print("pip install matplotlib networkx")
        except Exception as e:
            print(f"Lỗi khi vẽ đồ thị: {e}")
    
    def run_demo(self):
        """Chạy demo chương trình"""
        print("=" * 60)
        print("    DEMO THUẬT TOÁN TÌM ĐƯỜNG ĐI NGẮN NHẤT")
        print("=" * 60)
        
        # Tạo đồ thị mẫu
        self.create_sample_graph()
        
        print("\n📊 Đồ thị mẫu đã được tạo!")
        print(f"Các đỉnh: {sorted(list(self.graph.vertices))}")
        print("\nCác cạnh trong đồ thị:")
        edges_shown = set()
        for u in sorted(self.graph.vertices):
            for v, weight in self.graph.edges[u]:
                edge = tuple(sorted([u, v]))
                if edge not in edges_shown:
                    print(f"  {edge[0]} ↔ {edge[1]}: {weight}")
                    edges_shown.add(edge)
        
        # Hiển thị đồ thị
        self.visualize_graph(title="Đồ thị ban đầu")
        
        # Nhập đỉnh bắt đầu và kết thúc
        print("\n" + "─" * 40)
        while True:
            start = input("🚀 Nhập đỉnh bắt đầu: ").strip().upper()
            if start in self.graph.vertices:
                break
            print(f"❌ Đỉnh '{start}' không tồn tại. Vui lòng nhập lại.")
        
        while True:
            end = input("🎯 Nhập đỉnh kết thúc: ").strip().upper()
            if end in self.graph.vertices:
                break
            print(f"❌ Đỉnh '{end}' không tồn tại. Vui lòng nhập lại.")
        
        # Chạy thuật toán Dijkstra
        print(f"\n🔍 THUẬT TOÁN DIJKSTRA")
        print("─" * 30)
        distances, previous = self.graph.dijkstra(start, end)
        path = self.graph.get_path(previous, start, end)
        
        if path:
            print(f"✅ Đường đi ngắn nhất từ {start} đến {end}:")
            print(f"   {' → '.join(path)}")
            print(f"📏 Độ dài đường đi: {distances[end]}")
            
            # Hiển thị đồ thị với đường đi ngắn nhất
            self.visualize_graph(path, f"Đường đi ngắn nhất từ {start} đến {end} (Dijkstra)")
        else:
            print(f"❌ Không có đường đi từ {start} đến {end}")
        
        # So sánh với Bellman-Ford
        print(f"\n🔍 THUẬT TOÁN BELLMAN-FORD")
        print("─" * 30)
        bf_distances, bf_previous, has_negative_cycle = self.graph.bellman_ford(start)
        
        if has_negative_cycle:
            print("⚠️  Phát hiện chu trình âm trong đồ thị!")
        else:
            bf_path = self.graph.get_path(bf_previous, start, end)
            if bf_path:
                print(f"✅ Đường đi ngắn nhất từ {start} đến {end}:")
                print(f"   {' → '.join(bf_path)}")
                print(f"📏 Độ dài đường đi: {bf_distances[end]}")
                
                # So sánh kết quả
                if abs(distances[end] - bf_distances[end]) < 1e-9:
                    print("✅ Kết quả của hai thuật toán giống nhau")
                else:
                    print("⚠️  Kết quả của hai thuật toán khác nhau")
            else:
                print(f"❌ Không có đường đi từ {start} đến {end}")
        
        print("\n" + "=" * 60)
        print("Cảm ơn bạn đã sử dụng chương trình!")
        print("=" * 60)

def test_negative_cycle():
    """Test case cho chu trình âm"""
    print("\n🧪 TEST CASE: CHU TRÌNH ÂM")
    print("─" * 30)
    
    graph = Graph()
    # Tạo đồ thị có chu trình âm
    edges = [('A', 'B', 1), ('B', 'C', -3), ('C', 'A', 1)]
    for u, v, w in edges:
        graph.add_edge(u, v, w)
    
    distances, previous, has_negative_cycle = graph.bellman_ford('A')
    
    if has_negative_cycle:
        print("✅ Phát hiện chu trình âm thành công!")
    else:
        print("❌ Không phát hiện được chu trình âm")

if __name__ == "__main__":
    # Chạy demo chính
    visualizer = ShortestPathVisualizer()
    visualizer.run_demo()
    
    # Chạy test case chu trình âm
    test_negative_cycle()
