# Ứng Dụng Danh Sách Liên Kết Đơn Để Quản Lý Dữ Liệu Học Sinh

## Mô tả dự án

Đây là một đồ án về cấu trúc dữ liệu và gi<PERSON>i thuật, sử dụng **Danh Sách Liên Kết Đơn (Singly Linked List)** để xây dựng ứng dụng quản lý thông tin học sinh. Dự án được viết bằng ngôn ngữ **C/C++** và bao gồm đầy đủ các chức năng quản lý cơ bản và nâng cao.

## Tính năng chính

### 🔧 Quản lý cơ bản
- ✅ Thêm học sinh mới
- ✅ Xóa học sinh theo mã số
- ✅ Cập nhật thông tin học sinh
- ✅ Hiển thị danh sách học sinh

### 🔍 Tìm kiếm và lọc dữ liệu
- ✅ Tìm kiếm học sinh theo mã số
- ✅ <PERSON><PERSON><PERSON> h<PERSON>c sinh theo khối (10, 11, 12)
- ✅ <PERSON><PERSON><PERSON> học sinh theo lớp
- ✅ <PERSON>ọc học sinh theo điểm trung bình

### 📊 Thống kê và báo cáo
- ✅ Thống kê số lượng học sinh theo khối
- ✅ Tìm học sinh có điểm cao nhất/thấp nhất
- ✅ Thống kê tỉ lệ đạt/không đạt yêu cầu

### 🔄 Sắp xếp dữ liệu
- ✅ Sắp xếp theo mã số học sinh
- ✅ Sắp xếp theo điểm trung bình (giảm dần)

## Cấu trúc dữ liệu

### Thông tin học sinh
```cpp
struct HocSinh {
    char MaSo[10];           // Mã số học sinh (VD: "HS001")
    char HoTen[50];          // Họ tên học sinh
    int NamSinh;             // Năm sinh
    int Khoi;                // Khối (10, 11, 12)
    char Lop[10];            // Lớp (VD: "A1", "B2")
    float DiemTrungBinh;     // Điểm trung bình
};
```

### Nút trong danh sách liên kết
```cpp
struct Node {
    HocSinh data;    // Dữ liệu học sinh
    Node* next;      // Con trỏ trỏ đến nút tiếp theo
};
```

## Hướng dẫn cài đặt và chạy

### Yêu cầu hệ thống
- **Compiler**: GCC/G++ hoặc Visual Studio
- **Hệ điều hành**: Windows, Linux, macOS
- **Ngôn ngữ**: C++11 trở lên

### Biên dịch và chạy

#### Trên Windows:
```bash
# Sử dụng GCC/MinGW
g++ -o QuanLyHocSinh QuanLyHocSinh.cpp
QuanLyHocSinh.exe

# Hoặc sử dụng Visual Studio
# Mở file .cpp trong Visual Studio và nhấn F5
```

#### Trên Linux/macOS:
```bash
# Biên dịch
g++ -o QuanLyHocSinh QuanLyHocSinh.cpp

# Chạy chương trình
./QuanLyHocSinh
```

## Hướng dẫn sử dụng

### Menu chính
```
========== QUẢN LÝ DANH SÁCH HỌC SINH ==========
1. Thêm học sinh mới
2. Hiển thị danh sách học sinh
3. Tìm kiếm học sinh
4. Xóa học sinh
5. Cập nhật thông tin học sinh
6. Sắp xếp danh sách
7. Thống kê dữ liệu
8. Trích lọc dữ liệu
0. Thoát chương trình
===============================================
```

### Dữ liệu mẫu để test
```
Mã số: HS001
Họ tên: Nguyễn Văn An
Năm sinh: 2005
Khối: 12
Lớp: A1
Điểm TB: 8.5

Mã số: HS002
Họ tên: Trần Thị Bình
Năm sinh: 2006
Khối: 11
Lớp: B2
Điểm TB: 7.2
```

## Cấu trúc thư mục

```
Ung-Dung-Danh-Sach-Lien-Ket-Don-Quan-Ly-Hoc-Sinh/
├── BaoCaoDoAn.md          # Báo cáo đồ án hoàn chỉnh
├── QuanLyHocSinh.cpp      # Mã nguồn chương trình
└── README.md              # Hướng dẫn sử dụng
```

## Độ phức tạp thuật toán

| Thao tác | Độ phức tạp | Ghi chú |
|----------|-------------|---------|
| Thêm vào đầu | O(1) | Hiệu quả nhất |
| Thêm vào cuối | O(n) | Phải duyệt đến cuối |
| Xóa ở đầu | O(1) | Hiệu quả nhất |
| Xóa ở giữa/cuối | O(n) | Phải tìm vị trí |
| Tìm kiếm | O(n) | Tìm kiếm tuần tự |
| Sắp xếp | O(n²) | Bubble Sort |
| Duyệt danh sách | O(n) | Phải duyệt tất cả |

## Ưu điểm của Danh Sách Liên Kết Đơn

✅ **Kích thước động**: Có thể thay đổi kích thước trong runtime
✅ **Tiết kiệm bộ nhớ**: Chỉ cấp phát khi cần thiết
✅ **Thêm/xóa linh hoạt**: Dễ dàng thêm/xóa ở bất kỳ vị trí nào
✅ **Không lãng phí**: Không cần khai báo trước kích thước tối đa

## Nhược điểm và hạn chế

❌ **Truy cập tuần tự**: Không thể truy cập ngẫu nhiên
❌ **Bộ nhớ phụ**: Cần thêm bộ nhớ cho con trỏ
❌ **Không cache-friendly**: Các nút có thể rải rác trong bộ nhớ
❌ **Phức tạp hơn mảng**: Cần quản lý con trỏ cẩn thận

## Hướng phát triển

### 🚀 Cải thiện hiệu suất
- Sử dụng Quick Sort/Merge Sort thay vì Bubble Sort
- Thêm con trỏ tail để thêm vào cuối với O(1)
- Cài đặt Hash Table cho tìm kiếm nhanh

### 📁 Mở rộng chức năng
- Lưu/đọc dữ liệu từ file (CSV, JSON)
- Validation dữ liệu đầu vào
- Undo/Redo operations
- Export báo cáo ra Excel/PDF

### 🎨 Cải thiện giao diện
- Phát triển GUI với Qt hoặc Windows Forms
- Web interface với HTML/CSS/JavaScript
- Mobile app

### 🔒 Bảo mật
- Đăng nhập và phân quyền
- Mã hóa dữ liệu
- Audit log
- Backup tự động

## Tác giả

**Đồ án môn**: Cấu trúc dữ liệu và Giải thuật
**Ngôn ngữ**: C/C++
**Cấu trúc dữ liệu**: Danh Sách Liên Kết Đơn (Singly Linked List)

## Giấy phép

Dự án này được tạo ra cho mục đích học tập và nghiên cứu.

---

📧 **Liên hệ**: Nếu có thắc mắc hoặc góp ý, vui lòng tạo issue hoặc liên hệ trực tiếp.

⭐ **Đánh giá**: Nếu dự án hữu ích, hãy cho một star để ủng hộ!
