using System;

namespace StudentManagement
{
    /// <summary>
    /// Lớp đại diện cho thông tin sinh viên
    /// </summary>
    public class Student
    {
        public string MaSinhVien { get; set; }
        public string HoTen { get; set; }
        public DateTime NgaySinh { get; set; }
        public string GioiTinh { get; set; }
        public string Lop { get; set; }
        public double DiemTrungBinh { get; set; }
        public string Email { get; set; }
        public string SoDienThoai { get; set; }

        public Student()
        {
        }

        public Student(string maSinhVien, string hoTen, DateTime ngaySinh, string gioiTinh, 
                      string lop, double diemTrungBinh, string email, string soDienThoai)
        {
            MaSinhVien = maSinhVien;
            HoTen = hoTen;
            NgaySinh = ngaySinh;
            GioiTinh = gioiTinh;
            Lop = lop;
            DiemTrungBinh = diemTrungBinh;
            Email = email;
            SoDienThoai = soDienThoai;
        }

        public override string ToString()
        {
            return $"Mã SV: {MaSinh<PERSON>ien,-10} | Họ tên: {HoTen,-25} | Ngày sinh: {NgaySinh:dd/MM/yyyy} | " +
                   $"Giới tính: {GioiTinh,-5} | Lớp: {Lop,-10} | ĐTB: {DiemTrungBinh:F2} | " +
                   $"Email: {Email,-25} | SĐT: {SoDienThoai}";
        }

        public string ToDetailString()
        {
            return $"╔══════════════════════════════════════════════════════════════════════════════════════╗\n" +
                   $"║                              THÔNG TIN SINH VIÊN                                    ║\n" +
                   $"╠══════════════════════════════════════════════════════════════════════════════════════╣\n" +
                   $"║ Mã sinh viên    : {MaSinhVien,-20}                                          ║\n" +
                   $"║ Họ và tên       : {HoTen,-20}                                          ║\n" +
                   $"║ Ngày sinh       : {NgaySinh:dd/MM/yyyy}                                                      ║\n" +
                   $"║ Giới tính       : {GioiTinh,-20}                                          ║\n" +
                   $"║ Lớp             : {Lop,-20}                                          ║\n" +
                   $"║ Điểm trung bình : {DiemTrungBinh:F2}                                                         ║\n" +
                   $"║ Email           : {Email,-20}                                          ║\n" +
                   $"║ Số điện thoại   : {SoDienThoai,-20}                                          ║\n" +
                   $"╚══════════════════════════════════════════════════════════════════════════════════════╝";
        }
    }
}
