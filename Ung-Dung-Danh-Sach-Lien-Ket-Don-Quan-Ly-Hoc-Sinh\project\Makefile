# Makefile cho chương trình Quản Lý <PERSON>ọ<PERSON>
# Sử dụng cấu trúc dữ liệu Danh Sách Liên Kết <PERSON>ơn

# Compiler và flags
CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra -O2
TARGET = QuanLyHocSinh
SOURCE = QuanLyHocSinh.cpp

# Phát hiện hệ điều hành
ifeq ($(OS),Windows_NT)
    TARGET_EXT = .exe
    RM = del /Q
    MKDIR = mkdir
else
    TARGET_EXT = 
    RM = rm -f
    MKDIR = mkdir -p
endif

# Target chính
all: $(TARGET)$(TARGET_EXT)

# Biên dịch chương trình
$(TARGET)$(TARGET_EXT): $(SOURCE)
	$(CXX) $(CXXFLAGS) -o $(TARGET)$(TARGET_EXT) $(SOURCE)
	@echo "Biên dịch thành công!"
	@echo "Chạy chương trình: ./$(TARGET)$(TARGET_EXT)"

# Chạy chương trình
run: $(TARGET)$(TARGET_EXT)
	./$(TARGET)$(TARGET_EXT)

# Debug build
debug: CXXFLAGS += -g -DDEBUG
debug: $(TARGET)$(TARGET_EXT)

# Release build (tối ưu hóa)
release: CXXFLAGS += -O3 -DNDEBUG
release: $(TARGET)$(TARGET_EXT)

# Dọn dẹp files được tạo
clean:
	$(RM) $(TARGET)$(TARGET_EXT)
	@echo "Đã dọn dẹp files!"

# Cài đặt (copy vào thư mục bin)
install: $(TARGET)$(TARGET_EXT)
	$(MKDIR) bin
	cp $(TARGET)$(TARGET_EXT) bin/
	@echo "Đã cài đặt vào thư mục bin/"

# Hiển thị thông tin
info:
	@echo "=== THÔNG TIN DỰ ÁN ==="
	@echo "Tên: Quản Lý Học Sinh"
	@echo "Cấu trúc dữ liệu: Danh Sách Liên Kết Đơn"
	@echo "Ngôn ngữ: C++"
	@echo "Compiler: $(CXX)"
	@echo "Flags: $(CXXFLAGS)"
	@echo "Target: $(TARGET)$(TARGET_EXT)"
	@echo "========================"

# Hiển thị hướng dẫn
help:
	@echo "=== HƯỚNG DẪN SỬ DỤNG MAKEFILE ==="
	@echo "make          - Biên dịch chương trình"
	@echo "make run      - Biên dịch và chạy chương trình"
	@echo "make debug    - Biên dịch với debug info"
	@echo "make release  - Biên dịch với tối ưu hóa"
	@echo "make clean    - Xóa files đã biên dịch"
	@echo "make install  - Cài đặt vào thư mục bin"
	@echo "make info     - Hiển thị thông tin dự án"
	@echo "make help     - Hiển thị hướng dẫn này"
	@echo "=================================="

# Phony targets
.PHONY: all run debug release clean install info help
