# PowerShell script to test Electronics Shop website
Write-Host "========================================" -ForegroundColor Green
Write-Host "   Electronics Shop Website Tester" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Configuration
$baseUrl = "http://localhost/electronics-shop"
$adminUrl = "$baseUrl/administrator"
$timeout = 30

# Test results
$testResults = @()

function Test-Url {
    param(
        [string]$Url,
        [string]$TestName,
        [int]$ExpectedStatusCode = 200,
        [string]$ExpectedContent = $null
    )
    
    try {
        Write-Host "Testing: $TestName" -ForegroundColor Yellow
        Write-Host "URL: $Url" -ForegroundColor Gray
        
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec $timeout -UseBasicParsing
        
        $result = @{
            TestName = $TestName
            Url = $Url
            StatusCode = $response.StatusCode
            ResponseTime = 0
            Success = $false
            Message = ""
        }
        
        # Check status code
        if ($response.StatusCode -eq $ExpectedStatusCode) {
            $result.Success = $true
            $result.Message = "OK"
            Write-Host "✓ PASS - Status: $($response.StatusCode)" -ForegroundColor Green
        } else {
            $result.Message = "Unexpected status code: $($response.StatusCode)"
            Write-Host "✗ FAIL - Status: $($response.StatusCode)" -ForegroundColor Red
        }
        
        # Check content if specified
        if ($ExpectedContent -and $result.Success) {
            if ($response.Content -like "*$ExpectedContent*") {
                Write-Host "✓ Content check passed" -ForegroundColor Green
            } else {
                $result.Success = $false
                $result.Message = "Expected content not found: $ExpectedContent"
                Write-Host "✗ Content check failed" -ForegroundColor Red
            }
        }
        
        # Measure response time (approximate)
        $result.ResponseTime = $response.Headers.'X-Response-Time'
        
    } catch {
        $result = @{
            TestName = $TestName
            Url = $Url
            StatusCode = 0
            ResponseTime = 0
            Success = $false
            Message = $_.Exception.Message
        }
        Write-Host "✗ ERROR - $($_.Exception.Message)" -ForegroundColor Red
    }
    
    $script:testResults += $result
    Write-Host ""
    return $result
}

function Test-DatabaseConnection {
    Write-Host "Testing Database Connection..." -ForegroundColor Yellow
    
    try {
        # Test if MySQL is running
        $mysqlProcess = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue
        if ($mysqlProcess) {
            Write-Host "✓ MySQL service is running" -ForegroundColor Green
            
            # Test database connection (requires MySQL client)
            $testQuery = "SELECT 1"
            $connectionString = "Server=localhost;Database=electronics_shop;Uid=root;Pwd=;"
            
            # This would require MySQL .NET connector
            # For now, just check if phpMyAdmin is accessible
            Test-Url -Url "http://localhost/phpmyadmin" -TestName "phpMyAdmin Access"
            
        } else {
            Write-Host "✗ MySQL service is not running" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ Database test failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    Write-Host ""
}

function Test-FilePermissions {
    Write-Host "Testing File Permissions..." -ForegroundColor Yellow
    
    $joomlaPath = "C:\xampp\htdocs\electronics-shop"
    $criticalPaths = @(
        "configuration.php",
        "tmp",
        "logs",
        "cache",
        "images",
        "administrator\cache"
    )
    
    foreach ($path in $criticalPaths) {
        $fullPath = Join-Path $joomlaPath $path
        if (Test-Path $fullPath) {
            try {
                # Test write permission
                $testFile = Join-Path $fullPath "test_write.tmp"
                "test" | Out-File -FilePath $testFile -ErrorAction Stop
                Remove-Item $testFile -ErrorAction SilentlyContinue
                Write-Host "✓ $path - Write permission OK" -ForegroundColor Green
            } catch {
                Write-Host "✗ $path - Write permission FAILED" -ForegroundColor Red
            }
        } else {
            Write-Host "⚠ $path - Path not found" -ForegroundColor Yellow
        }
    }
    Write-Host ""
}

function Test-Performance {
    param([string]$Url, [string]$TestName)
    
    Write-Host "Performance Test: $TestName" -ForegroundColor Yellow
    
    $times = @()
    for ($i = 1; $i -le 3; $i++) {
        $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
        try {
            $response = Invoke-WebRequest -Uri $Url -TimeoutSec $timeout -UseBasicParsing
            $stopwatch.Stop()
            $times += $stopwatch.ElapsedMilliseconds
            Write-Host "  Attempt $i : $($stopwatch.ElapsedMilliseconds)ms" -ForegroundColor Gray
        } catch {
            Write-Host "  Attempt $i : FAILED" -ForegroundColor Red
        }
    }
    
    if ($times.Count -gt 0) {
        $avgTime = ($times | Measure-Object -Average).Average
        Write-Host "  Average: $([math]::Round($avgTime, 0))ms" -ForegroundColor Cyan
        
        if ($avgTime -lt 3000) {
            Write-Host "✓ Performance: GOOD" -ForegroundColor Green
        } elseif ($avgTime -lt 5000) {
            Write-Host "⚠ Performance: ACCEPTABLE" -ForegroundColor Yellow
        } else {
            Write-Host "✗ Performance: POOR" -ForegroundColor Red
        }
    }
    Write-Host ""
}

# Start testing
Write-Host "Starting website tests..." -ForegroundColor Cyan
Write-Host "Base URL: $baseUrl" -ForegroundColor Gray
Write-Host ""

# Test 1: Basic connectivity
Write-Host "=== CONNECTIVITY TESTS ===" -ForegroundColor Magenta
Test-Url -Url $baseUrl -TestName "Homepage" -ExpectedContent "Electronics Shop"
Test-Url -Url "$baseUrl/shop" -TestName "Shop Page"
Test-Url -Url "$adminUrl" -TestName "Admin Login Page" -ExpectedContent "Joomla"

# Test 2: VirtueMart pages
Write-Host "=== VIRTUEMART TESTS ===" -ForegroundColor Magenta
Test-Url -Url "$baseUrl/index.php?option=com_virtuemart" -TestName "VirtueMart Component"
Test-Url -Url "$baseUrl/index.php?option=com_virtuemart&view=category" -TestName "Category View"
Test-Url -Url "$baseUrl/index.php?option=com_virtuemart&view=cart" -TestName "Shopping Cart"

# Test 3: Static resources
Write-Host "=== STATIC RESOURCES TESTS ===" -ForegroundColor Magenta
Test-Url -Url "$baseUrl/templates/electronics-shop/css/template.css" -TestName "Template CSS"
Test-Url -Url "$baseUrl/templates/electronics-shop/js/template.js" -TestName "Template JS"
Test-Url -Url "$baseUrl/media/jui/css/bootstrap.min.css" -TestName "Bootstrap CSS"

# Test 4: API endpoints (if any)
Write-Host "=== API TESTS ===" -ForegroundColor Magenta
Test-Url -Url "$baseUrl/index.php?option=com_ajax&module=product_condition&method=getConditions&format=json" -TestName "Product Condition API" -ExpectedStatusCode 200

# Test 5: Error pages
Write-Host "=== ERROR HANDLING TESTS ===" -ForegroundColor Magenta
Test-Url -Url "$baseUrl/nonexistent-page" -TestName "404 Error Page" -ExpectedStatusCode 404

# Test 6: Database and file system
Write-Host "=== SYSTEM TESTS ===" -ForegroundColor Magenta
Test-DatabaseConnection
Test-FilePermissions

# Test 7: Performance tests
Write-Host "=== PERFORMANCE TESTS ===" -ForegroundColor Magenta
Test-Performance -Url $baseUrl -TestName "Homepage Load Time"
Test-Performance -Url "$baseUrl/shop" -TestName "Shop Page Load Time"

# Test 8: Mobile responsiveness (basic check)
Write-Host "=== MOBILE RESPONSIVENESS ===" -ForegroundColor Magenta
try {
    $headers = @{
        'User-Agent' = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
    }
    $response = Invoke-WebRequest -Uri $baseUrl -Headers $headers -TimeoutSec $timeout -UseBasicParsing
    if ($response.Content -like "*viewport*") {
        Write-Host "✓ Mobile viewport meta tag found" -ForegroundColor Green
    } else {
        Write-Host "⚠ Mobile viewport meta tag not found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✗ Mobile responsiveness test failed" -ForegroundColor Red
}
Write-Host ""

# Generate test report
Write-Host "=== TEST SUMMARY ===" -ForegroundColor Magenta
$totalTests = $testResults.Count
$passedTests = ($testResults | Where-Object { $_.Success }).Count
$failedTests = $totalTests - $passedTests

Write-Host "Total Tests: $totalTests" -ForegroundColor Cyan
Write-Host "Passed: $passedTests" -ForegroundColor Green
Write-Host "Failed: $failedTests" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round(($passedTests / $totalTests) * 100, 1))%" -ForegroundColor Cyan

if ($failedTests -gt 0) {
    Write-Host ""
    Write-Host "Failed Tests:" -ForegroundColor Red
    $testResults | Where-Object { -not $_.Success } | ForEach-Object {
        Write-Host "  ✗ $($_.TestName): $($_.Message)" -ForegroundColor Red
    }
}

# Save detailed report
$reportPath = ".\test-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
$testResults | ConvertTo-Json -Depth 3 | Out-File -FilePath $reportPath -Encoding UTF8
Write-Host ""
Write-Host "Detailed report saved to: $reportPath" -ForegroundColor Green

# Recommendations
Write-Host ""
Write-Host "=== RECOMMENDATIONS ===" -ForegroundColor Magenta

if ($failedTests -eq 0) {
    Write-Host "🎉 All tests passed! Your website is ready for production." -ForegroundColor Green
} else {
    Write-Host "⚠ Some tests failed. Please review and fix the issues before going live." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Fix any failed tests" -ForegroundColor White
Write-Host "2. Run performance optimization" -ForegroundColor White
Write-Host "3. Configure SSL/HTTPS" -ForegroundColor White
Write-Host "4. Set up monitoring and backups" -ForegroundColor White
Write-Host "5. Test with real users" -ForegroundColor White

Write-Host ""
Write-Host "Testing completed!" -ForegroundColor Green
Read-Host "Press Enter to exit"
