# Simple XAMPP Download Script
Write-Host "Downloading XAMPP..." -ForegroundColor Green

# Create download directory
$downloadDir = "$env:USERPROFILE\Downloads\XAMPP"
if (-not (Test-Path $downloadDir)) {
    New-Item -ItemType Directory -Path $downloadDir -Force
}

# XAMPP download URL
$xamppUrl = "https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download"
$xamppFile = "$downloadDir\xampp-installer.exe"

Write-Host "Downloading from: $xamppUrl"
Write-Host "Saving to: $xamppFile"

try {
    Invoke-WebRequest -Uri $xamppUrl -OutFile $xamppFile -UseBasicParsing
    Write-Host "Download completed!" -ForegroundColor Green
    Write-Host "File saved at: $xamppFile" -ForegroundColor Cyan
    
    # Ask if user wants to run installer
    $runInstaller = Read-Host "Do you want to run the installer now? (y/n)"
    if ($runInstaller -eq "y" -or $runInstaller -eq "Y") {
        Write-Host "Starting XAMPP installer..." -ForegroundColor Yellow
        Start-Process $xamppFile -Verb RunAs
    }
} catch {
    Write-Host "Download failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please download manually from: https://www.apachefriends.org/" -ForegroundColor Yellow
}

Read-Host "Press Enter to continue"
