"""
Cài đặt giải thuật Floyd-Warshall tìm đường đi ngắn nhất
Author: Student
Date: 2025
"""

import sys
from typing import List

class Graph:
    """Lớp biểu diễn đồ thị có trọng số cho Floyd-Warshall"""
    
    def __init__(self, vertices: int):
        """
        Khởi tạo đồ thị
        Args:
            vertices: Số lượng đỉnh
        """
        self.V = vertices
        # Khởi tạo ma trận khoảng cách
        self.dist = [[sys.maxsize for _ in range(vertices)] for _ in range(vertices)]
        
        # Khoảng cách từ một đỉnh đến chính nó là 0
        for i in range(vertices):
            self.dist[i][i] = 0
    
    def add_edge(self, u: int, v: int, weight: int):
        """
        Thêm cạnh vào đồ thị
        Args:
            u: Đỉnh nguồn
            v: Đỉnh đích
            weight: Trọng số của cạnh
        """
        self.dist[u][v] = weight
    
    def floyd_warshall(self) -> List[List[int]]:
        """
        <PERSON><PERSON><PERSON><PERSON> thuật Floyd-<PERSON>hall tìm đường đi ngắn nhất giữa tất cả các cặp đỉnh
        Returns:
            Ma trận khoảng cách ngắn nhất giữa tất cả các cặp đỉnh
        """
        # Tạo bản sao của ma trận khoảng cách
        dist = [row[:] for row in self.dist]
        
        # Thực hiện thuật toán Floyd-Warshall
        # k là đỉnh trung gian
        for k in range(self.V):
            # i là đỉnh nguồn
            for i in range(self.V):
                # j là đỉnh đích
                for j in range(self.V):
                    # Nếu đường đi qua k ngắn hơn đường đi trực tiếp
                    if (dist[i][k] != sys.maxsize and 
                        dist[k][j] != sys.maxsize and 
                        dist[i][k] + dist[k][j] < dist[i][j]):
                        dist[i][j] = dist[i][k] + dist[k][j]
        
        return dist
    
    def has_negative_cycle(self, dist: List[List[int]]) -> bool:
        """
        Kiểm tra xem đồ thị có chu trình âm hay không
        Args:
            dist: Ma trận khoảng cách sau khi chạy Floyd-Warshall
        Returns:
            True nếu có chu trình âm, False nếu không
        """
        for i in range(self.V):
            if dist[i][i] < 0:
                return True
        return False
    
    def print_solution(self, dist: List[List[int]]):
        """
        In ma trận khoảng cách ngắn nhất
        Args:
            dist: Ma trận khoảng cách
        """
        print("Ma trận khoảng cách ngắn nhất giữa tất cả các cặp đỉnh:")
        print("(∞ nghĩa là không có đường đi)")
        print()
        
        # In header
        print("   ", end="")
        for j in range(self.V):
            print(f"{j:>8}", end="")
        print()
        
        # In ma trận
        for i in range(self.V):
            print(f"{i:>2} ", end="")
            for j in range(self.V):
                if dist[i][j] == sys.maxsize:
                    print(f"{'∞':>8}", end="")
                else:
                    print(f"{dist[i][j]:>8}", end="")
            print()
    
    def print_path(self, dist: List[List[int]], start: int, end: int):
        """
        In đường đi ngắn nhất giữa hai đỉnh cụ thể
        Args:
            dist: Ma trận khoảng cách
            start: Đỉnh bắt đầu
            end: Đỉnh kết thúc
        """
        if dist[start][end] == sys.maxsize:
            print(f"Không có đường đi từ đỉnh {start} đến đỉnh {end}")
        else:
            print(f"Khoảng cách ngắn nhất từ đỉnh {start} đến đỉnh {end}: {dist[start][end]}")

def create_example_graph() -> Graph:
    """Tạo đồ thị ví dụ để minh họa"""
    # Tạo đồ thị có 4 đỉnh
    g = Graph(4)
    
    # Thêm các cạnh (u, v, trọng số)
    g.add_edge(0, 1, 5)
    g.add_edge(0, 3, 10)
    g.add_edge(1, 2, 3)
    g.add_edge(2, 3, 1)
    
    return g

def create_negative_weight_graph() -> Graph:
    """Tạo đồ thị có trọng số âm để minh họa"""
    g = Graph(4)
    
    # Thêm các cạnh có trọng số âm
    g.add_edge(0, 1, 1)
    g.add_edge(1, 2, -3)
    g.add_edge(2, 3, 2)
    g.add_edge(3, 1, 1)
    g.add_edge(1, 3, 4)
    
    return g

def main():
    """Hàm chính để chạy chương trình"""
    print("=== GIẢI THUẬT FLOYD-WARSHALL - TÌM ĐƯỜNG ĐI NGẮN NHẤT ===\n")
    
    # Test 1: Đồ thị cơ bản
    print("TEST 1: Đồ thị cơ bản")
    graph1 = create_example_graph()
    
    print("Các cạnh: (0,1,5), (0,3,10), (1,2,3), (2,3,1)")
    print()
    
    result1 = graph1.floyd_warshall()
    graph1.print_solution(result1)
    
    print("\nMột số đường đi cụ thể:")
    graph1.print_path(result1, 0, 2)
    graph1.print_path(result1, 0, 3)
    
    print("\n" + "="*60 + "\n")
    
    # Test 2: Đồ thị có trọng số âm
    print("TEST 2: Đồ thị có trọng số âm")
    graph2 = create_negative_weight_graph()
    
    print("Các cạnh: (0,1,1), (1,2,-3), (2,3,2), (3,1,1), (1,3,4)")
    print()
    
    result2 = graph2.floyd_warshall()
    
    # Kiểm tra chu trình âm
    if graph2.has_negative_cycle(result2):
        print("Cảnh báo: Đồ thị chứa chu trình âm!")
    else:
        print("Đồ thị không chứa chu trình âm.")
    
    print()
    graph2.print_solution(result2)
    
    print("\n=== PHÂN TÍCH KẾT QUẢ ===")
    print("- Floyd-Warshall tìm đường đi ngắn nhất giữa TẤT CẢ các cặp đỉnh")
    print("- Có thể xử lý trọng số âm và phát hiện chu trình âm")
    print("- Độ phức tạp thời gian: O(V³)")
    print("- Độ phức tạp không gian: O(V²)")
    print("- Phù hợp với đồ thị nhỏ và trung bình")
    print("- Sử dụng quy hoạch động với 3 vòng lặp lồng nhau")

if __name__ == "__main__":
    main()
