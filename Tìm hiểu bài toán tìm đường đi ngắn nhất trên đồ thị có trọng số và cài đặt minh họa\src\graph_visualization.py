"""
Visualization cho các giải thuật tìm đường đi ngắn nhất
Author: Student
Date: 2025
"""

import matplotlib.pyplot as plt
import networkx as nx
import numpy as np
from typing import List, Tuple, Dict

class GraphVisualizer:
    """Lớp để vẽ và minh họa đồ thị"""
    
    def __init__(self):
        """Khởi tạo visualizer"""
        plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
        
    def create_networkx_graph(self, edges: List[Tuple[int, int, int]], directed: bool = True) -> nx.Graph:
        """
        Tạo đồ thị NetworkX từ danh sách cạnh
        Args:
            edges: Danh sách các cạnh (u, v, weight)
            directed: <PERSON><PERSON> thị có hướng hay không
        Returns:
            Đồ thị NetworkX
        """
        if directed:
            G = nx.DiGraph()
        else:
            G = nx.Graph()
        
        for u, v, weight in edges:
            G.add_edge(u, v, weight=weight)
        
        return G
    
    def visualize_graph(self, edges: List[Tuple[int, int, int]], 
                       title: str = "Đồ thị", 
                       highlight_path: List[int] = None,
                       directed: bool = True):
        """
        Vẽ đồ thị
        Args:
            edges: Danh sách các cạnh (u, v, weight)
            title: Tiêu đề của đồ thị
            highlight_path: Đường đi cần highlight
            directed: Đồ thị có hướng hay không
        """
        G = self.create_networkx_graph(edges, directed)
        
        plt.figure(figsize=(12, 8))
        
        # Tạo layout cho đồ thị
        pos = nx.spring_layout(G, seed=42, k=2, iterations=50)
        
        # Vẽ các cạnh
        edge_colors = []
        edge_widths = []
        
        for u, v in G.edges():
            if highlight_path and len(highlight_path) > 1:
                # Kiểm tra xem cạnh có trong đường đi highlight không
                is_in_path = False
                for i in range(len(highlight_path) - 1):
                    if (u == highlight_path[i] and v == highlight_path[i + 1]) or \
                       (not directed and u == highlight_path[i + 1] and v == highlight_path[i]):
                        is_in_path = True
                        break
                
                if is_in_path:
                    edge_colors.append('red')
                    edge_widths.append(3)
                else:
                    edge_colors.append('gray')
                    edge_widths.append(1)
            else:
                edge_colors.append('gray')
                edge_widths.append(1)
        
        # Vẽ cạnh
        nx.draw_networkx_edges(G, pos, edge_color=edge_colors, width=edge_widths,
                              arrows=directed, arrowsize=20, arrowstyle='->')
        
        # Vẽ đỉnh
        node_colors = []
        for node in G.nodes():
            if highlight_path and node in highlight_path:
                if node == highlight_path[0]:
                    node_colors.append('lightgreen')  # Đỉnh bắt đầu
                elif node == highlight_path[-1]:
                    node_colors.append('lightcoral')  # Đỉnh kết thúc
                else:
                    node_colors.append('lightblue')   # Đỉnh trung gian
            else:
                node_colors.append('lightgray')
        
        nx.draw_networkx_nodes(G, pos, node_color=node_colors, 
                              node_size=800, alpha=0.9)
        
        # Vẽ nhãn đỉnh
        nx.draw_networkx_labels(G, pos, font_size=12, font_weight='bold')
        
        # Vẽ trọng số cạnh
        edge_labels = nx.get_edge_attributes(G, 'weight')
        nx.draw_networkx_edge_labels(G, pos, edge_labels, font_size=10)
        
        plt.title(title, fontsize=16, fontweight='bold')
        plt.axis('off')
        plt.tight_layout()
        plt.show()
    
    def visualize_dijkstra_step_by_step(self, edges: List[Tuple[int, int, int]], 
                                       source: int, target: int):
        """
        Minh họa từng bước của giải thuật Dijkstra
        Args:
            edges: Danh sách các cạnh
            source: Đỉnh nguồn
            target: Đỉnh đích
        """
        # Tạo đồ thị và chạy Dijkstra để lấy kết quả
        from dijkstra import Graph
        
        # Tìm số đỉnh tối đa
        max_vertex = max(max(u, v) for u, v, _ in edges) + 1
        
        graph = Graph(max_vertex)
        for u, v, w in edges:
            graph.add_edge(u, v, w)
        
        distances, parents = graph.dijkstra(source)
        
        # Truy vết đường đi
        path = graph.get_path(parents, target)
        
        # Vẽ đồ thị gốc
        self.visualize_graph(edges, f"Đồ thị gốc - Tìm đường từ {source} đến {target}")
        
        # Vẽ đồ thị với đường đi ngắn nhất
        if path and len(path) > 1:
            self.visualize_graph(edges, 
                               f"Đường đi ngắn nhất từ {source} đến {target} (khoảng cách: {distances[target]})",
                               highlight_path=path)
        else:
            print(f"Không có đường đi từ {source} đến {target}")
    
    def compare_algorithms_visualization(self):
        """So sánh trực quan các giải thuật"""
        # Tạo dữ liệu so sánh độ phức tạp
        vertices = np.array([10, 20, 50, 100, 200, 500])
        edges = vertices * 2  # Giả sử đồ thị có E ≈ 2V
        
        # Tính độ phức tạp (chuẩn hóa)
        dijkstra_complexity = (vertices + edges) * np.log2(vertices)
        bellman_ford_complexity = vertices * edges
        floyd_warshall_complexity = vertices ** 3
        
        plt.figure(figsize=(12, 8))
        
        plt.subplot(2, 2, 1)
        plt.plot(vertices, dijkstra_complexity, 'b-o', label='Dijkstra O((V+E)logV)')
        plt.plot(vertices, bellman_ford_complexity, 'r-s', label='Bellman-Ford O(VE)')
        plt.plot(vertices, floyd_warshall_complexity, 'g-^', label='Floyd-Warshall O(V³)')
        plt.xlabel('Số đỉnh (V)')
        plt.ylabel('Độ phức tạp (chuẩn hóa)')
        plt.title('So sánh độ phức tạp thời gian')
        plt.legend()
        plt.yscale('log')
        
        # Bảng so sánh tính năng
        plt.subplot(2, 2, 2)
        algorithms = ['Dijkstra', 'Bellman-Ford', 'Floyd-Warshall']
        features = ['Trọng số âm', 'Chu trình âm', 'SSSP', 'APSP']
        
        # Ma trận tính năng (1: có, 0: không, 0.5: phát hiện)
        feature_matrix = np.array([
            [0, 0, 1, 0],      # Dijkstra
            [1, 0.5, 1, 0],    # Bellman-Ford  
            [1, 0.5, 0, 1]     # Floyd-Warshall
        ])
        
        im = plt.imshow(feature_matrix, cmap='RdYlGn', aspect='auto')
        plt.xticks(range(len(features)), features, rotation=45)
        plt.yticks(range(len(algorithms)), algorithms)
        plt.title('So sánh tính năng')
        
        # Thêm text vào các ô
        for i in range(len(algorithms)):
            for j in range(len(features)):
                value = feature_matrix[i, j]
                if value == 1:
                    text = 'Có'
                elif value == 0.5:
                    text = 'Phát hiện'
                else:
                    text = 'Không'
                plt.text(j, i, text, ha='center', va='center', fontweight='bold')
        
        plt.subplot(2, 2, 3)
        # Biểu đồ ứng dụng
        use_cases = ['Định tuyến mạng', 'GPS Navigation', 'Game AI', 'Tối ưu chi phí']
        dijkstra_usage = [90, 95, 80, 70]
        bellman_ford_usage = [60, 30, 40, 85]
        floyd_warshall_usage = [40, 20, 60, 50]
        
        x = np.arange(len(use_cases))
        width = 0.25
        
        plt.bar(x - width, dijkstra_usage, width, label='Dijkstra', alpha=0.8)
        plt.bar(x, bellman_ford_usage, width, label='Bellman-Ford', alpha=0.8)
        plt.bar(x + width, floyd_warshall_usage, width, label='Floyd-Warshall', alpha=0.8)
        
        plt.xlabel('Ứng dụng')
        plt.ylabel('Mức độ phù hợp (%)')
        plt.title('Mức độ phù hợp với các ứng dụng')
        plt.xticks(x, use_cases, rotation=45)
        plt.legend()
        
        plt.subplot(2, 2, 4)
        # Biểu đồ thời gian thực thi (giả lập)
        graph_sizes = ['Nhỏ (V<50)', 'Trung bình (50≤V<200)', 'Lớn (V≥200)']
        dijkstra_time = [1, 5, 25]
        bellman_ford_time = [3, 20, 150]
        floyd_warshall_time = [2, 50, 800]
        
        x = np.arange(len(graph_sizes))
        
        plt.bar(x - width, dijkstra_time, width, label='Dijkstra', alpha=0.8)
        plt.bar(x, bellman_ford_time, width, label='Bellman-Ford', alpha=0.8)
        plt.bar(x + width, floyd_warshall_time, width, label='Floyd-Warshall', alpha=0.8)
        
        plt.xlabel('Kích thước đồ thị')
        plt.ylabel('Thời gian (ms)')
        plt.title('Thời gian thực thi ước tính')
        plt.xticks(x, graph_sizes)
        plt.legend()
        plt.yscale('log')
        
        plt.tight_layout()
        plt.show()

def demo_visualization():
    """Demo các tính năng visualization"""
    visualizer = GraphVisualizer()
    
    # Ví dụ 1: Đồ thị cơ bản
    edges1 = [
        (0, 1, 4), (0, 2, 2), (1, 2, 1), (1, 3, 5),
        (2, 3, 8), (2, 4, 10), (3, 4, 2), (3, 5, 6), (4, 5, 3)
    ]
    
    print("Demo 1: Vẽ đồ thị cơ bản")
    visualizer.visualize_graph(edges1, "Đồ thị ví dụ cho Dijkstra")
    
    print("Demo 2: Minh họa Dijkstra từng bước")
    visualizer.visualize_dijkstra_step_by_step(edges1, 0, 5)
    
    print("Demo 3: So sánh các giải thuật")
    visualizer.compare_algorithms_visualization()

if __name__ == "__main__":
    demo_visualization()
