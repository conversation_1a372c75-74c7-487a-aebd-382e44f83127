{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["w2g+r59QLS2IJZJWyho9usw9Dw14he6q8Hkqw78CEGo=", "V9lgjh9GcgParbtQ1PkMOMR7wy0OjkZIC0gXMwGtveg=", "tWeLUjdxvypQALvHW4uZAD5ePSbnpJn61JUAx6RDKtw=", "vxNko+wbnm+WrYHCskpoZhXhmK1cW/yBol3Z1XI+NV8=", "W5Rm8CKxprpxEiOkSSyxqjDzQimSOobLirgkQ0pzdEw=", "mF7C1XxX3V8IvHFUKyBjUo4rlkMWk0t5WnMyoZOyJZo=", "aUzvGQuc9RTHRsR24PYuuTlGrqLzeTzSoJN5uEyHU24=", "zGgM6ytFdBn/1RDmCEdugJ4mB498i9/P/dSOoBF2Bh4=", "F/8S0uG1ly+DgbUujEfNDQx6XrsxUTYS5WrBr5qgDoI=", "SLp4rfwZ4S8kBLI+FD4qfkvLos+6Kul5B+UdTpZdVf8=", "//pgVLfW/bX+SlW4cRtMSfvlaLsq9vTQyWT8iajEpfs=", "xfv700T9kl1kRhxJxaRIzKctRMn0AJ5j0jE45eWTioQ=", "UwDzP6wl85LWJo0X1AqxiVtktKrVyavO1mukQPqL/dM=", "uBFPzf5Dn/ZBbEuodHHe9gbftUD0XpomC9fsbeGj94w=", "ojOmL5n+6cUOHcFhD4PcZIzumnVMgXhO6nsgGpbVYd4=", "HYycUu/E+ph9cQX84THasOtXBYBSHVPN/H9GhzVwvq0=", "DRveTSLc4xftf1Pa8Ox64pLff1pTEokEwIx7Kborq0c=", "FS4DHtGVFNAMeC4jf6FbeuuJ3tPw4q8XettyQkaKT2I=", "TIi6bYqR1p+rei77aH4UZEzUGz7XbyUmpSX87hFA8+g=", "sz7V4ccecTrPjqjjJi6Ux+Y1gDq7TyJy5eZxI+VGxCQ=", "kjAQTURDlIFGhjbB9giblWkyl0EoGkEkxH0h8NO7gvw=", "7HRBlTp5Xcya/R93f3TTGdIT7+8BY1OYWYdYdsdz0MQ=", "SpzKPvwrouNK9TNlrrpMqB2CSMwAg6HhJiUd7Bwn4m4=", "N1Dl10+MlvvkRXGlkWTXIGHYjWgidgosEh2vv75CsNU=", "RRdJ3AfiP+9YenpgeIiknTY0sN6gLNXNNAmyl05NA2o=", "6y0DrMY87bHoZe7T04uBCgkdoq4eRwG3IGwE0A17sFM=", "xjDW1SdrLfRtjoh1C/aI/3xZScmfSU8WVJow3q/5CiQ=", "G242HgCK4d7k4tzg+pn6TFc+Tx5BuMtQxuWjeMczis4=", "87ov3xRkmdPbgrZImLER20+KiDti7LaQp2AIPY+Y7og=", "FU95JLla0CTQdp09IEJV6yvoma4mMfbfFBe8Orb/8P4=", "gAJrFLdm0hm6kWwY+wfdAfsbuvLsACbe9Lh0KU91Y/c=", "TsdIVz3Ccrb+7lhqLvC7VcJKGFTp0XAZJnKx5tlFPY4=", "RKA4PqCEf9ah+WcjQXXtoPeBRo5xWxWeQjCnSIEycwc=", "GM29QXlFxXBWdL+oVtKaM0oAxiKwhsf4Ub4DHlYfAoY=", "jkWLyRlKceSUlKCRCLxQDuVNUMs2Z9pmfHtpWMfW1qc=", "17lTU9cqV2JJLBiRdwU454/z8l3MG3YB4KPtyuj9Pms=", "NQQK1Sqpj4E2lGue/DpmhQs9MYwvu355a7iu6Edl95M=", "k4IFwYmG+Hb8RcJc2Q2HrdiZNpQxJVptDlkM0KN/3qo=", "ZbcTPm79m+D3DzePA6Tsd1fm5Z62JbChXZM14brc1wI=", "TOLpLhtAdOUfstHXXQXQXcex/ynDUebNmeLZNxioda0=", "yBQsQGjYxOMlAhbldxKK+yPH41oLpyyWORuFQcHJjA8=", "zp8cqxx0J3hf3Iya8aqAlWIpcahlILEps5KFkdo2Myo=", "Qvj+mB3M7gIh7+Evz3hCNLJxgW0SPILcWqXU7FzbxSk=", "LURPkPzY+RSs+KNDFMQQ9YV5pSgAz9h4WFCUNE7M3Lo=", "VEt5mk+Lk2HJcxvsDMhU5lqz5hw4wT+N8btMoLtlOEM=", "2WtFDIdDYq/dAI+pBy5j3X0zWMJPpxhyM+W8r579K0U=", "xyUMrwnD3OAWOh+aX/AG0RJnCQUtLYSmXocjFfye4rM=", "Cf8MrsI5fdFSGP+effgFSS9vLhRL3weyPdFuRnFKafo=", "/gReDsx7DrIOZCl62Q0nQ1Gv9SYTOKkhRiMBBudAgtQ=", "qr30LHwcaZ8P5F9Sxrhiz+geoDBokJ8kTxGbPgfSxMg=", "jfFlw7gjRLARQ4fxc7Lu1j2LmJDON3cNQQWii8s/nrQ=", "9RKFuQXxvfS+CtfEgsQ5FDGW7mFH2C86GHTR/w5+VnI=", "Meq5G1AXHV0Ma77q2/aYjrmswCSqMMQPLQqnqVEBwDY=", "7/pkIpCnZdJ++ihCMBroIdFMkEhXgH7GK9BhPjfqUJk=", "H+ygVgKW6oogV6nzpMbOpkV8z676fM2YtIQDZbaw0YQ=", "P7VhAXeV0FxkOT9h1Zq/RPOHwbsj1IuKFjN9Q0iFHoU=", "6DiO64XGtR3iteO0GNwZs+Hk42Nd+TgpTTLSaQtnIdE=", "hzE18DdV+3DflNVrfLvXBNUvlZHcO5xM+R0Rzfs9eOA=", "lLRc+8dOMbVxta0HPToEaubrq1NY7mZlhu9fYWdYlb8=", "kcLbj1fCOXwqEfPJJdydrzInIp27RA7eQiaKpSB8YBo=", "9N3Wm5BeQIRN9vekSDvaHWeJJpqlKowCMHsViu/DGF4=", "43qn8NLuUO+KkW55+A1jwpw9p29hD9x4iFhAv48EtkY=", "Wk0KaevDAwlLzrm5ZNLRwk3GdJITPIxOgTffTWerGmw=", "bbXWOY5vRjSl4JBLnivPhyeNnPJ7/GR+OR1QCZ6OiPw=", "rPnjJUeYNhUWaxe3BiQuKXHRdoR1htld7k1gof2XNco=", "xFNDsPbg3aAtj08uLUIIBwjXAGNvx+Om7DRg1ErhlHo=", "MBCLkcbZmZkaSxplDGTlhVQGLGS1hYLjg2Kpv0ShvlE=", "O8Sxy4OYxunyFSe32CKQQFsi7WTUlaevHOn0JZHbDRM=", "CP/xJt8geVOJjyKBCui6ruqy5Nms0/YeXlye1Tr2VeY=", "1c1XAovvVvZ9FbaGvH3y9lBobMrbBmhhkl+kPxqpTbI=", "WQIhir6NgNXs85jzxIgrTadGY8Dqik3pPWlYBolsOEE=", "fM05eLOCjIqhkbksOkcSN/tL4Tr8A1TnyzKaGspJBYQ=", "pfpicPWJHnE3v4aB7Quvve0dyPCjEqpQrX0iRdBjTVU=", "/1+o4U82bDB8OHnUQtvQLwXNG35A7ReiGKDcOZ5jlQE=", "MbcLFPbMGfmlpkIiPHlOypMnFfQ7gLPiNqid5jb6V/s=", "ZEek9+wQpC9pAqoBTs1ME9TC6MDwOIyZDw7LnPnu+hU=", "fmw5fcEbek70AvIhoSwMaBrdop029cRw5J5LwIPm1DE=", "zIT8Scz/ewERsoCwTH959qXv6/lKC4FW9E3/X3RIj78=", "j1cfpsXq26Gwy8wwVuRn2pBkLV5Zu0zrqTYSxDNTjDk=", "mATyHxcRoDnYOEL2TuN0kJM5i4GCgbnoqWdmkGwsD/Y=", "X4BOxsV2WXLicbQonyT0aMCJ0jaDfAbSQLqb284dJrM=", "Xy9ZS/tkoe7l78eH7zNXZjU3umk0Kj2RpptsKSAgU54=", "cgraZf9ZzkFeo60UjR2pK1N5UPs2EsFYh9b8o+wTMDI=", "pl3dltGwUzMbTnNAwsONh20KZMnEOj6dMQSIm9hXY7k=", "JZEgrVzntocEBTawu45zCWYaEzBtQ+fV3Qy5RaDoOVg=", "y5IwHbku2DrV8XgFg5nvSXhLVG3u+H/Txg8IzLq+u9g=", "wGudYo4sIzIu5lZT23iEJAI13qdU8POlfvnOrdT0gmw=", "ipXLJMfh7oNNiqMOWMHaAWG2AIxm8Y9Tshmi8nsfWUo=", "DEFB4mHTRqjuysl5IIpYeyxI916CY2OC1E/p/WaZ5WM=", "NiBtMN+KOyywm1Ca3NjVzXJShTA3NUr5KWJqod1bh14=", "PszNRahmv0PBaF2f5JxT3ievV6sQT5AH2kHCRXqymwo=", "bJD2l2TFhIMhoVOYQqKZoeddKF/EVDoeoJ/T8gBfpFA=", "OyWY9pPuEZGfkkg5fJwSEShyIlC4RAHUntZrz1JqJAo=", "GBMkVHbPL+4+SYGZ/T1ldci69y6ihRgDTeb0EMvia4I=", "89FAa3Umwwd7t0o2+nBqu8YtTeyi+HJbnPT49MFNO8c=", "X1EfXCIgMeCxxGIbKTrJQLt6EutKvytJYAy+8kc/+4Q=", "4jRxhjL6dB0A0fEbTW6M/rWkWtWw0EmgDcOjoCHILbE=", "GLuS2neQsuM4tPAKVUgWyrmeY9KzhPxaF1FsL8cQ5Ck=", "sFHYLz8SW0cZU1e6D/USDvuQyypdwCp6BddVwpljtcg=", "duywNEaHcKbiyjBUMCfcji9hqRXWIFti3wF/OwdFX9Y=", "gBaEpjgr/twSLM/l1/B7N3TGJTbyfQDeZKTdQa308dY=", "x7eMPE2JuJKxnO08Zrmt7H9XjINsv33jljy5LykCv/E=", "F09C6RxGQkbXD5HgUH8MTBynvkSBM3e05HMN78UWNBg=", "LXqLXZMvRsKF4q9fgOPWnkMLju5rESSFt4RDrhY5Vts=", "LO6Exl53wPM3QJhizkKf3O08Ze2BniqMfWl1mqV86Gk=", "qF35B5wCkCnj3St6Ek0YUK6JEWXAgJtJRWcvarN/UhM=", "QxxCb3GCIwwE9wbc935xkkng7NW+dk6CmVpDxyll0gk=", "0XKyrokDRuPvb6RFKKcSRuxipn5DhOkBlgyZmoKW8CU=", "8LQaqHGqpppiKVxx/ZG0I6nKitqvD9v4fLhZojINDps=", "0riiiCPOAsNksQdpT1CsPBDVkSTwf6xFHDAcbxuQquU=", "gctcUIt5s0xHBm9uJftmlqdINnunK+kmjqZxCOze/Vo=", "zgQDB/2mk4Vv96fTcfZHK5nbpIAmnShju5t+jPH4CqU=", "dIXMIpBOFV7SDLVosU2ThsTG5iHLziysmOj3tLvzD44=", "VWags4167DOodxoQMdEPrwu11rW6UJ6jDlFvm8aBoSU=", "qHanQzlduSXXw8ypMhEFP6PR5hgG6YfES2PddWmAXOo=", "l0nseBopaNpOOrSeT+AMEsglYGKbCNZscIkcWQ0BRUE=", "QA19TxfrAnn7mc8IkF8riKV/C9sktzE+1ZbEBct4KKw=", "b0usmgRmXK4pqmoMT9BfJv/khuPL16ZTkpFzuZlm5vY=", "cKthKhS8YQNpMPYBjCk8UlcwljnGi53xeHKCZWMbMYI=", "1RPlV3NC7koWCOmgEurXamqDYbWvHi2mood+s5cW3RI=", "ow2IDta4FFviIkLq51hZjBofsZEe6AttDTFxJhCC3wI=", "CFgNHJr4KnV7/+s5BaxlsGKLNJ60DC7LxJ9x/JQWTMs=", "6erzp3xsCP8vxgUkipt57WHl5L4a6B/jqnP53dBbJBs=", "0jaDOvHNHnQo/lyAhFRuKGf4FyK3h5eo0HtNojfDCwI=", "HN78frd6oB6SeAFDD9X17uqkece2DNehr0OV8AuoL9M=", "FgYs1eN3HV/rOSm3npclnAzNOG7oNBfZ9y+jBLwdxWg=", "qR17oOc+A/lsSWmg3i8BTKpL5LGcodWKghsHGOYyP6I=", "r/H7oLTCVP40cDwl492P+jnB8MALINGOOcRQvoXZltg=", "dZZuqW+DXrS/k5q2axpihOotf6ieF2zSxART89c4YIM=", "eH5ET2JL9ZIsQjmBkvglQj3zDi86bkWVi8P7tqe+Sjg=", "tcv77msrApgjM2DbKyaaeqZChDhiLNLqabqisa+iA7U=", "8rb0j0HsnVyaYRrYghNnBIekssX8lHkq/IonjH5ApQQ=", "V5W/dHAWOebre1uN5K69PF8kHaAoOooSC6yIeMqbMbU=", "4Ww3cfemwdAgsY/SaR2blAeNSjtRUuU/SPdwybDLa9U=", "nTL5OsvooAV5u4UUdmgLF0tLOZVdLkBMNbXqMkv0k+k=", "96iKNmN41RmJixg6XC5KI1Uk7SMphU9JxO/8P/8eOdU=", "PLt/9+99H2vBuO1KP7hhKhSfZaVWrInJSB/qTfy0LVM=", "RTLf3mB/vATeYZMJQk8bXZDpsSHZYG22I+uFinxjqBM=", "pbabCm+S3KpIXxqb1CdPjPXS1bwwYe6F2xse+iwnqH8=", "iWCfbB//k5KlUFmB9h7b+kCTNWKtoSlrQ5F8AM64Eq0=", "s6hz4XzGIhp10fpB/4jJ5wxxogNNB96o0WNmlQWonVk=", "r54/PNElI/PZKYKYWAU7CxODvw7oPrMx8ukg4IlQ+J0=", "PR4fCRSZTieLi6mOJOiEvaQziu3sWKcYFsMy6rVqEW0="], "CachedAssets": {"w2g+r59QLS2IJZJWyho9usw9Dw14he6q8Hkqw78CEGo=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-07-23T17:18:53.5460996+00:00"}, "V9lgjh9GcgParbtQ1PkMOMR7wy0OjkZIC0gXMwGtveg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-07-23T17:18:53.5550195+00:00"}, "tWeLUjdxvypQALvHW4uZAD5ePSbnpJn61JUAx6RDKtw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-23T17:18:53.584929+00:00"}, "vxNko+wbnm+WrYHCskpoZhXhmK1cW/yBol3Z1XI+NV8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-23T17:18:53.5727613+00:00"}, "W5Rm8CKxprpxEiOkSSyxqjDzQimSOobLirgkQ0pzdEw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-23T17:18:53.5753293+00:00"}, "mF7C1XxX3V8IvHFUKyBjUo4rlkMWk0t5WnMyoZOyJZo=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-23T17:18:53.5809029+00:00"}, "aUzvGQuc9RTHRsR24PYuuTlGrqLzeTzSoJN5uEyHU24=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-23T17:18:53.5820091+00:00"}, "zGgM6ytFdBn/1RDmCEdugJ4mB498i9/P/dSOoBF2Bh4=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-23T17:18:53.5869371+00:00"}, "F/8S0uG1ly+DgbUujEfNDQx6XrsxUTYS5WrBr5qgDoI=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-23T17:18:53.61303+00:00"}, "SLp4rfwZ4S8kBLI+FD4qfkvLos+6Kul5B+UdTpZdVf8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-23T17:18:53.61303+00:00"}, "//pgVLfW/bX+SlW4cRtMSfvlaLsq9vTQyWT8iajEpfs=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-23T17:18:53.61303+00:00"}, "xfv700T9kl1kRhxJxaRIzKctRMn0AJ5j0jE45eWTioQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-23T17:18:53.5173778+00:00"}, "UwDzP6wl85LWJo0X1AqxiVtktKrVyavO1mukQPqL/dM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-23T17:18:53.5392467+00:00"}, "uBFPzf5Dn/ZBbEuodHHe9gbftUD0XpomC9fsbeGj94w=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-23T17:18:53.5460996+00:00"}, "ojOmL5n+6cUOHcFhD4PcZIzumnVMgXhO6nsgGpbVYd4=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-23T17:18:53.5659704+00:00"}, "HYycUu/E+ph9cQX84THasOtXBYBSHVPN/H9GhzVwvq0=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-23T17:18:53.5659704+00:00"}, "DRveTSLc4xftf1Pa8Ox64pLff1pTEokEwIx7Kborq0c=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-23T17:18:53.5738478+00:00"}, "FS4DHtGVFNAMeC4jf6FbeuuJ3tPw4q8XettyQkaKT2I=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-23T17:18:53.5793936+00:00"}, "TIi6bYqR1p+rei77aH4UZEzUGz7XbyUmpSX87hFA8+g=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-23T17:18:53.5820091+00:00"}, "sz7V4ccecTrPjqjjJi6Ux+Y1gDq7TyJy5eZxI+VGxCQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-23T17:18:53.5869371+00:00"}, "kjAQTURDlIFGhjbB9giblWkyl0EoGkEkxH0h8NO7gvw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-23T17:18:53.5982652+00:00"}, "7HRBlTp5Xcya/R93f3TTGdIT7+8BY1OYWYdYdsdz0MQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-23T17:18:53.6088936+00:00"}, "SpzKPvwrouNK9TNlrrpMqB2CSMwAg6HhJiUd7Bwn4m4=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-23T17:18:53.5341462+00:00"}, "N1Dl10+MlvvkRXGlkWTXIGHYjWgidgosEh2vv75CsNU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-23T17:18:53.5415807+00:00"}, "RRdJ3AfiP+9YenpgeIiknTY0sN6gLNXNNAmyl05NA2o=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-23T17:18:53.5582295+00:00"}, "6y0DrMY87bHoZe7T04uBCgkdoq4eRwG3IGwE0A17sFM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-23T17:18:53.5582295+00:00"}, "xjDW1SdrLfRtjoh1C/aI/3xZScmfSU8WVJow3q/5CiQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-23T17:18:53.5626949+00:00"}, "G242HgCK4d7k4tzg+pn6TFc+Tx5BuMtQxuWjeMczis4=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-23T17:18:53.5869371+00:00"}, "87ov3xRkmdPbgrZImLER20+KiDti7LaQp2AIPY+Y7og=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-23T17:18:53.6247026+00:00"}, "FU95JLla0CTQdp09IEJV6yvoma4mMfbfFBe8Orb/8P4=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-23T17:18:53.5959131+00:00"}, "gAJrFLdm0hm6kWwY+wfdAfsbuvLsACbe9Lh0KU91Y/c=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-23T17:18:53.6115227+00:00"}, "TsdIVz3Ccrb+7lhqLvC7VcJKGFTp0XAZJnKx5tlFPY4=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-23T17:18:53.61303+00:00"}, "RKA4PqCEf9ah+WcjQXXtoPeBRo5xWxWeQjCnSIEycwc=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-23T17:18:53.6553308+00:00"}, "GM29QXlFxXBWdL+oVtKaM0oAxiKwhsf4Ub4DHlYfAoY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-23T17:18:53.5341462+00:00"}, "jkWLyRlKceSUlKCRCLxQDuVNUMs2Z9pmfHtpWMfW1qc=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-23T17:18:53.5431369+00:00"}, "17lTU9cqV2JJLBiRdwU454/z8l3MG3YB4KPtyuj9Pms=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-23T17:18:53.549108+00:00"}, "NQQK1Sqpj4E2lGue/DpmhQs9MYwvu355a7iu6Edl95M=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-23T17:18:53.5659704+00:00"}, "k4IFwYmG+Hb8RcJc2Q2HrdiZNpQxJVptDlkM0KN/3qo=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-23T17:18:53.5809029+00:00"}, "ZbcTPm79m+D3DzePA6Tsd1fm5Z62JbChXZM14brc1wI=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-23T17:18:53.5982652+00:00"}, "TOLpLhtAdOUfstHXXQXQXcex/ynDUebNmeLZNxioda0=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-23T17:18:53.606035+00:00"}, "yBQsQGjYxOMlAhbldxKK+yPH41oLpyyWORuFQcHJjA8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-23T17:18:53.6110006+00:00"}, "zp8cqxx0J3hf3Iya8aqAlWIpcahlILEps5KFkdo2Myo=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-23T17:18:53.61303+00:00"}, "Qvj+mB3M7gIh7+Evz3hCNLJxgW0SPILcWqXU7FzbxSk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-23T17:18:53.61303+00:00"}, "LURPkPzY+RSs+KNDFMQQ9YV5pSgAz9h4WFCUNE7M3Lo=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-23T17:18:53.6342181+00:00"}, "VEt5mk+Lk2HJcxvsDMhU5lqz5hw4wT+N8btMoLtlOEM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-23T17:18:53.5361633+00:00"}, "2WtFDIdDYq/dAI+pBy5j3X0zWMJPpxhyM+W8r579K0U=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-23T17:18:53.5460996+00:00"}, "xyUMrwnD3OAWOh+aX/AG0RJnCQUtLYSmXocjFfye4rM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-23T17:18:53.5614928+00:00"}, "Cf8MrsI5fdFSGP+effgFSS9vLhRL3weyPdFuRnFKafo=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-23T17:18:53.5626949+00:00"}, "/gReDsx7DrIOZCl62Q0nQ1Gv9SYTOKkhRiMBBudAgtQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-23T17:18:53.5637744+00:00"}, "qr30LHwcaZ8P5F9Sxrhiz+geoDBokJ8kTxGbPgfSxMg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-23T17:18:53.5637744+00:00"}, "jfFlw7gjRLARQ4fxc7Lu1j2LmJDON3cNQQWii8s/nrQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-23T17:18:53.5659704+00:00"}, "9RKFuQXxvfS+CtfEgsQ5FDGW7mFH2C86GHTR/w5+VnI=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-23T17:18:53.5738478+00:00"}, "Meq5G1AXHV0Ma77q2/aYjrmswCSqMMQPLQqnqVEBwDY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-23T17:18:53.5869371+00:00"}, "7/pkIpCnZdJ++ihCMBroIdFMkEhXgH7GK9BhPjfqUJk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-23T17:18:53.5982652+00:00"}, "H+ygVgKW6oogV6nzpMbOpkV8z676fM2YtIQDZbaw0YQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-23T17:18:53.606035+00:00"}, "P7VhAXeV0FxkOT9h1Zq/RPOHwbsj1IuKFjN9Q0iFHoU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-23T17:18:53.5361633+00:00"}, "6DiO64XGtR3iteO0GNwZs+Hk42Nd+TgpTTLSaQtnIdE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-23T17:18:53.5431369+00:00"}, "hzE18DdV+3DflNVrfLvXBNUvlZHcO5xM+R0Rzfs9eOA=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-23T17:18:53.5506348+00:00"}, "lLRc+8dOMbVxta0HPToEaubrq1NY7mZlhu9fYWdYlb8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-23T17:18:53.5651962+00:00"}, "kcLbj1fCOXwqEfPJJdydrzInIp27RA7eQiaKpSB8YBo=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-23T17:18:53.5718343+00:00"}, "9N3Wm5BeQIRN9vekSDvaHWeJJpqlKowCMHsViu/DGF4=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-23T17:18:53.5753293+00:00"}, "43qn8NLuUO+KkW55+A1jwpw9p29hD9x4iFhAv48EtkY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-23T17:18:53.5820091+00:00"}, "Wk0KaevDAwlLzrm5ZNLRwk3GdJITPIxOgTffTWerGmw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\16x5hptwnm-wxukb43dng.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "css/site#[.{fingerprint=wxukb43dng}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hp601njcaa", "Integrity": "9a65+SejG8UWaTBBAjcCAVXmXZNFBgkZThPY3PS3LkY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\css\\site.css", "FileLength": 974, "LastWriteTime": "2025-07-23T17:18:53.5820091+00:00"}, "bbXWOY5vRjSl4JBLnivPhyeNnPJ7/GR+OR1QCZ6OiPw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\f2n19trgho-61n19gt1b8.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-23T17:18:53.5820091+00:00"}, "Xy9ZS/tkoe7l78eH7zNXZjU3umk0Kj2RpptsKSAgU54=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\4bguza2wwq-xtxxf3hu2r.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-23T17:18:53.5361633+00:00"}, "cgraZf9ZzkFeo60UjR2pK1N5UPs2EsFYh9b8o+wTMDI=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\gvreqwmawe-bqjiyaj88i.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-23T17:18:53.5550195+00:00"}, "pl3dltGwUzMbTnNAwsONh20KZMnEOj6dMQSIm9hXY7k=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\8vcgkrwmkl-c2jlpeoesf.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-23T17:18:53.5582295+00:00"}, "JZEgrVzntocEBTawu45zCWYaEzBtQ+fV3Qy5RaDoOVg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\v69uv9l2lh-erw9l3u2r3.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-23T17:18:53.5582295+00:00"}, "y5IwHbku2DrV8XgFg5nvSXhLVG3u+H/Txg8IzLq+u9g=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\8cyuw1473v-aexeepp0ev.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-23T17:18:53.5608999+00:00"}, "wGudYo4sIzIu5lZT23iEJAI13qdU8POlfvnOrdT0gmw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\x6nrwiogo5-d7shbmvgxk.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-23T17:18:53.5614928+00:00"}, "ipXLJMfh7oNNiqMOWMHaAWG2AIxm8Y9Tshmi8nsfWUo=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\28731eqxw8-ausgxo2sd3.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-23T17:18:53.5637744+00:00"}, "DEFB4mHTRqjuysl5IIpYeyxI916CY2OC1E/p/WaZ5WM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\9mdthi67dv-k8d9w2qqmf.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-23T17:18:53.5173778+00:00"}, "NiBtMN+KOyywm1Ca3NjVzXJShTA3NUr5KWJqod1bh14=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\g6qlq9wzep-cosvhxvwiu.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-23T17:18:53.5336057+00:00"}, "PszNRahmv0PBaF2f5JxT3ievV6sQT5AH2kHCRXqymwo=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\l1j5xbo07o-ub07r2b239.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-23T17:18:53.5341462+00:00"}, "bJD2l2TFhIMhoVOYQqKZoeddKF/EVDoeoJ/T8gBfpFA=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\gfkyhpalvi-fvhpjtyr6v.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-23T17:18:53.5361633+00:00"}, "OyWY9pPuEZGfkkg5fJwSEShyIlC4RAHUntZrz1JqJAo=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\t6p4p6n6yb-b7pk76d08c.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-23T17:18:53.5361633+00:00"}, "GBMkVHbPL+4+SYGZ/T1ldci69y6ihRgDTeb0EMvia4I=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\v9iccpy2kn-fsbi9cje9m.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-23T17:18:53.5415807+00:00"}, "89FAa3Umwwd7t0o2+nBqu8YtTeyi+HJbnPT49MFNO8c=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\7oe5vyyx9w-rzd6atqjts.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-23T17:18:53.5415807+00:00"}, "X1EfXCIgMeCxxGIbKTrJQLt6EutKvytJYAy+8kc/+4Q=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\qh82bmls2l-ee0r1s7dh0.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-23T17:18:53.5431369+00:00"}, "4jRxhjL6dB0A0fEbTW6M/rWkWtWw0EmgDcOjoCHILbE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\txactiogbi-dxx9fxp4il.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-23T17:18:53.5431369+00:00"}, "GLuS2neQsuM4tPAKVUgWyrmeY9KzhPxaF1FsL8cQ5Ck=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\1zmedvy5kz-jd9uben2k1.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-23T17:18:53.5460996+00:00"}, "sFHYLz8SW0cZU1e6D/USDvuQyypdwCp6BddVwpljtcg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\oznnqchpzv-khv3u5hwcm.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-23T17:18:53.5460996+00:00"}, "duywNEaHcKbiyjBUMCfcji9hqRXWIFti3wF/OwdFX9Y=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ssnfiv1yzk-r4e9w2rdcm.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-23T17:18:53.5341462+00:00"}, "gBaEpjgr/twSLM/l1/B7N3TGJTbyfQDeZKTdQa308dY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\loxz0wgd2r-lcd1t2u6c8.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-23T17:18:53.5361633+00:00"}, "x7eMPE2JuJKxnO08Zrmt7H9XjINsv33jljy5LykCv/E=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\7qdbtgp7hb-c2oey78nd0.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-23T17:18:53.5361633+00:00"}, "F09C6RxGQkbXD5HgUH8MTBynvkSBM3e05HMN78UWNBg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\p5smgi897r-tdbxkamptv.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-23T17:18:53.5415807+00:00"}, "LXqLXZMvRsKF4q9fgOPWnkMLju5rESSFt4RDrhY5Vts=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\3z0hydztvm-j5mq2jizvt.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-23T17:18:53.5460996+00:00"}, "LO6Exl53wPM3QJhizkKf3O08Ze2BniqMfWl1mqV86Gk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\mr8ot3pktr-06098lyss8.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-23T17:18:53.5550195+00:00"}, "qF35B5wCkCnj3St6Ek0YUK6JEWXAgJtJRWcvarN/UhM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\jrzceuklaq-nvvlpmu67g.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-23T17:18:53.61303+00:00"}, "QxxCb3GCIwwE9wbc935xkkng7NW+dk6CmVpDxyll0gk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\4rre1lw2rt-s35ty4nyc5.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-23T17:18:53.5550195+00:00"}, "0XKyrokDRuPvb6RFKKcSRuxipn5DhOkBlgyZmoKW8CU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\remhgu4vv1-pj5nd1wqec.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-23T17:18:53.5659704+00:00"}, "8LQaqHGqpppiKVxx/ZG0I6nKitqvD9v4fLhZojINDps=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\o1lnyx5mc6-46ein0sx1k.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-23T17:18:53.5659704+00:00"}, "0riiiCPOAsNksQdpT1CsPBDVkSTwf6xFHDAcbxuQquU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\u7u39a79im-v0zj4ognzu.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-23T17:18:53.5793936+00:00"}, "gctcUIt5s0xHBm9uJftmlqdINnunK+kmjqZxCOze/Vo=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\p9cvd16lt0-37tfw0ft22.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-23T17:18:53.5341462+00:00"}, "zgQDB/2mk4Vv96fTcfZHK5nbpIAmnShju5t+jPH4CqU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\mzimungl5e-hrwsygsryq.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-23T17:18:53.5431369+00:00"}, "dIXMIpBOFV7SDLVosU2ThsTG5iHLziysmOj3tLvzD44=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\7h6yj91wkq-pk9g2wxc8p.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-23T17:18:53.5460996+00:00"}, "VWags4167DOodxoQMdEPrwu11rW6UJ6jDlFvm8aBoSU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\r756wof0qh-ft3s53vfgj.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-23T17:18:53.5582295+00:00"}, "qHanQzlduSXXw8ypMhEFP6PR5hgG6YfES2PddWmAXOo=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\3m7f91nier-6cfz1n2cew.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-23T17:18:53.5626949+00:00"}, "l0nseBopaNpOOrSeT+AMEsglYGKbCNZscIkcWQ0BRUE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\jw5a92aq3t-6pdc2jztkx.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-23T17:18:53.5738478+00:00"}, "QA19TxfrAnn7mc8IkF8riKV/C9sktzE+1ZbEBct4KKw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\gyuwpau4zj-493y06b0oq.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-23T17:18:53.5753293+00:00"}, "b0usmgRmXK4pqmoMT9BfJv/khuPL16ZTkpFzuZlm5vY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\pb4x9icssc-iovd86k7lj.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-23T17:18:53.5727613+00:00"}, "cKthKhS8YQNpMPYBjCk8UlcwljnGi53xeHKCZWMbMYI=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\wru9w095ou-vr1egmr9el.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-23T17:18:53.5753293+00:00"}, "1RPlV3NC7koWCOmgEurXamqDYbWvHi2mood+s5cW3RI=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\sao04fkaau-kbrnm935zg.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-23T17:18:53.5820091+00:00"}, "ow2IDta4FFviIkLq51hZjBofsZEe6AttDTFxJhCC3wI=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\vzi8lrz6wj-jj8uyg4cgr.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-23T17:18:53.5982652+00:00"}, "CFgNHJr4KnV7/+s5BaxlsGKLNJ60DC7LxJ9x/JQWTMs=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\xxbtc3h5e2-y7v9cxd14o.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-23T17:18:53.5341462+00:00"}, "6erzp3xsCP8vxgUkipt57WHl5L4a6B/jqnP53dBbJBs=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\thvypom8t2-notf2xhcfb.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-23T17:18:53.5361633+00:00"}, "0jaDOvHNHnQo/lyAhFRuKGf4FyK3h5eo0HtNojfDCwI=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\471uhqs47y-h1s4sie4z3.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-23T17:18:53.5512236+00:00"}, "HN78frd6oB6SeAFDD9X17uqkece2DNehr0OV8AuoL9M=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\g52nsnploq-63fj8s7r0e.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-23T17:18:53.5869371+00:00"}, "FgYs1eN3HV/rOSm3npclnAzNOG7oNBfZ9y+jBLwdxWg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\jj2z1lxy1f-0j3bgjxly4.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-23T17:18:53.61303+00:00"}, "qR17oOc+A/lsSWmg3i8BTKpL5LGcodWKghsHGOYyP6I=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\b0wdyvoq0e-47otxtyo56.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-23T17:18:53.61303+00:00"}, "r/H7oLTCVP40cDwl492P+jnB8MALINGOOcRQvoXZltg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\mgsn4a7rqb-4v8eqarkd7.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-23T17:18:53.61303+00:00"}, "dZZuqW+DXrS/k5q2axpihOotf6ieF2zSxART89c4YIM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\o6xdbukn84-356vix0kms.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-23T17:18:53.5753293+00:00"}, "eH5ET2JL9ZIsQjmBkvglQj3zDi86bkWVi8P7tqe+Sjg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\za7ti5z8ew-83jwlth58m.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-23T17:18:53.5753293+00:00"}, "tcv77msrApgjM2DbKyaaeqZChDhiLNLqabqisa+iA7U=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\pimg4bcchp-mrlpezrjn3.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-23T17:18:53.5793936+00:00"}, "8rb0j0HsnVyaYRrYghNnBIekssX8lHkq/IonjH5ApQQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\dana2szpqu-lzl9nlhx6b.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-23T17:18:53.606035+00:00"}, "V5W/dHAWOebre1uN5K69PF8kHaAoOooSC6yIeMqbMbU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\okmrmed9t4-ag7o75518u.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-23T17:18:53.5173778+00:00"}, "4Ww3cfemwdAgsY/SaR2blAeNSjtRUuU/SPdwybDLa9U=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\52kpdtld7s-x0q3zqp4vz.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-23T17:18:53.5173778+00:00"}, "nTL5OsvooAV5u4UUdmgLF0tLOZVdLkBMNbXqMkv0k+k=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\bnuj1lyqi8-0i3buxo5is.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-23T17:18:53.5392467+00:00"}, "96iKNmN41RmJixg6XC5KI1Uk7SMphU9JxO/8P/8eOdU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\acz1cnk0ic-o1o13a6vjx.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-23T17:18:53.5431369+00:00"}, "PLt/9+99H2vBuO1KP7hhKhSfZaVWrInJSB/qTfy0LVM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\y7pt8v4sl0-ttgo8qnofa.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-23T17:18:53.5460996+00:00"}, "RTLf3mB/vATeYZMJQk8bXZDpsSHZYG22I+uFinxjqBM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\wxr78b97uf-2z0ns9nrw6.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-23T17:18:53.5626949+00:00"}, "pbabCm+S3KpIXxqb1CdPjPXS1bwwYe6F2xse+iwnqH8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\1bbela6ocw-muycvpuwrr.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-23T17:18:53.5637744+00:00"}, "iWCfbB//k5KlUFmB9h7b+kCTNWKtoSlrQ5F8AM64Eq0=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\vhe5vki0ai-87fc7y1x7t.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-23T17:18:53.5659704+00:00"}, "s6hz4XzGIhp10fpB/4jJ5wxxogNNB96o0WNmlQWonVk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\m99m346zva-mlv21k5csn.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-23T17:18:53.5659704+00:00"}, "r54/PNElI/PZKYKYWAU7CxODvw7oPrMx8ukg4IlQ+J0=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\668bouo4dg-aw1qx0tx3t.gz", "SourceId": "GocPho", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "GocPho#[.{fingerprint=aw1qx0tx3t}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\scopedcss\\bundle\\GocPho.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3sfm6ylns6", "Integrity": "nWi02AOeQbOmvfr1vnP/PEVT3GwKm/0oJDOvIaablPc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\scopedcss\\bundle\\GocPho.styles.css", "FileLength": 539, "LastWriteTime": "2025-07-23T17:18:53.5659704+00:00"}, "PR4fCRSZTieLi6mOJOiEvaQziu3sWKcYFsMy6rVqEW0=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ajdd70ldzy-aw1qx0tx3t.gz", "SourceId": "GocPho", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "GocPho#[.{fingerprint=aw1qx0tx3t}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\GocPho.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3sfm6ylns6", "Integrity": "nWi02AOeQbOmvfr1vnP/PEVT3GwKm/0oJDOvIaablPc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\GocPho.bundle.scp.css", "FileLength": 539, "LastWriteTime": "2025-07-23T17:18:53.5659704+00:00"}, "WQIhir6NgNXs85jzxIgrTadGY8Dqik3pPWlYBolsOEE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\o900pz65p5-76dwl5a5pz.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/coffee-cup#[.{fingerprint=76dwl5a5pz}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-cup.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "06dd28rakr", "Integrity": "6BVHiZz+z5YgCxGSTn8bfSlaktROMvapCFGUzA9OSao=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-cup.svg", "FileLength": 356, "LastWriteTime": "2025-07-23T17:18:53.61303+00:00"}, "fM05eLOCjIqhkbksOkcSN/tL4Tr8A1TnyzKaGspJBYQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\xhkei0fxtn-40h8kgmqbi.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/coffee-hero#[.{fingerprint=40h8kgmqbi}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-hero.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "occz3ru07s", "Integrity": "SDhge9l9/LsPuJl7+zUbNWmRzxCeVk0pRklyKCVjsAA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-hero.svg", "FileLength": 361, "LastWriteTime": "2025-07-23T17:18:53.61303+00:00"}, "MbcLFPbMGfmlpkIiPHlOypMnFfQ7gLPiNqid5jb6V/s=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\f8fht7pf4z-ybrhg4el0e.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/default-coffee#[.{fingerprint=ybrhg4el0e}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\default-coffee.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2m3i9wiy3f", "Integrity": "NlQVYx+EHBaM982sSSbDAzbxMJI56MLQzsgzRyV4ZI8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\default-coffee.svg", "FileLength": 349, "LastWriteTime": "2025-07-23T17:18:53.5982652+00:00"}, "X4BOxsV2WXLicbQonyT0aMCJ0jaDfAbSQLqb284dJrM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\fcrk7cv3ph-1n5x6omr1j.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/placeholder-generator#[.{fingerprint=1n5x6omr1j}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\placeholder-generator.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8mikg0yjy4", "Integrity": "QIuJ+vtMxApAT2gQtDS+Cq1Ba95iXhVG+fhRf3Orhj8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\placeholder-generator.html", "FileLength": 664, "LastWriteTime": "2025-07-23T17:18:53.5361633+00:00"}, "xFNDsPbg3aAtj08uLUIIBwjXAGNvx+Om7DRg1ErhlHo=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\fuu1oae2tc-gobymrk17e.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/bac-xiu#[.{fingerprint=gobymrk17e}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\bac-xiu.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q2mudwsy9c", "Integrity": "QrhqkB+GPsx2hYs8TCseBVtxmmdsqpXe4ZbQEy3LDYg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\bac-xiu.svg", "FileLength": 617, "LastWriteTime": "2025-07-23T17:18:53.5977212+00:00"}, "MBCLkcbZmZkaSxplDGTlhVQGLGS1hYLjg2Kpv0ShvlE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\6c0sv7su9y-gllmg658ug.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/ca-phe-den-da#[.{fingerprint=gllmg658ug}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\ca-phe-den-da.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7fldkgrbw", "Integrity": "Of3tWH/Gp/Qx/p0+Jo28sfGIkSIppiIhByEEjQSXBxE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\ca-phe-den-da.svg", "FileLength": 543, "LastWriteTime": "2025-07-23T17:18:53.5173778+00:00"}, "O8Sxy4OYxunyFSe32CKQQFsi7WTUlaevHOn0JZHbDRM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\7uud018py2-ufaql7fegv.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/ca-phe-sua-da#[.{fingerprint=ufaql7fegv}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\ca-phe-sua-da.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x9txfpaaag", "Integrity": "daWx9VTwRml1RtxQdq9o6V451Dd/GbYCDegCxgBTLU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\ca-phe-sua-da.svg", "FileLength": 623, "LastWriteTime": "2025-07-23T17:18:53.5341462+00:00"}, "1c1XAovvVvZ9FbaGvH3y9lBobMrbBmhhkl+kPxqpTbI=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ohr6whz1i7-k4qxzznt7v.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/coffee-beans#[.{fingerprint=k4qxzznt7v}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-beans.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2fzo05wfz7", "Integrity": "skLCbTAzp76+sK5pUUlyn5loE3fbxjV+gKlXMRGnLZs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-beans.svg", "FileLength": 555, "LastWriteTime": "2025-07-23T17:18:53.61303+00:00"}, "/1+o4U82bDB8OHnUQtvQLwXNG35A7ReiGKDcOZ5jlQE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\hgx7ek3gn4-ih0w9tino9.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/coffee-shop-interior#[.{fingerprint=ih0w9tino9}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-shop-interior.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "guxsjs6w8i", "Integrity": "qCJ4ofKth/ufGLPNL3O3k6wqKeDnXWi4+WiUMBdBImQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-shop-interior.svg", "FileLength": 516, "LastWriteTime": "2025-07-23T17:18:53.5982652+00:00"}, "rPnjJUeYNhUWaxe3BiQuKXHRdoR1htld7k1gof2XNco=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\04cqiqls0z-3g0w087goj.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/americano#[.{fingerprint=3g0w087goj}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\americano.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jmdn6o8qsy", "Integrity": "u8rIH2IyMTM2k65JjVXztc70oY6qO8L+g/XdfnecrFE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\americano.svg", "FileLength": 1059, "LastWriteTime": "2025-07-23T17:18:53.5820091+00:00"}, "CP/xJt8geVOJjyKBCui6ruqy5Nms0/YeXlye1Tr2VeY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\d5pwmky542-uvti8ctbdn.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/cappuccino#[.{fingerprint=uvti8ctbdn}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\cappuccino.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bc7o1lqwn8", "Integrity": "K/CAMusPmkV/cAo1bZnJG7riaOURAaKVEUO+xMDUFE8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\cappuccino.svg", "FileLength": 868, "LastWriteTime": "2025-07-23T17:18:53.5392467+00:00"}, "fmw5fcEbek70AvIhoSwMaBrdop029cRw5J5LwIPm1DE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\xjtwsidlzm-w3565h96xy.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/frappuccino#[.{fingerprint=w3565h96xy}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\frappuccino.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w8vzqqucfw", "Integrity": "8GqGPufJY1U76UcwJy67jry6FmiUPQnW2pL2g1rsHv0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\frappuccino.svg", "FileLength": 1161, "LastWriteTime": "2025-07-23T17:18:53.5982652+00:00"}, "pfpicPWJHnE3v4aB7Quvve0dyPCjEqpQrX0iRdBjTVU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\jcg7mnvyuj-g2658t4jld.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/coffee-images#[.{fingerprint=g2658t4jld}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-images.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bimagx5imf", "Integrity": "jWuxfLah5T7Kzb2/6TOHikg6p1BFYPqkCn8fs/EYzjI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-images.html", "FileLength": 965, "LastWriteTime": "2025-07-23T17:18:53.61303+00:00"}, "ZEek9+wQpC9pAqoBTs1ME9TC6MDwOIyZDw7LnPnu+hU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\se8m553ywj-jwr2x1kwxo.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/espresso#[.{fingerprint=jwr2x1kwxo}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\espresso.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "myucw81ab3", "Integrity": "vveGlLEl3bRoQZYZFwLg2eO+7VAr/nTniWqsZ6qGbFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\espresso.svg", "FileLength": 879, "LastWriteTime": "2025-07-23T17:18:53.5982652+00:00"}, "zIT8Scz/ewERsoCwTH959qXv6/lKC4FW9E3/X3RIj78=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\0atamb2oz8-kkkj4824pj.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/latte#[.{fingerprint=kkkj4824pj}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\latte.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y7m855ipas", "Integrity": "giR3H5cl0sKOG23goPxKeYMNHlLXrPLPgVWKc+7kIMw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\latte.svg", "FileLength": 1116, "LastWriteTime": "2025-07-23T17:18:53.5173778+00:00"}, "j1cfpsXq26Gwy8wwVuRn2pBkLV5Zu0zrqTYSxDNTjDk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\pjebks0n4i-cax4sk882a.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/menu-banner#[.{fingerprint=cax4sk882a}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\menu-banner.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tqfhe10c3m", "Integrity": "9dsTocTF295OMxgGeJU08G8dQAC6e2cuKAqL76+pknE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\menu-banner.svg", "FileLength": 1113, "LastWriteTime": "2025-07-23T17:18:53.5173778+00:00"}, "mATyHxcRoDnYOEL2TuN0kJM5i4GCgbnoqWdmkGwsD/Y=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\7x2rtw26is-ce5aaz74y5.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/mocha#[.{fingerprint=ce5aaz74y5}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\mocha.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9m5sgl3z8k", "Integrity": "j4AKeGyH5wOqSFdJG1jDS5Jk3T35HPXdbNf9cHbRNIw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\mocha.svg", "FileLength": 1159, "LastWriteTime": "2025-07-23T17:18:53.5341462+00:00"}}, "CachedCopyCandidates": {}}