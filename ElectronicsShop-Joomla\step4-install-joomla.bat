@echo off
title Step 4 - Install Joomla
color 0E

echo ========================================
echo    STEP 4: INSTALL JOOMLA
echo ========================================
echo.

echo Checking XAMPP htdocs directory...
if exist "C:\xampp\htdocs" (
    echo [OK] XAMPP htdocs found
) else (
    echo [ERROR] XAMPP htdocs not found!
    echo Please make sure XAMPP is installed correctly.
    pause
    exit
)

echo.
echo ========================================
echo    DOWNLOADING JOOMLA
echo ========================================
echo.

echo Starting Joomla download...
powershell -ExecutionPolicy Bypass -File "download-joomla-simple.ps1"

echo.
echo Press any key when Joomla download is complete...
pause

echo ========================================
echo    CHECKING JOOMLA INSTALLATION
echo ========================================
echo.

if exist "C:\xampp\htdocs\electronics-shop\index.php" (
    echo [OK] Joomla files found!
    echo Location: C:\xampp\htdocs\electronics-shop\
) else (
    echo [WARNING] Joomla files not found automatically.
    echo.
    echo MANUAL INSTALLATION STEPS:
    echo 1. Download Joomla from: https://www.joomla.org/download.html
    echo 2. Extract to: C:\xampp\htdocs\electronics-shop\
    echo 3. Make sure index.php exists in that folder
    echo.
    echo Press any key when manual installation is done...
    pause
)

echo.
echo ========================================
echo    STARTING JOOMLA SETUP
echo ========================================
echo.

echo Opening Joomla installation wizard...
start "" "http://localhost/electronics-shop"

echo.
echo JOOMLA SETUP WIZARD INSTRUCTIONS:
echo =================================
echo.
echo STEP 1 - CONFIGURATION:
echo ------------------------
echo Site Name: Electronics Shop - Thiet Bi Dien Tu Cu
echo Site Description: Website ban thiet bi dien tu cu uy tin
echo Admin Email: <EMAIL>
echo Admin Username: admin
echo Admin Password: admin123
echo Confirm Password: admin123
echo.
echo STEP 2 - DATABASE:
echo ------------------
echo Database Type: MySQLi
echo Host Name: localhost
echo Username: root
echo Password: (leave empty)
echo Database Name: electronics_shop
echo Table Prefix: jos_
echo.
echo STEP 3 - OVERVIEW:
echo ------------------
echo Install Sample Data: Blog Sample Data
echo Email Configuration: Yes
echo.
echo Click INSTALL and wait for completion
echo.
echo Press any key when Joomla installation is complete...
pause

echo ========================================
echo    TESTING JOOMLA INSTALLATION
echo ========================================
echo.

echo Testing Joomla frontend...
start "" "http://localhost/electronics-shop"

timeout /t 3 /nobreak >nul

echo Testing Joomla admin panel...
start "" "http://localhost/electronics-shop/administrator"

echo.
echo You should now see:
echo 1. Joomla website frontend
echo 2. Joomla admin login page
echo.
echo Login credentials:
echo Username: admin
echo Password: admin123
echo.
echo Press any key when you can access both pages...
pause

echo ========================================
echo    STEP 4 COMPLETE!
echo ========================================
echo.
echo [SUCCESS] Joomla is installed and running!
echo.
echo Your website URLs:
echo - Frontend: http://localhost/electronics-shop
echo - Admin Panel: http://localhost/electronics-shop/administrator
echo.
echo Login credentials:
echo - Username: admin
echo - Password: admin123
echo.
echo Next step: Install VirtueMart for e-commerce
echo.
echo Press any key to continue to Step 5...
pause
