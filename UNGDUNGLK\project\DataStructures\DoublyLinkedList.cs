using DoublyLinkedListApp.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DoublyLinkedListApp.DataStructures
{
    /// <summary>
    /// Danh sách liên kết kép để quản lý dữ liệu hàng hóa
    /// </summary>
    public class DoublyLinkedList
    {
        private DoublyLinkedListNode? head;
        private DoublyLinkedListNode? tail;
        private int count;

        public int Count => count;
        public bool IsEmpty => head == null;

        public DoublyLinkedList()
        {
            head = null;
            tail = null;
            count = 0;
        }

        /// <summary>
        /// Thêm phần tử vào đầu danh sách
        /// </summary>
        public void AddFirst(HangHoa data)
        {
            var newNode = new DoublyLinkedListNode(data);

            if (IsEmpty)
            {
                head = tail = newNode;
            }
            else
            {
                newNode.Next = head;
                head!.Previous = newNode;
                head = newNode;
            }
            count++;
        }

        /// <summary>
        /// Thêm phần tử vào cuối danh sách
        /// </summary>
        public void AddLast(HangHoa data)
        {
            var newNode = new DoublyLinkedListNode(data);

            if (IsEmpty)
            {
                head = tail = newNode;
            }
            else
            {
                tail!.Next = newNode;
                newNode.Previous = tail;
                tail = newNode;
            }
            count++;
        }

        /// <summary>
        /// Thêm phần tử vào vị trí chỉ định
        /// </summary>
        public void AddAt(int index, HangHoa data)
        {
            if (index < 0 || index > count)
                throw new ArgumentOutOfRangeException(nameof(index));

            if (index == 0)
            {
                AddFirst(data);
                return;
            }

            if (index == count)
            {
                AddLast(data);
                return;
            }

            var newNode = new DoublyLinkedListNode(data);
            var current = GetNodeAt(index);

            newNode.Next = current;
            newNode.Previous = current!.Previous;
            current.Previous!.Next = newNode;
            current.Previous = newNode;

            count++;
        }

        /// <summary>
        /// Xóa phần tử đầu tiên
        /// </summary>
        public bool RemoveFirst()
        {
            if (IsEmpty) return false;

            if (count == 1)
            {
                head = tail = null;
            }
            else
            {
                head = head!.Next;
                head!.Previous = null;
            }
            count--;
            return true;
        }

        /// <summary>
        /// Xóa phần tử cuối cùng
        /// </summary>
        public bool RemoveLast()
        {
            if (IsEmpty) return false;

            if (count == 1)
            {
                head = tail = null;
            }
            else
            {
                tail = tail!.Previous;
                tail!.Next = null;
            }
            count--;
            return true;
        }

        /// <summary>
        /// Xóa phần tử tại vị trí chỉ định
        /// </summary>
        public bool RemoveAt(int index)
        {
            if (index < 0 || index >= count) return false;

            if (index == 0) return RemoveFirst();
            if (index == count - 1) return RemoveLast();

            var nodeToRemove = GetNodeAt(index);
            nodeToRemove!.Previous!.Next = nodeToRemove.Next;
            nodeToRemove.Next!.Previous = nodeToRemove.Previous;

            count--;
            return true;
        }

        /// <summary>
        /// Xóa phần tử theo mã số
        /// </summary>
        public bool Remove(string maSo)
        {
            var current = head;
            while (current != null)
            {
                if (current.Data.MaSo.Equals(maSo, StringComparison.OrdinalIgnoreCase))
                {
                    if (current == head && current == tail)
                    {
                        head = tail = null;
                    }
                    else if (current == head)
                    {
                        head = current.Next;
                        head!.Previous = null;
                    }
                    else if (current == tail)
                    {
                        tail = current.Previous;
                        tail!.Next = null;
                    }
                    else
                    {
                        current.Previous!.Next = current.Next;
                        current.Next!.Previous = current.Previous;
                    }
                    count--;
                    return true;
                }
                current = current.Next;
            }
            return false;
        }

        /// <summary>
        /// Tìm kiếm theo mã số
        /// </summary>
        public HangHoa? Find(string maSo)
        {
            var current = head;
            while (current != null)
            {
                if (current.Data.MaSo.Equals(maSo, StringComparison.OrdinalIgnoreCase))
                {
                    return current.Data;
                }
                current = current.Next;
            }
            return null;
        }

        /// <summary>
        /// Lấy node tại vị trí chỉ định
        /// </summary>
        private DoublyLinkedListNode? GetNodeAt(int index)
        {
            if (index < 0 || index >= count) return null;

            DoublyLinkedListNode? current;
            if (index < count / 2)
            {
                current = head;
                for (int i = 0; i < index; i++)
                {
                    current = current!.Next;
                }
            }
            else
            {
                current = tail;
                for (int i = count - 1; i > index; i--)
                {
                    current = current!.Previous;
                }
            }
            return current;
        }

        /// <summary>
        /// Lấy phần tử tại vị trí chỉ định
        /// </summary>
        public HangHoa? GetAt(int index)
        {
            var node = GetNodeAt(index);
            return node?.Data;
        }

        /// <summary>
        /// Chuyển đổi thành danh sách
        /// </summary>
        public List<HangHoa> ToList()
        {
            var result = new List<HangHoa>();
            var current = head;
            while (current != null)
            {
                result.Add(current.Data);
                current = current.Next;
            }
            return result;
        }

        /// <summary>
        /// Xóa tất cả phần tử
        /// </summary>
        public void Clear()
        {
            head = tail = null;
            count = 0;
        }

        /// <summary>
        /// Kiểm tra danh sách có chứa phần tử không
        /// </summary>
        public bool Contains(string maSo)
        {
            return Find(maSo) != null;
        }

        /// <summary>
        /// Cập nhật thông tin hàng hóa
        /// </summary>
        public bool Update(string maSo, HangHoa newData)
        {
            var current = head;
            while (current != null)
            {
                if (current.Data.MaSo.Equals(maSo, StringComparison.OrdinalIgnoreCase))
                {
                    current.Data = newData;
                    return true;
                }
                current = current.Next;
            }
            return false;
        }
    }
}
