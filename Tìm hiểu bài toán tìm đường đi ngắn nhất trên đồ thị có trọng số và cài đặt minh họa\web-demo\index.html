<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo <PERSON>hu<PERSON>t Tìm <PERSON>ng <PERSON></title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1><i class="fas fa-route"></i> Demo Giải Thuật Tìm Đư<PERSON></h1>
            <p><PERSON> họa trự<PERSON> quan các g<PERSON><PERSON><PERSON><PERSON>, <PERSON>man<PERSON><PERSON> và <PERSON>-<PERSON></p>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="container">
            <div class="nav-tabs">
                <button class="nav-tab active" data-tab="dijkstra">
                    <i class="fas fa-map-marked-alt"></i> Dijkstra
                </button>
                <button class="nav-tab" data-tab="bellman-ford">
                    <i class="fas fa-project-diagram"></i> Bellman-Ford
                </button>
                <button class="nav-tab" data-tab="floyd-warshall">
                    <i class="fas fa-network-wired"></i> Floyd-Warshall
                </button>
                <button class="nav-tab" data-tab="comparison">
                    <i class="fas fa-chart-bar"></i> So Sánh
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Dijkstra Tab -->
            <div id="dijkstra" class="tab-content active">
                <div class="algorithm-section">
                    <div class="section-header">
                        <h2><i class="fas fa-map-marked-alt"></i> Giải Thuật Dijkstra</h2>
                        <p>Tìm đường đi ngắn nhất từ một đỉnh nguồn đến tất cả các đỉnh khác (SSSP)</p>
                    </div>

                    <div class="demo-container">
                        <div class="graph-panel">
                            <div class="panel-header">
                                <h3>Đồ Thị Minh Họa</h3>
                                <div class="controls">
                                    <select id="dijkstra-source" class="source-select">
                                        <option value="0">Đỉnh 0</option>
                                        <option value="1">Đỉnh 1</option>
                                        <option value="2">Đỉnh 2</option>
                                        <option value="3">Đỉnh 3</option>
                                        <option value="4">Đỉnh 4</option>
                                        <option value="5">Đỉnh 5</option>
                                    </select>
                                    <button id="dijkstra-run" class="btn btn-primary">
                                        <i class="fas fa-play"></i> Chạy Dijkstra
                                    </button>
                                    <button id="dijkstra-reset" class="btn btn-secondary">
                                        <i class="fas fa-undo"></i> Reset
                                    </button>
                                </div>
                            </div>
                            <canvas id="dijkstra-canvas" width="600" height="400"></canvas>
                        </div>

                        <div class="info-panel">
                            <div class="algorithm-info">
                                <h3>Thông Tin Giải Thuật</h3>
                                <div class="info-grid">
                                    <div class="info-item">
                                        <span class="label">Độ phức tạp:</span>
                                        <span class="value">O((V+E) log V)</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">Loại bài toán:</span>
                                        <span class="value">SSSP</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">Trọng số âm:</span>
                                        <span class="value">Không</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">Chiến lược:</span>
                                        <span class="value">Greedy</span>
                                    </div>
                                </div>
                            </div>

                            <div class="results-panel">
                                <h3>Kết Quả</h3>
                                <div id="dijkstra-results" class="results-table">
                                    <div class="table-header">
                                        <span>Đỉnh</span>
                                        <span>Khoảng Cách</span>
                                        <span>Đường Đi</span>
                                    </div>
                                    <div id="dijkstra-table-body" class="table-body">
                                        <!-- Results will be populated here -->
                                    </div>
                                </div>
                            </div>

                            <div class="steps-panel">
                                <h3>Các Bước Thực Hiện</h3>
                                <div id="dijkstra-steps" class="steps-list">
                                    <!-- Steps will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="theory-section">
                        <h3>Lý Thuyết</h3>
                        <div class="theory-content">
                            <div class="theory-item">
                                <h4>Nguyên Lý Hoạt Động</h4>
                                <p>Dijkstra sử dụng chiến lược tham lam (greedy): luôn chọn đỉnh có khoảng cách ngắn nhất chưa được xử lý để mở rộng.</p>
                                <ul>
                                    <li>Khởi tạo khoảng cách từ nguồn = 0, các đỉnh khác = ∞</li>
                                    <li>Sử dụng priority queue để chọn đỉnh có khoảng cách nhỏ nhất</li>
                                    <li>Thực hiện thư giãn (relaxation) cho các đỉnh kề</li>
                                    <li>Lặp lại cho đến khi xử lý hết tất cả đỉnh</li>
                                </ul>
                            </div>
                            <div class="theory-item">
                                <h4>Ứng Dụng Thực Tế</h4>
                                <ul>
                                    <li><strong>GPS Navigation:</strong> Tìm đường đi ngắn nhất trong hệ thống giao thông</li>
                                    <li><strong>Network Routing:</strong> Định tuyến gói tin trong mạng máy tính</li>
                                    <li><strong>Game AI:</strong> Pathfinding cho nhân vật trong game</li>
                                    <li><strong>Social Networks:</strong> Tìm mức độ kết nối giữa người dùng</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bellman-Ford Tab -->
            <div id="bellman-ford" class="tab-content">
                <div class="algorithm-section">
                    <div class="section-header">
                        <h2><i class="fas fa-project-diagram"></i> Giải Thuật Bellman-Ford</h2>
                        <p>Xử lý đồ thị có trọng số âm và phát hiện chu trình âm</p>
                    </div>

                    <div class="demo-container">
                        <div class="graph-panel">
                            <div class="panel-header">
                                <h3>Đồ Thị Có Trọng Số Âm</h3>
                                <div class="controls">
                                    <select id="bellman-source" class="source-select">
                                        <option value="0">Đỉnh 0</option>
                                        <option value="1">Đỉnh 1</option>
                                        <option value="2">Đỉnh 2</option>
                                        <option value="3">Đỉnh 3</option>
                                        <option value="4">Đỉnh 4</option>
                                    </select>
                                    <button id="bellman-run" class="btn btn-primary">
                                        <i class="fas fa-play"></i> Chạy Bellman-Ford
                                    </button>
                                    <button id="bellman-reset" class="btn btn-secondary">
                                        <i class="fas fa-undo"></i> Reset
                                    </button>
                                    <button id="bellman-toggle-negative" class="btn btn-warning">
                                        <i class="fas fa-exclamation-triangle"></i> Tạo Chu Trình Âm
                                    </button>
                                </div>
                            </div>
                            <canvas id="bellman-canvas" width="600" height="400"></canvas>
                        </div>

                        <div class="info-panel">
                            <div class="algorithm-info">
                                <h3>Thông Tin Giải Thuật</h3>
                                <div class="info-grid">
                                    <div class="info-item">
                                        <span class="label">Độ phức tạp:</span>
                                        <span class="value">O(VE)</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">Loại bài toán:</span>
                                        <span class="value">SSSP</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">Trọng số âm:</span>
                                        <span class="value">Có</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">Chu trình âm:</span>
                                        <span class="value">Phát hiện</span>
                                    </div>
                                </div>
                            </div>

                            <div class="results-panel">
                                <h3>Kết Quả</h3>
                                <div id="bellman-status" class="status-indicator">
                                    <span class="status-text">Chưa chạy</span>
                                </div>
                                <div id="bellman-results" class="results-table">
                                    <div class="table-header">
                                        <span>Đỉnh</span>
                                        <span>Khoảng Cách</span>
                                        <span>Đường Đi</span>
                                    </div>
                                    <div id="bellman-table-body" class="table-body">
                                        <!-- Results will be populated here -->
                                    </div>
                                </div>
                            </div>

                            <div class="iterations-panel">
                                <h3>Các Lần Lặp</h3>
                                <div id="bellman-iterations" class="iterations-list">
                                    <!-- Iterations will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="theory-section">
                        <h3>Lý Thuyết</h3>
                        <div class="theory-content">
                            <div class="theory-item">
                                <h4>Nguyên Lý Hoạt Động</h4>
                                <p>Bellman-Ford sử dụng quy hoạch động và nguyên lý thư giãn để dần cải thiện ước lượng khoảng cách.</p>
                                <ul>
                                    <li>Thực hiện V-1 lần lặp thư giãn tất cả các cạnh</li>
                                    <li>Mỗi lần lặp cải thiện ước lượng khoảng cách</li>
                                    <li>Lần lặp thứ V kiểm tra chu trình âm</li>
                                    <li>Có thể xử lý trọng số âm một cách an toàn</li>
                                </ul>
                            </div>
                            <div class="theory-item">
                                <h4>Ứng Dụng Thực Tế</h4>
                                <ul>
                                    <li><strong>Financial Markets:</strong> Phát hiện cơ hội arbitrage</li>
                                    <li><strong>Network Protocols:</strong> Routing với chi phí âm (ưu đãi)</li>
                                    <li><strong>Game Theory:</strong> Phân tích chiến lược tối ưu</li>
                                    <li><strong>Currency Exchange:</strong> Tìm chu trình lãi suất âm</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Floyd-Warshall Tab -->
            <div id="floyd-warshall" class="tab-content">
                <div class="algorithm-section">
                    <div class="section-header">
                        <h2><i class="fas fa-network-wired"></i> Giải Thuật Floyd-Warshall</h2>
                        <p>Tìm đường đi ngắn nhất giữa tất cả các cặp đỉnh (APSP)</p>
                    </div>

                    <div class="demo-container">
                        <div class="graph-panel">
                            <div class="panel-header">
                                <h3>Ma Trận Khoảng Cách</h3>
                                <div class="controls">
                                    <button id="floyd-run" class="btn btn-primary">
                                        <i class="fas fa-play"></i> Chạy Floyd-Warshall
                                    </button>
                                    <button id="floyd-reset" class="btn btn-secondary">
                                        <i class="fas fa-undo"></i> Reset
                                    </button>
                                    <button id="floyd-step" class="btn btn-info">
                                        <i class="fas fa-step-forward"></i> Từng Bước
                                    </button>
                                </div>
                            </div>
                            <div class="matrix-container">
                                <div id="floyd-matrix" class="distance-matrix">
                                    <!-- Matrix will be populated here -->
                                </div>
                            </div>
                        </div>

                        <div class="info-panel">
                            <div class="algorithm-info">
                                <h3>Thông Tin Giải Thuật</h3>
                                <div class="info-grid">
                                    <div class="info-item">
                                        <span class="label">Độ phức tạp:</span>
                                        <span class="value">O(V³)</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">Loại bài toán:</span>
                                        <span class="value">APSP</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">Trọng số âm:</span>
                                        <span class="value">Có</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">Chiến lược:</span>
                                        <span class="value">Dynamic Programming</span>
                                    </div>
                                </div>
                            </div>

                            <div class="progress-panel">
                                <h3>Tiến Trình</h3>
                                <div id="floyd-progress" class="progress-info">
                                    <div class="progress-bar">
                                        <div id="floyd-progress-fill" class="progress-fill"></div>
                                    </div>
                                    <div id="floyd-progress-text" class="progress-text">Chưa bắt đầu</div>
                                </div>
                                <div id="floyd-current-k" class="current-step">
                                    Đỉnh trung gian: <span>-</span>
                                </div>
                            </div>

                            <div class="path-finder">
                                <h3>Tìm Đường Đi</h3>
                                <div class="path-controls">
                                    <select id="floyd-from" class="path-select">
                                        <option value="0">Từ đỉnh 0</option>
                                        <option value="1">Từ đỉnh 1</option>
                                        <option value="2">Từ đỉnh 2</option>
                                        <option value="3">Từ đỉnh 3</option>
                                    </select>
                                    <select id="floyd-to" class="path-select">
                                        <option value="0">Đến đỉnh 0</option>
                                        <option value="1">Đến đỉnh 1</option>
                                        <option value="2">Đến đỉnh 2</option>
                                        <option value="3">Đến đỉnh 3</option>
                                    </select>
                                    <button id="floyd-find-path" class="btn btn-success">
                                        <i class="fas fa-search"></i> Tìm Đường
                                    </button>
                                </div>
                                <div id="floyd-path-result" class="path-result">
                                    <!-- Path result will be shown here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="theory-section">
                        <h3>Lý Thuyết</h3>
                        <div class="theory-content">
                            <div class="theory-item">
                                <h4>Nguyên Lý Hoạt Động</h4>
                                <p>Floyd-Warshall sử dụng quy hoạch động với ý tưởng: xét từng đỉnh trung gian k, cập nhật đường đi ngắn nhất giữa mọi cặp đỉnh (i,j) thông qua k.</p>
                                <div class="formula">
                                    <code>d[i][j] = min(d[i][j], d[i][k] + d[k][j])</code>
                                </div>
                            </div>
                            <div class="theory-item">
                                <h4>Ứng Dụng Thực Tế</h4>
                                <ul>
                                    <li><strong>Network Analysis:</strong> Phân tích mạng xã hội và internet</li>
                                    <li><strong>Transportation:</strong> Tối ưu hóa hệ thống giao thông công cộng</li>
                                    <li><strong>Logistics:</strong> Lập kế hoạch vận chuyển đa điểm</li>
                                    <li><strong>Bioinformatics:</strong> Phân tích chuỗi DNA và protein</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Comparison Tab -->
            <div id="comparison" class="tab-content">
                <div class="algorithm-section">
                    <div class="section-header">
                        <h2><i class="fas fa-chart-bar"></i> So Sánh Các Giải Thuật</h2>
                        <p>Phân tích và so sánh hiệu quả của các giải thuật</p>
                    </div>

                    <div class="comparison-container">
                        <div class="comparison-table-container">
                            <h3>Bảng So Sánh Tổng Quan</h3>
                            <table class="comparison-table">
                                <thead>
                                    <tr>
                                        <th>Tiêu Chí</th>
                                        <th>Dijkstra</th>
                                        <th>Bellman-Ford</th>
                                        <th>Floyd-Warshall</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Độ phức tạp thời gian</td>
                                        <td class="good">O((V+E) log V)</td>
                                        <td class="average">O(VE)</td>
                                        <td class="poor">O(V³)</td>
                                    </tr>
                                    <tr>
                                        <td>Độ phức tạp không gian</td>
                                        <td class="good">O(V)</td>
                                        <td class="good">O(V)</td>
                                        <td class="poor">O(V²)</td>
                                    </tr>
                                    <tr>
                                        <td>Loại bài toán</td>
                                        <td>SSSP</td>
                                        <td>SSSP</td>
                                        <td>APSP</td>
                                    </tr>
                                    <tr>
                                        <td>Trọng số âm</td>
                                        <td class="poor">Không</td>
                                        <td class="good">Có</td>
                                        <td class="good">Có</td>
                                    </tr>
                                    <tr>
                                        <td>Phát hiện chu trình âm</td>
                                        <td class="poor">Không</td>
                                        <td class="good">Có</td>
                                        <td class="good">Có</td>
                                    </tr>
                                    <tr>
                                        <td>Dễ cài đặt</td>
                                        <td class="average">Trung bình</td>
                                        <td class="good">Dễ</td>
                                        <td class="good">Dễ</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="performance-charts">
                            <h3>Biểu Đồ Hiệu Suất</h3>
                            <div class="charts-container">
                                <div class="chart-item">
                                    <h4>Thời Gian Thực Thi (ms)</h4>
                                    <canvas id="performance-chart" width="400" height="300"></canvas>
                                </div>
                                <div class="chart-item">
                                    <h4>Sử Dụng Bộ Nhớ (MB)</h4>
                                    <canvas id="memory-chart" width="400" height="300"></canvas>
                                </div>
                            </div>
                        </div>

                        <div class="recommendation-panel">
                            <h3>Hướng Dẫn Lựa Chọn</h3>
                            <div class="recommendations">
                                <div class="recommendation-item">
                                    <div class="recommendation-header">
                                        <i class="fas fa-map-marked-alt"></i>
                                        <h4>Sử dụng Dijkstra khi:</h4>
                                    </div>
                                    <ul>
                                        <li>Đồ thị có trọng số không âm</li>
                                        <li>Cần tìm SSSP với hiệu suất cao</li>
                                        <li>Đồ thị lớn và thưa</li>
                                        <li>Ứng dụng real-time (GPS, game)</li>
                                    </ul>
                                </div>
                                <div class="recommendation-item">
                                    <div class="recommendation-header">
                                        <i class="fas fa-project-diagram"></i>
                                        <h4>Sử dụng Bellman-Ford khi:</h4>
                                    </div>
                                    <ul>
                                        <li>Đồ thị có trọng số âm</li>
                                        <li>Cần phát hiện chu trình âm</li>
                                        <li>Tính chính xác quan trọng hơn hiệu suất</li>
                                        <li>Ứng dụng tài chính, game theory</li>
                                    </ul>
                                </div>
                                <div class="recommendation-item">
                                    <div class="recommendation-header">
                                        <i class="fas fa-network-wired"></i>
                                        <h4>Sử dụng Floyd-Warshall khi:</h4>
                                    </div>
                                    <ul>
                                        <li>Cần tìm APSP</li>
                                        <li>Đồ thị nhỏ (V < 1000)</li>
                                        <li>Cần ma trận khoảng cách đầy đủ</li>
                                        <li>Phân tích mạng, logistics</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>Sinh viên: Lê Đức Tài - MSSV: 170123432 - Lớp: DX23TT11 - Email: <EMAIL></p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="algorithms.js"></script>
    <script src="visualization.js"></script>
    <script src="main.js"></script>
</body>
</html>
