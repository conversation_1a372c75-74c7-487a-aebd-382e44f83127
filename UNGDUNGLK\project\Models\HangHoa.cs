using System;

namespace DoublyLinkedListApp.Models
{
    /// <summary>
    /// Lớp đại diện cho thông tin hàng hóa
    /// </summary>
    public class HangHoa
    {
        public string MaSo { get; set; }
        public string TenHang { get; set; }
        public string DonViTinh { get; set; }
        public decimal DonGia { get; set; }
        public int SoLuong { get; set; }

        public HangHoa()
        {
            MaSo = string.Empty;
            TenHang = string.Empty;
            DonViTinh = string.Empty;
        }

        public HangHoa(string maSo, string tenHang, string donViTinh, decimal donGia, int soLuong)
        {
            MaSo = maSo;
            TenHang = tenHang;
            DonViTinh = donViTinh;
            DonGia = donGia;
            SoLuong = soLuong;
        }

        /// <summary>
        /// Tính thành tiền
        /// </summary>
        public decimal ThanhTien => DonGia * SoLuong;

        /// <summary>
        /// Trạng thái hàng hóa dựa trên số l<PERSON>ợ<PERSON>
        /// </summary>
        public string TrangThai
        {
            get
            {
                if (SoLuong == 0) return "Hết hàng";
                if (SoLuong <= 10) return "Sắp hết";
                return "Còn hàng";
            }
        }

        /// <summary>
        /// Phân loại giá
        /// </summary>
        public string PhanLoaiGia
        {
            get
            {
                if (DonGia >= 10000000) return "Cao cấp";
                if (DonGia >= 1000000) return "Trung cấp";
                if (DonGia >= 100000) return "Bình dân";
                return "Giá rẻ";
            }
        }

        public override string ToString()
        {
            return $"Mã: {MaSo}, Tên: {TenHang}, ĐVT: {DonViTinh}, Giá: {DonGia:C}, SL: {SoLuong}, Thành tiền: {ThanhTien:C}, Trạng thái: {TrangThai}";
        }

        /// <summary>
        /// So sánh hai đối tượng HangHoa
        /// </summary>
        public override bool Equals(object? obj)
        {
            if (obj is HangHoa other)
            {
                return MaSo.Equals(other.MaSo, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        public override int GetHashCode()
        {
            return MaSo.GetHashCode();
        }
    }
}
