// Graph Visualizer JavaScript
class GraphVisualizer {
    constructor() {
        this.graph = {
            vertices: [],
            edges: [],
            isDirected: true
        };
        this.editMode = 'select';
        this.selectedVertex = null;
        this.tempEdgeStart = null;
        this.currentResult = null;
        this.vertexIdCounter = 0;
        
        this.initializeEventListeners();
        this.updateGraphInfo();
    }
    
    initializeEventListeners() {
        // SVG click events
        document.getElementById('graphSvg').addEventListener('click', (e) => {
            this.handleSvgClick(e);
        });
        
        // Edit mode radio buttons
        document.querySelectorAll('input[name="editMode"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.editMode = e.target.id.replace('Mode', '');
                this.clearSelection();
            });
        });
        
        // Algorithm controls
        document.getElementById('runAlgorithm').addEventListener('click', () => {
            this.runSelectedAlgorithm();
        });
        
        document.getElementById('clearResult').addEventListener('click', () => {
            this.clearResult();
        });
        
        document.getElementById('clearGraph').addEventListener('click', () => {
            this.clearGraph();
        });
        
        document.getElementById('loadSample').addEventListener('click', () => {
            this.loadSampleGraph();
        });
        
        // Edge modal
        document.getElementById('confirmAddEdge').addEventListener('click', () => {
            this.confirmAddEdge();
        });
    }
    
    handleSvgClick(e) {
        const rect = e.target.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // Convert to SVG coordinates
        const svgX = (x / rect.width) * 800;
        const svgY = (y / rect.height) * 600;
        
        switch (this.editMode) {
            case 'addVertex':
                this.addVertex(svgX, svgY);
                break;
            case 'addEdge':
                this.handleEdgeClick(e.target, svgX, svgY);
                break;
            case 'select':
                this.handleSelectClick(e.target);
                break;
        }
    }
    
    addVertex(x, y) {
        const vertex = {
            id: this.vertexIdCounter++,
            name: String.fromCharCode(65 + this.graph.vertices.length), // A, B, C, ...
            x: x,
            y: y
        };
        
        this.graph.vertices.push(vertex);
        this.updateVisualization();
        this.updateVertexSelects();
        this.updateGraphInfo();
    }
    
    handleEdgeClick(target, x, y) {
        if (target.classList.contains('vertex-circle')) {
            const vertexId = parseInt(target.getAttribute('data-vertex-id'));
            const vertex = this.graph.vertices.find(v => v.id === vertexId);
            
            if (!this.tempEdgeStart) {
                this.tempEdgeStart = vertex;
                target.classList.add('selected');
            } else if (this.tempEdgeStart.id !== vertexId) {
                this.showEdgeModal(this.tempEdgeStart, vertex);
            }
        }
    }
    
    showEdgeModal(from, to) {
        document.getElementById('edgeFrom').value = from.name;
        document.getElementById('edgeTo').value = to.name;
        document.getElementById('edgeWeight').value = '1';
        
        this.pendingEdge = { from, to };
        
        const modal = new bootstrap.Modal(document.getElementById('edgeModal'));
        modal.show();
    }
    
    confirmAddEdge() {
        const weight = parseFloat(document.getElementById('edgeWeight').value);
        
        if (this.pendingEdge && !isNaN(weight)) {
            const edge = {
                from: this.pendingEdge.from.id,
                to: this.pendingEdge.to.id,
                weight: weight
            };
            
            this.graph.edges.push(edge);
            this.updateVisualization();
            this.updateGraphInfo();
        }
        
        this.clearSelection();
        bootstrap.Modal.getInstance(document.getElementById('edgeModal')).hide();
    }
    
    clearSelection() {
        this.selectedVertex = null;
        this.tempEdgeStart = null;
        this.pendingEdge = null;
        
        document.querySelectorAll('.vertex-circle').forEach(circle => {
            circle.classList.remove('selected');
        });
    }
    
    updateVisualization() {
        const svg = document.getElementById('graphSvg');
        svg.innerHTML = '';
        
        // Draw edges first (so they appear behind vertices)
        this.graph.edges.forEach(edge => {
            this.drawEdge(svg, edge);
        });
        
        // Draw vertices
        this.graph.vertices.forEach(vertex => {
            this.drawVertex(svg, vertex);
        });
    }
    
    drawVertex(svg, vertex) {
        const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        
        // Circle
        const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circle.setAttribute('cx', vertex.x);
        circle.setAttribute('cy', vertex.y);
        circle.setAttribute('r', 20);
        circle.setAttribute('class', 'vertex-circle');
        circle.setAttribute('data-vertex-id', vertex.id);
        
        // Apply styling based on current result
        if (this.currentResult) {
            if (this.currentResult.path && this.currentResult.path.includes(vertex.name)) {
                circle.classList.add('path');
            }
            // Add source/target styling if needed
        }
        
        // Label
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', vertex.x);
        text.setAttribute('y', vertex.y + 5);
        text.setAttribute('class', 'vertex-label');
        text.textContent = vertex.name;
        
        g.appendChild(circle);
        g.appendChild(text);
        svg.appendChild(g);
    }
    
    drawEdge(svg, edge) {
        const fromVertex = this.graph.vertices.find(v => v.id === edge.from);
        const toVertex = this.graph.vertices.find(v => v.id === edge.to);
        
        if (!fromVertex || !toVertex) return;
        
        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        line.setAttribute('x1', fromVertex.x);
        line.setAttribute('y1', fromVertex.y);
        line.setAttribute('x2', toVertex.x);
        line.setAttribute('y2', toVertex.y);
        line.setAttribute('class', 'edge');
        
        // Apply path styling if this edge is in the result path
        if (this.currentResult && this.isEdgeInPath(edge)) {
            line.classList.add('path');
        }
        
        svg.appendChild(line);
        
        // Add arrow for directed graphs
        if (this.graph.isDirected) {
            this.drawArrow(svg, fromVertex, toVertex);
        }
        
        // Add weight label
        this.drawEdgeWeight(svg, fromVertex, toVertex, edge.weight);
    }
    
    drawArrow(svg, from, to) {
        const angle = Math.atan2(to.y - from.y, to.x - from.x);
        const arrowLength = 10;
        const arrowAngle = Math.PI / 6;
        
        // Calculate arrow position (on the edge of the target vertex)
        const arrowX = to.x - 20 * Math.cos(angle);
        const arrowY = to.y - 20 * Math.sin(angle);
        
        const arrow = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
        const x1 = arrowX - arrowLength * Math.cos(angle - arrowAngle);
        const y1 = arrowY - arrowLength * Math.sin(angle - arrowAngle);
        const x2 = arrowX - arrowLength * Math.cos(angle + arrowAngle);
        const y2 = arrowY - arrowLength * Math.sin(angle + arrowAngle);
        
        arrow.setAttribute('points', `${arrowX},${arrowY} ${x1},${y1} ${x2},${y2}`);
        arrow.setAttribute('class', 'arrow');
        arrow.setAttribute('fill', '#6c757d');
        
        svg.appendChild(arrow);
    }
    
    drawEdgeWeight(svg, from, to, weight) {
        const midX = (from.x + to.x) / 2;
        const midY = (from.y + to.y) / 2;
        
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', midX);
        text.setAttribute('y', midY - 5);
        text.setAttribute('class', 'edge-weight');
        text.textContent = weight;
        
        svg.appendChild(text);
    }
    
    isEdgeInPath(edge) {
        if (!this.currentResult || !this.currentResult.path) return false;
        
        const path = this.currentResult.path;
        const fromVertex = this.graph.vertices.find(v => v.id === edge.from);
        const toVertex = this.graph.vertices.find(v => v.id === edge.to);
        
        for (let i = 0; i < path.length - 1; i++) {
            if (path[i] === fromVertex.name && path[i + 1] === toVertex.name) {
                return true;
            }
        }
        return false;
    }
    
    updateVertexSelects() {
        const sourceSelect = document.getElementById('sourceSelect');
        const targetSelect = document.getElementById('targetSelect');
        
        // Clear existing options
        sourceSelect.innerHTML = '<option value="">Chọn đỉnh nguồn</option>';
        targetSelect.innerHTML = '<option value="">Chọn đỉnh đích</option>';
        
        // Add vertex options
        this.graph.vertices.forEach(vertex => {
            const sourceOption = new Option(vertex.name, vertex.name);
            const targetOption = new Option(vertex.name, vertex.name);
            sourceSelect.add(sourceOption);
            targetSelect.add(targetOption);
        });
    }
    
    updateGraphInfo() {
        document.getElementById('vertexCount').textContent = this.graph.vertices.length;
        document.getElementById('edgeCount').textContent = this.graph.edges.length;
        document.getElementById('graphType').textContent = this.graph.isDirected ? 'Có hướng' : 'Vô hướng';
        
        const hasNegative = this.graph.edges.some(e => e.weight < 0);
        document.getElementById('hasNegativeWeights').textContent = hasNegative ? 'Có' : 'Không';
    }
    
    async runSelectedAlgorithm() {
        const algorithm = document.getElementById('algorithmSelect').value;
        const source = document.getElementById('sourceSelect').value;
        const target = document.getElementById('targetSelect').value;
        
        if (!source || !target) {
            alert('Vui lòng chọn đỉnh nguồn và đích');
            return;
        }
        
        if (this.graph.vertices.length === 0) {
            alert('Vui lòng tạo đồ thị trước');
            return;
        }
        
        const requestData = {
            vertices: this.graph.vertices,
            edges: this.graph.edges,
            source: source,
            target: target,
            isDirected: this.graph.isDirected
        };
        
        try {
            this.showLoading(true);
            
            let response;
            if (algorithm === 'compare') {
                response = await fetch('/api/algorithm/compare', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestData)
                });
            } else {
                response = await fetch(`/api/algorithm/${algorithm}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestData)
                });
            }
            
            const result = await response.json();
            this.displayResult(result, algorithm);
            
        } catch (error) {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi chạy thuật toán');
        } finally {
            this.showLoading(false);
        }
    }
    
    displayResult(result, algorithm) {
        const container = document.getElementById('resultContainer');
        
        if (algorithm === 'compare') {
            this.displayComparisonResult(result);
        } else {
            this.currentResult = result;
            this.displaySingleResult(result);
        }
        
        this.updateVisualization();
        this.displaySteps(result.steps || []);
    }
    
    displaySingleResult(result) {
        const container = document.getElementById('resultContainer');
        
        let html = `
            <div class="result-item">
                <h6 class="text-primary">${result.algorithmName}</h6>
                <p><strong>Đường đi:</strong> ${result.path ? result.path.join(' → ') : 'Không tìm thấy'}</p>
                <p><strong>Độ dài:</strong> ${result.pathLength}</p>
                <p><strong>Thời gian:</strong> ${result.executionTimeMs} ms</p>
                ${result.hasNegativeCycle ? '<p class="text-danger"><strong>Có chu trình âm!</strong></p>' : ''}
            </div>
        `;
        
        container.innerHTML = html;
    }
    
    displayComparisonResult(result) {
        const container = document.getElementById('resultContainer');
        
        let html = '<div class="comparison-results">';
        
        result.algorithms.forEach(alg => {
            html += `
                <div class="result-item mb-3 p-2 border rounded">
                    <h6 class="text-primary">${alg.algorithmName}</h6>
                    <small>Đường đi: ${alg.path ? alg.path.join(' → ') : 'Không tìm thấy'}</small><br>
                    <small>Độ dài: ${alg.pathLength}</small><br>
                    <small>Thời gian: ${alg.executionTimeMs} ms</small>
                    ${alg.hasNegativeCycle ? '<br><small class="text-danger">Có chu trình âm!</small>' : ''}
                </div>
            `;
        });
        
        html += '</div>';
        container.innerHTML = html;
        
        // Update performance chart
        this.updatePerformanceChart(result);
    }
    
    displaySteps(steps) {
        const container = document.getElementById('stepsContainer');
        
        if (!steps || steps.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">Chưa có bước nào</p>';
            return;
        }
        
        let html = '';
        steps.forEach((step, index) => {
            html += `<div class="step-item">${index + 1}. ${step}</div>`;
        });
        
        container.innerHTML = html;
    }
    
    updatePerformanceChart(result) {
        // Implementation for Chart.js performance comparison
        // This would create a bar chart comparing execution times
    }
    
    clearResult() {
        this.currentResult = null;
        document.getElementById('resultContainer').innerHTML = '<p class="text-muted text-center">Chưa có kết quả</p>';
        document.getElementById('stepsContainer').innerHTML = '<p class="text-muted text-center">Chưa có bước nào</p>';
        this.updateVisualization();
    }
    
    clearGraph() {
        this.graph.vertices = [];
        this.graph.edges = [];
        this.vertexIdCounter = 0;
        this.clearResult();
        this.updateVisualization();
        this.updateVertexSelects();
        this.updateGraphInfo();
    }
    
    async loadSampleGraph() {
        const sampleType = document.getElementById('sampleGraphSelect').value;
        if (!sampleType) return;
        
        try {
            const response = await fetch('/api/algorithm/sample-graphs');
            const samples = await response.json();
            
            if (samples[sampleType]) {
                this.graph = samples[sampleType];
                this.vertexIdCounter = Math.max(...this.graph.vertices.map(v => v.id)) + 1;
                this.updateVisualization();
                this.updateVertexSelects();
                this.updateGraphInfo();
            }
        } catch (error) {
            console.error('Error loading sample:', error);
            alert('Có lỗi khi tải đồ thị mẫu');
        }
    }
    
    showLoading(show) {
        const modal = document.getElementById('loadingModal');
        if (show) {
            new bootstrap.Modal(modal).show();
        } else {
            const instance = bootstrap.Modal.getInstance(modal);
            if (instance) instance.hide();
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.graphVisualizer = new GraphVisualizer();
});
