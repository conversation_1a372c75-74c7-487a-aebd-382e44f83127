<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - G<PERSON>c phố Coffee Admin</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/GocPho.styles.css" asp-append-version="true" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        .admin-sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s;
        }
        
        .admin-content {
            margin-left: 250px;
            min-height: 100vh;
            background: #f8f9fa;
            transition: all 0.3s;
        }
        
        .admin-header {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 2rem;
            border-bottom: 3px solid #f39c12;
        }
        
        .sidebar-brand {
            padding: 1.5rem 1rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .sidebar-nav .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            border: none;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }
        
        .sidebar-nav .nav-link:hover,
        .sidebar-nav .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
            border-left-color: #f39c12;
        }
        
        .sidebar-nav .nav-link i {
            width: 20px;
            margin-right: 10px;
        }
        
        .admin-card {
            border-radius: 10px;
            transition: transform 0.2s;
        }
        
        .admin-card:hover {
            transform: translateY(-2px);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            border-radius: 15px;
        }
        
        .stats-card-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        }
        
        .stats-card-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        
        .stats-card-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }
        
        @@media (max-width: 768px) {
            .admin-sidebar {
                margin-left: -250px;
            }
            
            .admin-content {
                margin-left: 0;
            }
            
            .sidebar-toggle {
                display: block !important;
            }
        }
    </style>
</head>
<body>
    <!-- Admin Sidebar -->
    <div class="admin-sidebar">
        <div class="sidebar-brand text-center">
            <h4 class="text-white mb-0">
                <i class="fas fa-coffee text-warning me-2"></i>
                <span class="fw-bold">Góc phố</span>
                <small class="d-block text-warning">Admin Panel</small>
            </h4>
        </div>
        
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link @(ViewContext.RouteData.Values["Action"]?.ToString() == "Index" ? "active" : "")" 
                       asp-controller="Admin" asp-action="Index">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(ViewContext.RouteData.Values["Action"]?.ToString() == "Products" ? "active" : "")" 
                       asp-controller="Admin" asp-action="Products">
                        <i class="fas fa-coffee"></i>Quản lý sản phẩm
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(ViewContext.RouteData.Values["Action"]?.ToString() == "CreateProduct" ? "active" : "")" 
                       asp-controller="Admin" asp-action="CreateProduct">
                        <i class="fas fa-plus-circle"></i>Thêm sản phẩm
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(ViewContext.RouteData.Values["Action"]?.ToString() == "Orders" ? "active" : "")" 
                       asp-controller="Admin" asp-action="Orders">
                        <i class="fas fa-shopping-cart"></i>Quản lý đơn hàng
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(ViewContext.RouteData.Values["Action"]?.ToString() == "Users" ? "active" : "")" 
                       asp-controller="Admin" asp-action="Users">
                        <i class="fas fa-users"></i>Quản lý người dùng
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(ViewContext.RouteData.Values["Action"]?.ToString() == "Categories" ? "active" : "")" 
                       asp-controller="Admin" asp-action="Categories">
                        <i class="fas fa-tags"></i>Quản lý danh mục
                    </a>
                </li>
                <li class="nav-item mt-3">
                    <hr class="text-white-50">
                </li>
                <li class="nav-item">
                    <a class="nav-link" asp-controller="Home" asp-action="Index">
                        <i class="fas fa-globe"></i>Xem website
                    </a>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Admin Content -->
    <div class="admin-content">
        <!-- Admin Header -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <button class="btn btn-outline-secondary d-none sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h5 class="mb-0 ms-2 d-inline-block">@ViewData["Title"]</h5>
                </div>
                <div class="d-flex align-items-center">
                    <span class="text-muted me-3">
                        <i class="fas fa-clock me-1"></i>
                        <span id="current-time"></span>
                    </span>
                    <div class="dropdown">
                        <button class="btn btn-outline-warning dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>Admin
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Cài đặt</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-sign-out-alt me-2"></i>Về trang chủ</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="p-4">
            @RenderBody()
        </main>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('vi-VN');
            document.getElementById('current-time').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);
        
        // Sidebar toggle for mobile
        document.querySelector('.sidebar-toggle')?.addEventListener('click', function() {
            document.querySelector('.admin-sidebar').classList.toggle('show');
        });
    </script>
    
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
