<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản <PERSON><PERSON> - <PERSON>h <PERSON>ách <PERSON></title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1><i class="fas fa-graduation-cap"></i> Quản Lý H<PERSON></h1>
            <p>Ứng dụng demo sử dụng cấu trúc dữ liệu Danh Sách Liên Kết Đơn</p>
        </div>
    </header>

    <!-- Student Info -->
    <div class="student-info">
        <div class="container">
            <div class="info-card">
                <h3><i class="fas fa-user"></i> Thông tin sinh viên thực hiện</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <strong>Họ tên:</strong> Huỳnh Thái Bảo
                    </div>
                    <div class="info-item">
                        <strong>Ngày sinh:</strong> 28/04/1994
                    </div>
                    <div class="info-item">
                        <strong>Email:</strong> <EMAIL>
                    </div>
                    <div class="info-item">
                        <strong>Điện thoại:</strong> 0355771075
                    </div>
                    <div class="info-item">
                        <strong>Tài khoản:</strong> baoht280494
                    </div>
                    <div class="info-item">
                        <strong>Lớp:</strong> DX23TT11
                    </div>
                    <div class="info-item">
                        <strong>Mã sinh viên:</strong> 170123488
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Control Panel -->
            <div class="control-panel">
                <div class="panel-section">
                    <h3><i class="fas fa-plus"></i> Thêm Học Sinh</h3>
                    <form id="studentForm" class="student-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="maSo">Mã số:</label>
                                <input type="text" id="maSo" required placeholder="VD: HS001">
                            </div>
                            <div class="form-group">
                                <label for="hoTen">Họ tên:</label>
                                <input type="text" id="hoTen" required placeholder="Nguyễn Văn A">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="namSinh">Năm sinh:</label>
                                <input type="number" id="namSinh" required min="1990" max="2010" placeholder="2005">
                            </div>
                            <div class="form-group">
                                <label for="khoi">Khối:</label>
                                <select id="khoi" required>
                                    <option value="">Chọn khối</option>
                                    <option value="10">Khối 10</option>
                                    <option value="11">Khối 11</option>
                                    <option value="12">Khối 12</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="lop">Lớp:</label>
                                <input type="text" id="lop" required placeholder="A1">
                            </div>
                            <div class="form-group">
                                <label for="diemTB">Điểm TB:</label>
                                <input type="number" id="diemTB" required min="0" max="10" step="0.1" placeholder="8.5">
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Thêm học sinh
                        </button>
                    </form>
                </div>

                <div class="panel-section">
                    <h3><i class="fas fa-search"></i> Tìm Kiếm & Lọc</h3>
                    <div class="search-controls">
                        <div class="form-group">
                            <label for="searchMaSo">Tìm theo mã số:</label>
                            <div class="input-group">
                                <input type="text" id="searchMaSo" placeholder="Nhập mã số...">
                                <button onclick="searchStudent()" class="btn btn-secondary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="filterKhoi">Lọc theo khối:</label>
                            <select id="filterKhoi" onchange="filterByKhoi()">
                                <option value="">Tất cả khối</option>
                                <option value="10">Khối 10</option>
                                <option value="11">Khối 11</option>
                                <option value="12">Khối 12</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="filterDiem">Lọc theo điểm:</label>
                            <div class="input-group">
                                <input type="number" id="filterDiem" min="0" max="10" step="0.1" placeholder="5.0">
                                <select id="filterOperator">
                                    <option value="gte">≥</option>
                                    <option value="lt"><</option>
                                </select>
                                <button onclick="filterByDiem()" class="btn btn-secondary">
                                    <i class="fas fa-filter"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel-section">
                    <h3><i class="fas fa-sort"></i> Sắp Xếp & Thống Kê</h3>
                    <div class="action-buttons">
                        <button onclick="sortByMaSo()" class="btn btn-info">
                            <i class="fas fa-sort-alpha-down"></i> Sắp xếp theo mã số
                        </button>
                        <button onclick="sortByDiem()" class="btn btn-info">
                            <i class="fas fa-sort-numeric-down"></i> Sắp xếp theo điểm
                        </button>
                        <button onclick="showStatistics()" class="btn btn-warning">
                            <i class="fas fa-chart-bar"></i> Thống kê
                        </button>
                        <button onclick="clearAll()" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Xóa tất cả
                        </button>
                    </div>
                </div>
            </div>

            <!-- Data Structure Visualization -->
            <div class="visualization-section">
                <h3><i class="fas fa-project-diagram"></i> Minh Họa Cấu Trúc Danh Sách Liên Kết Đơn</h3>
                <div id="linkedListVisualization" class="linked-list-viz">
                    <div class="empty-list">
                        <i class="fas fa-info-circle"></i>
                        Danh sách rỗng - Hãy thêm học sinh để xem cấu trúc liên kết
                    </div>
                </div>
            </div>

            <!-- Student List -->
            <div class="student-list-section">
                <div class="section-header">
                    <h3><i class="fas fa-list"></i> Danh Sách Học Sinh</h3>
                    <div class="list-stats">
                        <span id="totalCount">Tổng: 0 học sinh</span>
                    </div>
                </div>
                <div class="table-container">
                    <table id="studentTable" class="student-table">
                        <thead>
                            <tr>
                                <th>STT</th>
                                <th>Mã số</th>
                                <th>Họ tên</th>
                                <th>Năm sinh</th>
                                <th>Khối</th>
                                <th>Lớp</th>
                                <th>Điểm TB</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody id="studentTableBody">
                            <!-- Data will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Statistics Modal -->
            <div id="statisticsModal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-chart-bar"></i> Thống Kê Dữ Liệu</h3>
                        <span class="close" onclick="closeModal()">&times;</span>
                    </div>
                    <div class="modal-body" id="statisticsContent">
                        <!-- Statistics content will be inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 - Đồ án Cấu trúc dữ liệu và Giải thuật - Danh Sách Liên Kết Đơn</p>
            <p>Sinh viên thực hiện: <strong>Huỳnh Thái Bảo</strong> - Lớp: <strong>DX23TT11</strong></p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
