/**
 * Electronics Shop Template - Main JavaScript
 * Website bán thiết bị điện tử cũ
 */

(function() {
    'use strict';

    // DOM Ready
    document.addEventListener('DOMContentLoaded', function() {
        initializeTemplate();
    });

    /**
     * Initialize template functionality
     */
    function initializeTemplate() {
        initSmoothScrolling();
        initScrollToTop();
        initProductImageGallery();
        initProductQuickView();
        initCartFunctionality();
        initWishlistFunctionality();
        initSearchFunctionality();
        initMobileMenu();
        initAnimations();
        initTooltips();
        initProductComparison();
    }

    /**
     * Smooth scrolling for anchor links
     */
    function initSmoothScrolling() {
        const links = document.querySelectorAll('a[href^="#"]');
        
        links.forEach(link => {
            link.addEventListener('click', function(e) {
                const href = this.getAttribute('href');
                if (href === '#') return;
                
                const target = document.querySelector(href);
                if (target) {
                    e.preventDefault();
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    /**
     * Scroll to top button
     */
    function initScrollToTop() {
        // Create scroll to top button
        const scrollBtn = document.createElement('button');
        scrollBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
        scrollBtn.className = 'scroll-to-top btn btn-primary';
        scrollBtn.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: none;
            z-index: 1000;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;
        
        document.body.appendChild(scrollBtn);

        // Show/hide button based on scroll position
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                scrollBtn.style.display = 'block';
            } else {
                scrollBtn.style.display = 'none';
            }
        });

        // Scroll to top on click
        scrollBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    /**
     * Product image gallery
     */
    function initProductImageGallery() {
        const galleries = document.querySelectorAll('.product-gallery');
        
        galleries.forEach(gallery => {
            const mainImage = gallery.querySelector('.main-image img');
            const thumbnails = gallery.querySelectorAll('.thumbnail img');
            
            thumbnails.forEach(thumb => {
                thumb.addEventListener('click', function() {
                    // Remove active class from all thumbnails
                    thumbnails.forEach(t => t.parentElement.classList.remove('active'));
                    
                    // Add active class to clicked thumbnail
                    this.parentElement.classList.add('active');
                    
                    // Update main image
                    if (mainImage) {
                        mainImage.src = this.src;
                        mainImage.alt = this.alt;
                    }
                });
            });
        });
    }

    /**
     * Product quick view modal
     */
    function initProductQuickView() {
        const quickViewBtns = document.querySelectorAll('.btn-quick-view');
        
        quickViewBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.dataset.productId;
                
                if (productId) {
                    loadProductQuickView(productId);
                }
            });
        });
    }

    /**
     * Load product quick view
     */
    function loadProductQuickView(productId) {
        // Show loading modal
        showModal('Đang tải...', '<div class="text-center"><div class="spinner-border" role="status"></div></div>');
        
        // Simulate AJAX call (replace with actual VirtueMart AJAX)
        setTimeout(() => {
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <img src="/images/products/product-${productId}.jpg" class="img-fluid" alt="Product">
                    </div>
                    <div class="col-md-6">
                        <h4>Tên sản phẩm</h4>
                        <div class="product-rating mb-2">
                            <span class="stars">★★★★☆</span>
                            <span class="rating-count">(15 đánh giá)</span>
                        </div>
                        <div class="product-price mb-3">
                            <span class="current-price">15.000.000 ₫</span>
                            <span class="original-price">18.000.000 ₫</span>
                        </div>
                        <div class="product-condition mb-3">
                            <span class="badge bg-success">Tốt (85%)</span>
                        </div>
                        <div class="product-warranty mb-3">
                            <i class="fas fa-shield-alt"></i> Bảo hành 6 tháng
                        </div>
                        <div class="product-actions">
                            <button class="btn btn-primary btn-add-to-cart me-2">
                                <i class="fas fa-shopping-cart"></i> Thêm vào giỏ
                            </button>
                            <button class="btn btn-outline-danger btn-wishlist">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            showModal('Xem nhanh sản phẩm', content);
        }, 1000);
    }

    /**
     * Cart functionality
     */
    function initCartFunctionality() {
        const addToCartBtns = document.querySelectorAll('.btn-add-to-cart');
        
        addToCartBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                
                const productId = this.dataset.productId || this.closest('.product-card')?.dataset.productId;
                
                if (productId) {
                    addToCart(productId);
                }
            });
        });
    }

    /**
     * Add product to cart
     */
    function addToCart(productId) {
        // Show loading state
        const btn = document.querySelector(`[data-product-id="${productId}"] .btn-add-to-cart`);
        if (btn) {
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang thêm...';
            btn.disabled = true;
            
            // Simulate AJAX call
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
                
                // Show success message
                showToast('Sản phẩm đã được thêm vào giỏ hàng!', 'success');
                
                // Update cart count
                updateCartCount();
            }, 1000);
        }
    }

    /**
     * Wishlist functionality
     */
    function initWishlistFunctionality() {
        const wishlistBtns = document.querySelectorAll('.btn-wishlist');
        
        wishlistBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                
                const productId = this.dataset.productId || this.closest('.product-card')?.dataset.productId;
                
                if (productId) {
                    toggleWishlist(productId, this);
                }
            });
        });
    }

    /**
     * Toggle wishlist
     */
    function toggleWishlist(productId, btn) {
        const isInWishlist = btn.classList.contains('active');
        
        if (isInWishlist) {
            btn.classList.remove('active');
            btn.innerHTML = '<i class="far fa-heart"></i>';
            showToast('Đã xóa khỏi danh sách yêu thích', 'info');
        } else {
            btn.classList.add('active');
            btn.innerHTML = '<i class="fas fa-heart"></i>';
            showToast('Đã thêm vào danh sách yêu thích', 'success');
        }
    }

    /**
     * Search functionality
     */
    function initSearchFunctionality() {
        const searchInput = document.querySelector('.header-search input[type="search"]');
        const searchBtn = document.querySelector('.header-search .btn-search');
        
        if (searchInput) {
            // Search on Enter key
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch(this.value);
                }
            });
            
            // Auto-suggest (debounced)
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (this.value.length >= 2) {
                        showSearchSuggestions(this.value);
                    } else {
                        hideSearchSuggestions();
                    }
                }, 300);
            });
        }
        
        if (searchBtn) {
            searchBtn.addEventListener('click', function(e) {
                e.preventDefault();
                if (searchInput) {
                    performSearch(searchInput.value);
                }
            });
        }
    }

    /**
     * Perform search
     */
    function performSearch(query) {
        if (query.trim()) {
            window.location.href = `/search?q=${encodeURIComponent(query)}`;
        }
    }

    /**
     * Show search suggestions
     */
    function showSearchSuggestions(query) {
        // Simulate search suggestions
        const suggestions = [
            'iPhone 12 cũ',
            'Laptop Dell cũ',
            'Samsung Galaxy cũ',
            'MacBook cũ',
            'iPad cũ'
        ].filter(item => item.toLowerCase().includes(query.toLowerCase()));
        
        // Create suggestions dropdown (implement as needed)
        console.log('Search suggestions:', suggestions);
    }

    /**
     * Hide search suggestions
     */
    function hideSearchSuggestions() {
        // Hide suggestions dropdown
    }

    /**
     * Mobile menu
     */
    function initMobileMenu() {
        const mobileMenuBtn = document.querySelector('[data-bs-toggle="collapse"]');
        const mobileMenu = document.querySelector('#main-navigation');
        
        if (mobileMenuBtn && mobileMenu) {
            mobileMenuBtn.addEventListener('click', function() {
                mobileMenu.classList.toggle('show');
            });
        }
    }

    /**
     * Initialize animations
     */
    function initAnimations() {
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);
        
        // Observe elements with animation classes
        document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right').forEach(el => {
            observer.observe(el);
        });
    }

    /**
     * Initialize tooltips
     */
    function initTooltips() {
        // Initialize Bootstrap tooltips if available
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    /**
     * Product comparison
     */
    function initProductComparison() {
        const compareBtns = document.querySelectorAll('.btn-compare');
        
        compareBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.dataset.productId;
                
                if (productId) {
                    toggleProductComparison(productId, this);
                }
            });
        });
    }

    /**
     * Toggle product comparison
     */
    function toggleProductComparison(productId, btn) {
        const isInComparison = btn.classList.contains('active');
        
        if (isInComparison) {
            btn.classList.remove('active');
            showToast('Đã xóa khỏi so sánh', 'info');
        } else {
            btn.classList.add('active');
            showToast('Đã thêm vào so sánh', 'success');
        }
        
        updateComparisonCount();
    }

    /**
     * Update cart count
     */
    function updateCartCount() {
        const cartCount = document.querySelector('.cart-count');
        if (cartCount) {
            let count = parseInt(cartCount.textContent) || 0;
            cartCount.textContent = count + 1;
        }
    }

    /**
     * Update comparison count
     */
    function updateComparisonCount() {
        const compareCount = document.querySelector('.compare-count');
        if (compareCount) {
            const activeCompares = document.querySelectorAll('.btn-compare.active').length;
            compareCount.textContent = activeCompares;
            compareCount.style.display = activeCompares > 0 ? 'block' : 'none';
        }
    }

    /**
     * Show modal
     */
    function showModal(title, content) {
        // Create modal if it doesn't exist
        let modal = document.getElementById('quickViewModal');
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'quickViewModal';
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title"></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body"></div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }
        
        // Update content
        modal.querySelector('.modal-title').textContent = title;
        modal.querySelector('.modal-body').innerHTML = content;
        
        // Show modal
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
        }
    }

    /**
     * Show toast notification
     */
    function showToast(message, type = 'info') {
        // Create toast container if it doesn't exist
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '1055';
            document.body.appendChild(toastContainer);
        }
        
        // Create toast
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} border-0`;
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        toastContainer.appendChild(toast);
        
        // Show toast
        if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            // Remove toast after it's hidden
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }
        
        // Auto-remove after 3 seconds if Bootstrap is not available
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 3000);
    }

})();
