using Microsoft.AspNetCore.Mvc;
using ShortestPathProject.Models;
using ShortestPathProject.Services;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ShortestPathProject.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AlgorithmController : ControllerBase
    {
        private readonly DijkstraService _dijkstraService;
        private readonly BellmanFordService _bellmanFordService;
        private readonly FloydWarshallService _floydWarshallService;
        private readonly SPFAService _spfaService;
        
        public AlgorithmController()
        {
            _dijkstraService = new DijkstraService();
            _bellmanFordService = new BellmanFordService();
            _floydWarshallService = new FloydWarshallService();
            _spfaService = new SPFAService();
        }
        
        [HttpPost("dijkstra")]
        public IActionResult RunDijkstra([FromBody] AlgorithmRequest request)
        {
            try
            {
                var graph = CreateGraphFromRequest(request);
                var source = graph.Vertices.FirstOrDefault(v => v.Name == request.Source);
                var target = graph.Vertices.FirstOrDefault(v => v.Name == request.Target);
                
                if (source == null || target == null)
                    return BadRequest("Đỉnh nguồn hoặc đích không tồn tại");
                
                var result = _dijkstraService.FindShortestPath(graph, source, target);
                return Ok(result.ToJson());
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Lỗi: {ex.Message}");
            }
        }
        
        [HttpPost("bellman-ford")]
        public IActionResult RunBellmanFord([FromBody] AlgorithmRequest request)
        {
            try
            {
                var graph = CreateGraphFromRequest(request);
                var source = graph.Vertices.FirstOrDefault(v => v.Name == request.Source);
                var target = graph.Vertices.FirstOrDefault(v => v.Name == request.Target);
                
                if (source == null || target == null)
                    return BadRequest("Đỉnh nguồn hoặc đích không tồn tại");
                
                var result = _bellmanFordService.FindShortestPath(graph, source, target);
                return Ok(result.ToJson());
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Lỗi: {ex.Message}");
            }
        }
        
        [HttpPost("floyd-warshall")]
        public IActionResult RunFloydWarshall([FromBody] AlgorithmRequest request)
        {
            try
            {
                var graph = CreateGraphFromRequest(request);
                var result = _floydWarshallService.FindAllPairsShortestPaths(graph);
                
                // Nếu có source và target, tìm đường đi cụ thể
                if (!string.IsNullOrEmpty(request.Source) && !string.IsNullOrEmpty(request.Target))
                {
                    var source = graph.Vertices.FirstOrDefault(v => v.Name == request.Source);
                    var target = graph.Vertices.FirstOrDefault(v => v.Name == request.Target);
                    
                    if (source != null && target != null)
                    {
                        var path = _floydWarshallService.GetPath(result, source, target);
                        result.Path = path;
                        if (path.Count > 0)
                        {
                            int sourceIndex = result.VertexList.IndexOf(source);
                            int targetIndex = result.VertexList.IndexOf(target);
                            result.PathLength = result.DistanceMatrix[sourceIndex, targetIndex];
                        }
                    }
                }
                
                return Ok(result.ToJson());
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Lỗi: {ex.Message}");
            }
        }
        
        [HttpPost("spfa")]
        public IActionResult RunSPFA([FromBody] AlgorithmRequest request)
        {
            try
            {
                var graph = CreateGraphFromRequest(request);
                var source = graph.Vertices.FirstOrDefault(v => v.Name == request.Source);
                var target = graph.Vertices.FirstOrDefault(v => v.Name == request.Target);
                
                if (source == null || target == null)
                    return BadRequest("Đỉnh nguồn hoặc đích không tồn tại");
                
                var result = _spfaService.FindShortestPath(graph, source, target);
                return Ok(result.ToJson());
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Lỗi: {ex.Message}");
            }
        }
        
        [HttpPost("compare")]
        public IActionResult CompareAlgorithms([FromBody] AlgorithmRequest request)
        {
            try
            {
                var graph = CreateGraphFromRequest(request);
                var source = graph.Vertices.FirstOrDefault(v => v.Name == request.Source);
                var target = graph.Vertices.FirstOrDefault(v => v.Name == request.Target);
                
                if (source == null || target == null)
                    return BadRequest("Đỉnh nguồn hoặc đích không tồn tại");
                
                var comparison = new ComparisonResult();
                
                // Chạy Dijkstra (nếu không có trọng số âm)
                bool hasNegativeWeight = graph.Edges.Any(e => e.Weight < 0);
                if (!hasNegativeWeight)
                {
                    var dijkstraResult = _dijkstraService.FindShortestPath(graph, source, target);
                    comparison.Results.Add(dijkstraResult);
                    comparison.ExecutionTimes["Dijkstra"] = dijkstraResult.ExecutionTimeMs;
                }
                
                // Chạy Bellman-Ford
                var bellmanResult = _bellmanFordService.FindShortestPath(graph, source, target);
                comparison.Results.Add(bellmanResult);
                comparison.ExecutionTimes["Bellman-Ford"] = bellmanResult.ExecutionTimeMs;
                
                // Chạy SPFA
                var spfaResult = _spfaService.FindShortestPath(graph, source, target);
                comparison.Results.Add(spfaResult);
                comparison.ExecutionTimes["SPFA"] = spfaResult.ExecutionTimeMs;
                
                // Tìm thuật toán nhanh nhất
                comparison.FastestAlgorithm = comparison.ExecutionTimes
                    .OrderBy(kvp => kvp.Value)
                    .First().Key;
                
                return Ok(comparison.ToJson());
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Lỗi: {ex.Message}");
            }
        }

        [HttpGet("sample-graphs")]
        public IActionResult GetSampleGraphs()
        {
            try
            {
                var samples = new
                {
                    simple = ConvertGraphToRequest(Graph.CreateSampleGraph()),
                    negative = ConvertGraphToRequest(Graph.CreateNegativeWeightGraph()),
                    large = ConvertGraphToRequest(Graph.CreateLargeGraph(20, 0.3))
                };
                
                return Ok(samples);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Lỗi: {ex.Message}");
            }
        }

        [HttpPost("performance-test")]
        public IActionResult PerformanceTest([FromBody] PerformanceTestRequest request)
        {
            try
            {
                var results = new List<object>();
                
                foreach (var size in request.GraphSizes)
                {
                    var graph = Graph.CreateLargeGraph(size, request.EdgeDensity, request.HasNegativeWeights);
                    var source = graph.Vertices.First();
                    var target = graph.Vertices.Last();
                    
                    var testResult = new
                    {
                        graphSize = size,
                        edgeCount = graph.EdgeCount,
                        algorithms = new List<object>()
                    };
                    
                    // Test Dijkstra (nếu không có trọng số âm)
                    if (!request.HasNegativeWeights)
                    {
                        var dijkstraResult = _dijkstraService.FindShortestPath(graph, source, target);
                        ((List<object>)testResult.algorithms).Add(new
                        {
                            name = "Dijkstra",
                            executionTime = dijkstraResult.ExecutionTimeMs,
                            pathFound = dijkstraResult.Path.Count > 0
                        });
                    }
                    
                    // Test Bellman-Ford
                    var bellmanResult = _bellmanFordService.FindShortestPath(graph, source, target);
                    ((List<object>)testResult.algorithms).Add(new
                    {
                        name = "Bellman-Ford",
                        executionTime = bellmanResult.ExecutionTimeMs,
                        pathFound = bellmanResult.Path.Count > 0,
                        hasNegativeCycle = bellmanResult.HasNegativeCycle
                    });
                    
                    // Test SPFA
                    var spfaResult = _spfaService.FindShortestPath(graph, source, target);
                    ((List<object>)testResult.algorithms).Add(new
                    {
                        name = "SPFA",
                        executionTime = spfaResult.ExecutionTimeMs,
                        pathFound = spfaResult.Path.Count > 0,
                        hasNegativeCycle = spfaResult.HasNegativeCycle
                    });
                    
                    results.Add(testResult);
                }
                
                return Ok(new { testResults = results });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Lỗi: {ex.Message}");
            }
        }
        
        private Graph CreateGraphFromRequest(AlgorithmRequest request)
        {
            var graph = new Graph(request.IsDirected);
            
            // Thêm các đỉnh
            foreach (var vertexData in request.Vertices)
            {
                var vertex = new Vertex(vertexData.Id, vertexData.Name, vertexData.X, vertexData.Y);
                graph.AddVertex(vertex);
            }
            
            // Thêm các cạnh
            foreach (var edgeData in request.Edges)
            {
                var fromVertex = graph.Vertices.FirstOrDefault(v => v.Id == edgeData.From);
                var toVertex = graph.Vertices.FirstOrDefault(v => v.Id == edgeData.To);
                
                if (fromVertex != null && toVertex != null)
                {
                    graph.AddEdge(fromVertex, toVertex, edgeData.Weight);
                }
            }
            
            return graph;
        }

        private AlgorithmRequest ConvertGraphToRequest(Graph graph)
        {
            var request = new AlgorithmRequest
            {
                IsDirected = graph.IsDirected,
                Vertices = graph.Vertices.Select(v => new VertexData
                {
                    Id = v.Id,
                    Name = v.Name,
                    X = v.X,
                    Y = v.Y
                }).ToList(),
                Edges = graph.Edges.Select(e => new EdgeData
                {
                    From = e.From.Id,
                    To = e.To.Id,
                    Weight = e.Weight
                }).ToList()
            };
            
            return request;
        }
    }

    public class PerformanceTestRequest
    {
        public List<int> GraphSizes { get; set; } = new List<int>();
        public double EdgeDensity { get; set; } = 0.3;
        public bool HasNegativeWeights { get; set; } = false;
    }
}
