/**
 * Visualization cho các giải thuật tìm đường đi ngắn nhất
 * Author: Student
 * Date: 2025
 */

// Lớp GraphVisualizer để vẽ đồ thị
class GraphVisualizer {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.graph = null;
        this.currentStep = 0;
        this.isAnimating = false;
        this.highlightedPath = [];
        this.highlightedEdge = null;
        this.visitedNodes = new Set();
        this.currentNode = -1;
    }

    setGraph(graph) {
        this.graph = graph;
        this.reset();
    }

    reset() {
        this.currentStep = 0;
        this.isAnimating = false;
        this.highlightedPath = [];
        this.highlightedEdge = null;
        this.visitedNodes.clear();
        this.currentNode = -1;
        this.draw();
    }

    draw() {
        if (!this.graph) return;

        // Xóa canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Vẽ các cạnh trước
        this.drawEdges();
        
        // Vẽ các đỉnh sau
        this.drawVertices();
    }

    drawEdges() {
        if (!this.graph.graph && !this.graph.edges) return;

        this.ctx.lineWidth = 2;
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';

        // Vẽ cạnh cho Dijkstra
        if (this.graph.graph) {
            for (let u = 0; u < this.graph.V; u++) {
                for (const edge of this.graph.graph[u]) {
                    const v = edge.vertex;
                    const weight = edge.weight;
                    this.drawEdge(u, v, weight);
                }
            }
        }
        
        // Vẽ cạnh cho Bellman-Ford
        if (this.graph.edges) {
            for (const edge of this.graph.edges) {
                this.drawEdge(edge.src, edge.dest, edge.weight);
            }
        }
    }

    drawEdge(u, v, weight) {
        const pos1 = this.graph.positions[u];
        const pos2 = this.graph.positions[v];
        
        if (!pos1 || !pos2) return;

        // Kiểm tra xem cạnh có được highlight không
        const isHighlighted = this.highlightedEdge && 
            this.highlightedEdge.from === u && this.highlightedEdge.to === v;
        
        const isInPath = this.highlightedPath.length > 1 && 
            this.isEdgeInPath(u, v);

        // Màu sắc cạnh
        if (isHighlighted) {
            this.ctx.strokeStyle = '#ff6b6b';
            this.ctx.lineWidth = 4;
        } else if (isInPath) {
            this.ctx.strokeStyle = '#51cf66';
            this.ctx.lineWidth = 3;
        } else {
            this.ctx.strokeStyle = '#868e96';
            this.ctx.lineWidth = 2;
        }

        // Vẽ đường thẳng
        this.ctx.beginPath();
        this.ctx.moveTo(pos1.x, pos1.y);
        this.ctx.lineTo(pos2.x, pos2.y);
        this.ctx.stroke();

        // Vẽ mũi tên
        this.drawArrow(pos1.x, pos1.y, pos2.x, pos2.y);

        // Vẽ trọng số
        const midX = (pos1.x + pos2.x) / 2;
        const midY = (pos1.y + pos2.y) / 2;
        
        this.ctx.fillStyle = 'white';
        this.ctx.fillRect(midX - 15, midY - 10, 30, 20);
        
        this.ctx.fillStyle = weight < 0 ? '#ff6b6b' : '#495057';
        this.ctx.font = 'bold 12px Arial';
        this.ctx.fillText(weight.toString(), midX, midY + 4);
    }

    drawArrow(fromX, fromY, toX, toY) {
        const angle = Math.atan2(toY - fromY, toX - fromX);
        const arrowLength = 15;
        const arrowAngle = Math.PI / 6;

        // Điều chỉnh vị trí mũi tên để không đè lên đỉnh
        const adjustedToX = toX - Math.cos(angle) * 25;
        const adjustedToY = toY - Math.sin(angle) * 25;

        this.ctx.beginPath();
        this.ctx.moveTo(adjustedToX, adjustedToY);
        this.ctx.lineTo(
            adjustedToX - arrowLength * Math.cos(angle - arrowAngle),
            adjustedToY - arrowLength * Math.sin(angle - arrowAngle)
        );
        this.ctx.moveTo(adjustedToX, adjustedToY);
        this.ctx.lineTo(
            adjustedToX - arrowLength * Math.cos(angle + arrowAngle),
            adjustedToY - arrowLength * Math.sin(angle + arrowAngle)
        );
        this.ctx.stroke();
    }

    drawVertices() {
        for (let i = 0; i < this.graph.V; i++) {
            const pos = this.graph.positions[i];
            if (!pos) continue;

            // Màu sắc đỉnh
            let fillColor = '#e9ecef';
            let strokeColor = '#495057';
            let textColor = '#495057';

            if (i === this.currentNode) {
                fillColor = '#ff8cc8';
                strokeColor = '#e64980';
                textColor = 'white';
            } else if (this.visitedNodes.has(i)) {
                fillColor = '#8ce99a';
                strokeColor = '#51cf66';
                textColor = 'white';
            } else if (this.highlightedPath.includes(i)) {
                fillColor = '#74c0fc';
                strokeColor = '#339af0';
                textColor = 'white';
            }

            // Vẽ đỉnh
            this.ctx.beginPath();
            this.ctx.arc(pos.x, pos.y, 20, 0, 2 * Math.PI);
            this.ctx.fillStyle = fillColor;
            this.ctx.fill();
            this.ctx.strokeStyle = strokeColor;
            this.ctx.lineWidth = 3;
            this.ctx.stroke();

            // Vẽ nhãn đỉnh
            this.ctx.fillStyle = textColor;
            this.ctx.font = 'bold 16px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(i.toString(), pos.x, pos.y + 5);
        }
    }

    isEdgeInPath(u, v) {
        for (let i = 0; i < this.highlightedPath.length - 1; i++) {
            if (this.highlightedPath[i] === u && this.highlightedPath[i + 1] === v) {
                return true;
            }
        }
        return false;
    }

    highlightPath(path) {
        this.highlightedPath = path;
        this.draw();
    }

    highlightEdge(edge) {
        this.highlightedEdge = edge;
        this.draw();
    }

    setCurrentNode(node) {
        this.currentNode = node;
        this.draw();
    }

    addVisitedNode(node) {
        this.visitedNodes.add(node);
        this.draw();
    }

    clearHighlights() {
        this.highlightedPath = [];
        this.highlightedEdge = null;
        this.visitedNodes.clear();
        this.currentNode = -1;
        this.draw();
    }

    // Animation cho Dijkstra
    async animateDijkstra(steps, speed = 1000) {
        this.isAnimating = true;
        this.clearHighlights();

        for (let i = 0; i < steps.length && this.isAnimating; i++) {
            const step = steps[i];
            
            if (step.type === 'visit') {
                this.setCurrentNode(step.current);
                this.addVisitedNode(step.current);
            } else if (step.type === 'relax') {
                this.highlightEdge(step.edge);
                await delay(speed / 2);
                this.highlightEdge(null);
            }

            await delay(speed);
        }

        this.isAnimating = false;
    }

    stopAnimation() {
        this.isAnimating = false;
    }
}

// Lớp MatrixVisualizer để vẽ ma trận Floyd-Warshall
class MatrixVisualizer {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.matrix = null;
        this.size = 0;
        this.currentK = -1;
        this.updatedCells = new Set();
    }

    setMatrix(matrix) {
        this.matrix = matrix;
        this.size = matrix.length;
        this.render();
    }

    render() {
        if (!this.matrix) return;

        this.container.innerHTML = '';
        this.container.style.gridTemplateColumns = `repeat(${this.size + 1}, 1fr)`;

        // Header row
        this.addCell('', 'header');
        for (let j = 0; j < this.size; j++) {
            this.addCell(j.toString(), 'header');
        }

        // Data rows
        for (let i = 0; i < this.size; i++) {
            this.addCell(i.toString(), 'header');
            for (let j = 0; j < this.size; j++) {
                const value = this.matrix[i][j];
                const cellValue = value === Infinity ? '∞' : value.toString();
                const cellClass = value === Infinity ? 'infinity' : '';
                this.addCell(cellValue, cellClass, i, j);
            }
        }
    }

    addCell(content, className = '', row = -1, col = -1) {
        const cell = document.createElement('div');
        cell.className = `matrix-cell ${className}`;
        cell.textContent = content;
        
        if (row >= 0 && col >= 0) {
            cell.dataset.row = row;
            cell.dataset.col = col;
        }
        
        this.container.appendChild(cell);
    }

    highlightCell(i, j, className = 'updated') {
        const cell = this.container.querySelector(`[data-row="${i}"][data-col="${j}"]`);
        if (cell) {
            cell.classList.add(className);
            setTimeout(() => {
                cell.classList.remove(className);
            }, 1000);
        }
    }

    updateCell(i, j, value) {
        const cell = this.container.querySelector(`[data-row="${i}"][data-col="${j}"]`);
        if (cell) {
            const cellValue = value === Infinity ? '∞' : value.toString();
            cell.textContent = cellValue;
            cell.classList.toggle('infinity', value === Infinity);
            this.highlightCell(i, j);
        }
    }

    setCurrentK(k) {
        this.currentK = k;
        // Highlight row and column k
        this.clearHighlights();
        
        if (k >= 0) {
            // Highlight row k
            for (let j = 0; j < this.size; j++) {
                const cell = this.container.querySelector(`[data-row="${k}"][data-col="${j}"]`);
                if (cell) cell.classList.add('highlight-row');
            }
            
            // Highlight column k
            for (let i = 0; i < this.size; i++) {
                const cell = this.container.querySelector(`[data-row="${i}"][data-col="${k}"]`);
                if (cell) cell.classList.add('highlight-col');
            }
        }
    }

    clearHighlights() {
        const cells = this.container.querySelectorAll('.matrix-cell');
        cells.forEach(cell => {
            cell.classList.remove('highlight-row', 'highlight-col', 'updated');
        });
    }

    async animateFloydWarshall(matrices, speed = 1000) {
        for (let step = 0; step < matrices.length; step++) {
            const { k, matrix, updates } = matrices[step];
            
            this.setCurrentK(k);
            this.setMatrix(matrix);
            
            if (updates) {
                for (const update of updates) {
                    this.highlightCell(update.i, update.j);
                    await delay(speed / 4);
                }
            }
            
            await delay(speed);
        }
        
        this.clearHighlights();
    }
}

// Lớp ChartVisualizer để vẽ biểu đồ so sánh
class ChartVisualizer {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
    }

    drawPerformanceChart(data) {
        const { dijkstra, bellmanFord, floydWarshall } = data;
        const sizes = Object.keys(dijkstra).map(Number).sort((a, b) => a - b);
        
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Thiết lập
        const padding = 50;
        const chartWidth = this.canvas.width - 2 * padding;
        const chartHeight = this.canvas.height - 2 * padding;
        
        // Tìm giá trị max để scale
        let maxTime = 0;
        sizes.forEach(size => {
            maxTime = Math.max(maxTime, dijkstra[size]?.time || 0);
            maxTime = Math.max(maxTime, bellmanFord[size]?.time || 0);
            maxTime = Math.max(maxTime, floydWarshall[size]?.time || 0);
        });
        
        // Vẽ trục
        this.drawAxes(padding, chartWidth, chartHeight, sizes, maxTime);
        
        // Vẽ đường biểu đồ
        this.drawLine(sizes, dijkstra, 'time', maxTime, chartWidth, chartHeight, padding, '#667eea', 'Dijkstra');
        this.drawLine(sizes, bellmanFord, 'time', maxTime, chartWidth, chartHeight, padding, '#ff6b6b', 'Bellman-Ford');
        this.drawLine(sizes, floydWarshall, 'time', maxTime, chartWidth, chartHeight, padding, '#51cf66', 'Floyd-Warshall');
        
        // Vẽ legend
        this.drawLegend();
    }

    drawAxes(padding, chartWidth, chartHeight, sizes, maxTime) {
        this.ctx.strokeStyle = '#495057';
        this.ctx.lineWidth = 2;
        
        // Trục X
        this.ctx.beginPath();
        this.ctx.moveTo(padding, padding + chartHeight);
        this.ctx.lineTo(padding + chartWidth, padding + chartHeight);
        this.ctx.stroke();
        
        // Trục Y
        this.ctx.beginPath();
        this.ctx.moveTo(padding, padding);
        this.ctx.lineTo(padding, padding + chartHeight);
        this.ctx.stroke();
        
        // Nhãn trục
        this.ctx.fillStyle = '#495057';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';
        
        // Nhãn trục X
        sizes.forEach((size, index) => {
            const x = padding + (index / (sizes.length - 1)) * chartWidth;
            this.ctx.fillText(size.toString(), x, padding + chartHeight + 20);
        });
        
        // Nhãn trục Y
        this.ctx.textAlign = 'right';
        for (let i = 0; i <= 5; i++) {
            const y = padding + chartHeight - (i / 5) * chartHeight;
            const value = (maxTime * i / 5).toFixed(1);
            this.ctx.fillText(value + 'ms', padding - 10, y + 4);
        }
    }

    drawLine(sizes, data, property, maxValue, chartWidth, chartHeight, padding, color, label) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 3;
        this.ctx.beginPath();
        
        let firstPoint = true;
        sizes.forEach((size, index) => {
            if (data[size]) {
                const x = padding + (index / (sizes.length - 1)) * chartWidth;
                const y = padding + chartHeight - (data[size][property] / maxValue) * chartHeight;
                
                if (firstPoint) {
                    this.ctx.moveTo(x, y);
                    firstPoint = false;
                } else {
                    this.ctx.lineTo(x, y);
                }
                
                // Vẽ điểm
                this.ctx.fillStyle = color;
                this.ctx.beginPath();
                this.ctx.arc(x, y, 4, 0, 2 * Math.PI);
                this.ctx.fill();
            }
        });
        
        this.ctx.stroke();
    }

    drawLegend() {
        const legends = [
            { color: '#667eea', label: 'Dijkstra' },
            { color: '#ff6b6b', label: 'Bellman-Ford' },
            { color: '#51cf66', label: 'Floyd-Warshall' }
        ];
        
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'left';
        
        legends.forEach((legend, index) => {
            const x = 20;
            const y = 20 + index * 20;
            
            // Vẽ màu
            this.ctx.fillStyle = legend.color;
            this.ctx.fillRect(x, y - 8, 15, 12);
            
            // Vẽ text
            this.ctx.fillStyle = '#495057';
            this.ctx.fillText(legend.label, x + 20, y);
        });
    }
}

// Export classes
window.GraphVisualizer = GraphVisualizer;
window.MatrixVisualizer = MatrixVisualizer;
window.ChartVisualizer = ChartVisualizer;
