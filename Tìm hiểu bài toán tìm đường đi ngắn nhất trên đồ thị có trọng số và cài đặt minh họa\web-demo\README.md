# Demo Web - Gi<PERSON>i Thuật Tìm Đường Đi Ngắn Nhất

## Mô tả

Đây là một ứng dụng web tương tác được phát triển bằng HTML, CSS và JavaScript để minh họa trực quan các giải thuật tìm đường đi ngắn nhất trên đồ thị có trọng số.

## Tính năng chính

### 🎯 Gi<PERSON>i thuật Dijkstra
- **Visualization**: Vẽ đồ thị với animation từng bước
- **Interactive**: Chọn đỉnh nguồn, xem kết quả real-time
- **Step-by-step**: Hiển thị chi tiết từng bước thực hiện
- **Path highlighting**: Highlight đường đi ngắn nhất
- **Results table**: Bảng kết quả với khoảng cách và đường đi

### 🔄 G<PERSON><PERSON><PERSON> thuật <PERSON>-Ford
- **Negative weights**: <PERSON><PERSON> lý đồ thị có trọng số âm
- **Negative cycle detection**: <PERSON><PERSON><PERSON> hiện và cảnh báo chu trình âm
- **Iterations display**: Hiển thị từng lần lặp của thuật toán
- **Graph switching**: Chuyển đổi giữa đồ thị bình thường và có chu trình âm
- **Detailed results**: Kết quả chi tiết với trạng thái thuật toán

### 📊 Giải thuật Floyd-Warshall
- **Matrix visualization**: Hiển thị ma trận khoảng cách trực quan
- **Step-by-step execution**: Chạy từng bước với đỉnh trung gian
- **Progress tracking**: Thanh tiến trình và thông tin bước hiện tại
- **Path finder**: Tìm đường đi giữa hai đỉnh bất kỳ
- **Animation**: Animation cập nhật ma trận theo thời gian thực

### 📈 So sánh các giải thuật
- **Performance comparison**: Biểu đồ so sánh hiệu suất
- **Memory usage**: Phân tích sử dụng bộ nhớ
- **Recommendation guide**: Hướng dẫn lựa chọn giải thuật
- **Feature comparison table**: Bảng so sánh tính năng chi tiết

## Cấu trúc file

```
web-demo/
├── index.html          # Giao diện chính
├── styles.css          # Styling và responsive design
├── algorithms.js       # Cài đặt các giải thuật
├── visualization.js    # Visualization và animation
├── main.js            # Logic điều khiển chính
└── README.md          # Tài liệu này
```

## Công nghệ sử dụng

### Frontend
- **HTML5**: Cấu trúc semantic và accessibility
- **CSS3**: Modern styling với Flexbox/Grid, animations
- **JavaScript ES6+**: Logic thuật toán và tương tác
- **Canvas API**: Vẽ đồ thị và visualization
- **Font Awesome**: Icons chuyên nghiệp
- **Google Fonts**: Typography đẹp

### Tính năng CSS
- **Responsive Design**: Tương thích mọi thiết bị
- **CSS Grid/Flexbox**: Layout hiện đại
- **CSS Animations**: Smooth transitions và effects
- **CSS Variables**: Theming và maintainability
- **Backdrop Filter**: Glass morphism effects

### Tính năng JavaScript
- **ES6 Classes**: OOP structure cho algorithms
- **Async/Await**: Xử lý animation không đồng bộ
- **Canvas Manipulation**: Vẽ đồ thị động
- **Event Handling**: Tương tác người dùng
- **Performance Monitoring**: Đo thời gian thực thi

## Hướng dẫn sử dụng

### 1. Mở ứng dụng
```bash
# Mở file index.html trong trình duyệt
# Hoặc sử dụng live server
python -m http.server 8000
# Truy cập http://localhost:8000
```

### 2. Dijkstra Tab
1. Chọn đỉnh nguồn từ dropdown
2. Click "Chạy Dijkstra" để bắt đầu
3. Xem animation và kết quả
4. Click vào hàng trong bảng để highlight đường đi
5. Click "Reset" để thử lại

### 3. Bellman-Ford Tab
1. Chọn đỉnh nguồn
2. Click "Chạy Bellman-Ford"
3. Xem các lần lặp và kết quả
4. Click "Tạo Chu Trình Âm" để test negative cycle
5. Quan sát cảnh báo chu trình âm

### 4. Floyd-Warshall Tab
1. Click "Chạy Floyd-Warshall" để chạy hoàn chỉnh
2. Hoặc click "Từng Bước" để xem chi tiết
3. Sử dụng "Tìm Đường Đi" để tìm path giữa 2 đỉnh
4. Quan sát ma trận cập nhật theo thời gian thực

### 5. So Sánh Tab
1. Xem bảng so sánh tổng quan
2. Phân tích biểu đồ hiệu suất
3. Đọc hướng dẫn lựa chọn giải thuật
4. So sánh memory usage

## Đặc điểm kỹ thuật

### Hiệu suất
- **Optimized rendering**: Chỉ redraw khi cần thiết
- **Efficient algorithms**: Cài đặt tối ưu các thuật toán
- **Memory management**: Quản lý bộ nhớ hiệu quả
- **Responsive animations**: 60fps smooth animations

### Accessibility
- **Keyboard navigation**: Hỗ trợ điều hướng bàn phím
- **Screen reader friendly**: ARIA labels và semantic HTML
- **High contrast**: Màu sắc dễ phân biệt
- **Responsive text**: Font size linh hoạt

### Browser Support
- **Modern browsers**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **Mobile browsers**: iOS Safari, Chrome Mobile
- **Progressive enhancement**: Graceful degradation

## Customization

### Thay đổi đồ thị
```javascript
// Trong algorithms.js
function createCustomGraph() {
    const graph = new DijkstraGraph(numberOfVertices);
    graph.addEdge(from, to, weight);
    graph.setPosition(vertex, x, y);
    return graph;
}
```

### Thay đổi màu sắc
```css
/* Trong styles.css */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #51cf66;
    --error-color: #ff6b6b;
}
```

### Thay đổi animation speed
```javascript
// Trong main.js
await dijkstraVisualizer.animateDijkstra(result.steps, 1200); // 1200ms delay
```

## Troubleshooting

### Lỗi thường gặp

1. **Canvas không hiển thị**
   - Kiểm tra browser support cho Canvas API
   - Đảm bảo JavaScript được enable

2. **Animation bị lag**
   - Giảm animation speed
   - Đóng các tab khác
   - Sử dụng browser hiện đại

3. **Responsive issues**
   - Clear browser cache
   - Kiểm tra viewport meta tag
   - Test trên device thật

### Performance Tips

1. **Tối ưu hiệu suất**
   - Sử dụng Chrome DevTools để profile
   - Giảm số lượng đỉnh cho đồ thị lớn
   - Tắt animation nếu cần

2. **Memory optimization**
   - Reset graphs khi không sử dụng
   - Avoid memory leaks trong event listeners
   - Use efficient data structures

## Phát triển thêm

### Tính năng có thể thêm
- [ ] Graph editor để tạo đồ thị tùy chỉnh
- [ ] Export/Import graph data
- [ ] More algorithm variants (A*, D*)
- [ ] 3D visualization
- [ ] Real-world examples integration
- [ ] Performance benchmarking tools
- [ ] Multi-language support
- [ ] Dark/Light theme toggle

### Technical improvements
- [ ] Web Workers cho heavy computations
- [ ] WebGL cho better graphics performance
- [ ] PWA support với offline capability
- [ ] Unit tests với Jest
- [ ] E2E tests với Cypress
- [ ] Build system với Webpack/Vite
- [ ] TypeScript migration
- [ ] Component-based architecture

## Đóng góp

Để đóng góp vào dự án:

1. Fork repository
2. Tạo feature branch
3. Implement changes
4. Add tests nếu cần
5. Submit pull request

## License

Dự án này được phát triển cho mục đích giáo dục. Free to use and modify.

## Tác giả

- **Sinh viên**: Lê Đức Tài
- **MSSV**: 170123432
- **Lớp**: DX23TT11
- **Email**: <EMAIL>
- **Số điện thoại**: **********
- **Ngày sinh**: 13/12/2001
- **Năm**: 2024-2025

## Tài liệu tham khảo

1. Cormen, T. H., et al. (2009). Introduction to Algorithms, Third Edition.
2. MDN Web Docs - Canvas API
3. CSS-Tricks - Modern CSS Techniques
4. JavaScript.info - Modern JavaScript Tutorial

---

**Lưu ý**: Đây là phiên bản demo cho mục đích giáo dục. Để sử dụng trong production, cần thêm error handling, security measures và performance optimizations.
