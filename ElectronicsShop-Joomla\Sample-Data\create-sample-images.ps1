# PowerShell script to create sample product images structure
Write-Host "========================================" -ForegroundColor Green
Write-Host "   Sample Images Structure Creator" -ForegroundColor Green
Write-Host "   Electronics Shop Project" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Variables
$projectRoot = Split-Path -Parent $PSScriptRoot
$imagesPath = Join-Path $projectRoot "Sample-Data\Images"
$productImagesPath = Join-Path $imagesPath "Products"

# Create directory structure
$directories = @(
    "Products\Laptops\Dell",
    "Products\Laptops\Lenovo",
    "Products\Laptops\HP",
    "Products\Laptops\Asus",
    "Products\Phones\iPhone",
    "Products\Phones\Samsung",
    "Products\Phones\Xiaomi",
    "Products\Phones\Oppo",
    "Products\Tablets\iPad",
    "Products\Tablets\Samsung-Tab",
    "Products\Accessories\Chargers",
    "Products\Accessories\Headphones",
    "Products\Accessories\Cases",
    "Products\Components\RAM",
    "Products\Components\Storage",
    "Products\Components\Graphics",
    "Banners",
    "Categories",
    "Logos",
    "Placeholders"
)

Write-Host "Creating directory structure..." -ForegroundColor Yellow

foreach ($dir in $directories) {
    $fullPath = Join-Path $imagesPath $dir
    if (-not (Test-Path $fullPath)) {
        New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
        Write-Host "Created: $dir" -ForegroundColor Green
    } else {
        Write-Host "Exists: $dir" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "Creating sample image placeholders..." -ForegroundColor Yellow

# Sample product data
$sampleProducts = @{
    "Laptops" = @{
        "Dell" = @("E7450", "E7470", "Latitude-5520", "Inspiron-3511"),
        "Lenovo" = @("T460", "T470", "T480", "X1-Carbon"),
        "HP" = @("EliteBook-840", "ProBook-450", "Pavilion-15"),
        "Asus" = @("VivoBook-15", "ZenBook-14", "ROG-Strix")
    },
    "Phones" = @{
        "iPhone" = @("iPhone-12", "iPhone-13", "iPhone-11", "iPhone-SE"),
        "Samsung" = @("Galaxy-S21", "Galaxy-S22", "Galaxy-A52", "Galaxy-Note20"),
        "Xiaomi" = @("Mi-11", "Redmi-Note-11", "Poco-X3", "Mi-11-Lite"),
        "Oppo" = @("Reno-6", "A74", "Find-X3", "A54")
    },
    "Tablets" = @{
        "iPad" = @("iPad-Air", "iPad-Pro", "iPad-Mini", "iPad-9th"),
        "Samsung-Tab" = @("Tab-S7", "Tab-A7", "Tab-S6-Lite")
    }
}

# Create placeholder images info
$placeholderInfo = @"
# Sample Images for Electronics Shop

## Directory Structure
This directory contains sample images for the Electronics Shop website.

### Products/
Contains product images organized by category:

#### Laptops/
- Dell/ - Dell laptop images
- Lenovo/ - Lenovo laptop images  
- HP/ - HP laptop images
- Asus/ - Asus laptop images

#### Phones/
- iPhone/ - iPhone images
- Samsung/ - Samsung phone images
- Xiaomi/ - Xiaomi phone images
- Oppo/ - Oppo phone images

#### Tablets/
- iPad/ - iPad images
- Samsung-Tab/ - Samsung tablet images

#### Accessories/
- Chargers/ - Charger and cable images
- Headphones/ - Headphone images
- Cases/ - Phone and laptop case images

#### Components/
- RAM/ - RAM module images
- Storage/ - SSD and HDD images
- Graphics/ - Graphics card images

### Banners/
Website banner images for homepage and promotions

### Categories/
Category thumbnail images

### Logos/
Brand logos and website logo

### Placeholders/
Default placeholder images

## Image Requirements

### Product Images
- **Main Image**: 800x800px, JPG/PNG
- **Gallery Images**: 800x800px, JPG/PNG
- **Thumbnails**: 300x300px, JPG/PNG
- **Condition Images**: 400x400px, JPG/PNG

### Banner Images
- **Hero Banner**: 1920x600px, JPG
- **Category Banners**: 1200x400px, JPG
- **Promotion Banners**: 800x300px, JPG

### Category Images
- **Category Thumbnails**: 300x300px, JPG/PNG
- **Category Headers**: 1200x300px, JPG

## Naming Convention

### Product Images
- Main: `{product-sku}-main.jpg`
- Gallery: `{product-sku}-gallery-{number}.jpg`
- Thumbnail: `{product-sku}-thumb.jpg`
- Condition: `{product-sku}-condition.jpg`

### Examples
- `dell-e7450-main.jpg`
- `dell-e7450-gallery-1.jpg`
- `dell-e7450-gallery-2.jpg`
- `dell-e7450-thumb.jpg`
- `dell-e7450-condition.jpg`

## Sample Products

### Laptops
"@

foreach ($category in $sampleProducts.Keys) {
    $placeholderInfo += "`n#### $category`n"
    foreach ($brand in $sampleProducts[$category].Keys) {
        $placeholderInfo += "**$brand**:`n"
        foreach ($model in $sampleProducts[$category][$brand]) {
            $placeholderInfo += "- $model`n"
        }
        $placeholderInfo += "`n"
    }
}

$placeholderInfo += @"

## Image Sources
For actual implementation, you can source images from:

### Free Stock Photos
- Unsplash.com
- Pexels.com
- Pixabay.com

### Product Images
- Manufacturer websites
- E-commerce sites (with permission)
- Product photography

### Tools for Image Processing
- GIMP (Free)
- Photoshop
- Online tools: Canva, Figma

## Implementation Notes

1. **Optimize Images**: Compress images for web use
2. **Alt Text**: Add descriptive alt text for SEO
3. **Responsive**: Ensure images work on all devices
4. **Loading**: Implement lazy loading for performance
5. **Backup**: Keep original high-resolution images

## VirtueMart Integration

Upload images to: `images/stories/virtuemart/product/`

Directory structure in VirtueMart:
```
images/stories/virtuemart/product/
├── laptops/
├── phones/
├── tablets/
├── accessories/
└── components/
```

## Template Integration

Images are referenced in:
- VirtueMart product templates
- Custom modules
- Template overrides
- CSS background images

Generated on: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
"@

# Save the info file
$infoFile = Join-Path $imagesPath "README.md"
$placeholderInfo | Out-File -FilePath $infoFile -Encoding UTF8

Write-Host "Created README.md with image guidelines" -ForegroundColor Green

# Create sample .gitkeep files to maintain directory structure
foreach ($dir in $directories) {
    $fullPath = Join-Path $imagesPath $dir
    $gitkeepFile = Join-Path $fullPath ".gitkeep"
    "# Keep this directory in git" | Out-File -FilePath $gitkeepFile -Encoding UTF8
}

Write-Host "Created .gitkeep files to maintain directory structure" -ForegroundColor Green

# Create sample image list for each category
$imageListContent = @"
# Sample Image List for Electronics Shop

## Required Images by Category

### Laptops (Dell E7450 Example)
- dell-e7450-main.jpg (800x800) - Main product image
- dell-e7450-gallery-1.jpg (800x800) - Keyboard view
- dell-e7450-gallery-2.jpg (800x800) - Side ports view
- dell-e7450-gallery-3.jpg (800x800) - Screen display
- dell-e7450-gallery-4.jpg (800x800) - Bottom view
- dell-e7450-condition.jpg (400x400) - Condition details
- dell-e7450-thumb.jpg (300x300) - Thumbnail

### Phones (iPhone 12 Example)
- iphone-12-main.jpg (800x800) - Front view
- iphone-12-gallery-1.jpg (800x800) - Back view
- iphone-12-gallery-2.jpg (800x800) - Side view
- iphone-12-gallery-3.jpg (800x800) - Screen on
- iphone-12-condition.jpg (400x400) - Condition details
- iphone-12-accessories.jpg (400x400) - Included accessories
- iphone-12-thumb.jpg (300x300) - Thumbnail

### Category Images
- category-laptops.jpg (300x300)
- category-phones.jpg (300x300)
- category-tablets.jpg (300x300)
- category-accessories.jpg (300x300)
- category-components.jpg (300x300)

### Banner Images
- hero-banner-main.jpg (1920x600) - Main homepage banner
- banner-laptops.jpg (1200x400) - Laptop category banner
- banner-phones.jpg (1200x400) - Phone category banner
- banner-sale.jpg (800x300) - Sale promotion banner

### Condition Indicators
- condition-like-new.png (100x100) - Green checkmark
- condition-good.png (100x100) - Blue checkmark
- condition-fair.png (100x100) - Yellow warning
- condition-needs-repair.png (100x100) - Red warning

### Placeholder Images
- product-placeholder.jpg (800x800) - Default product image
- category-placeholder.jpg (300x300) - Default category image
- banner-placeholder.jpg (1200x400) - Default banner image
- avatar-placeholder.jpg (150x150) - Default user avatar

### Brand Logos
- logo-dell.png (200x100)
- logo-lenovo.png (200x100)
- logo-hp.png (200x100)
- logo-apple.png (200x100)
- logo-samsung.png (200x100)
- logo-xiaomi.png (200x100)

### Website Assets
- site-logo.png (300x100) - Main website logo
- site-favicon.ico (32x32) - Browser favicon
- loading-spinner.gif (50x50) - Loading animation
"@

$imageListFile = Join-Path $imagesPath "image-list.md"
$imageListContent | Out-File -FilePath $imageListFile -Encoding UTF8

Write-Host "Created image-list.md with detailed requirements" -ForegroundColor Green

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "   Sample Images Structure Created!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Directory created at: $imagesPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Add actual product images to the created directories" -ForegroundColor White
Write-Host "2. Follow the naming convention in README.md" -ForegroundColor White
Write-Host "3. Optimize images for web use (compress, resize)" -ForegroundColor White
Write-Host "4. Upload to VirtueMart: images/stories/virtuemart/product/" -ForegroundColor White
Write-Host "5. Link images to products in VirtueMart admin" -ForegroundColor White
Write-Host ""
Write-Host "Files created:" -ForegroundColor Green
Write-Host "- README.md - Image guidelines and requirements" -ForegroundColor White
Write-Host "- image-list.md - Detailed image list by category" -ForegroundColor White
Write-Host "- .gitkeep files - Maintain directory structure in git" -ForegroundColor White

Write-Host ""
Read-Host "Press Enter to exit"
