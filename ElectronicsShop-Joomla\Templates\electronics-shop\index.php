<?php
/**
 * @package     Electronics Shop Template
 * @subpackage  Template
 * @copyright   Copyright (C) 2025 Electronics Shop. All rights reserved.
 * @license     GNU General Public License version 2 or later
 */

defined('_JEXEC') or die;

use Joomla\CMS\Factory;
use Joomla\CMS\HTML\HTMLHelper;
use <PERSON><PERSON><PERSON>\CMS\Language\Text;
use <PERSON><PERSON>la\CMS\Uri\Uri;

$app = Factory::getApplication();
$wa  = $this->getWebAssetManager();

// Detecting Active Variables
$option   = $app->input->getCmd('option', '');
$view     = $app->input->getCmd('view', '');
$layout   = $app->input->getCmd('layout', '');
$task     = $app->input->getCmd('task', '');
$itemid   = $app->input->getCmd('Itemid', '');
$sitename = htmlspecialchars($app->get('sitename'), ENT_QUOTES, 'UTF-8');
$menu     = $app->getMenu()->getActive();
$pageclass = $menu !== null ? $menu->getParams()->get('pageclass_sfx', '') : '';

// Template Parameters
$logoFile = $this->params->get('logoFile');
$siteTitle = $this->params->get('siteTitle', 'Electronics Shop');
$siteTagline = $this->params->get('siteTagline', 'Thiết bị điện tử cũ chất lượng cao');
$primaryColor = $this->params->get('primaryColor', '#007bff');
$secondaryColor = $this->params->get('secondaryColor', '#6c757d');
$showBreadcrumbs = $this->params->get('showBreadcrumbs', 1);
$showSearch = $this->params->get('showSearch', 1);
$showCart = $this->params->get('showCart', 1);
$layoutStyle = $this->params->get('layoutStyle', 'boxed');
$enableAnimations = $this->params->get('enableAnimations', 1);
$contactPhone = $this->params->get('contactPhone', '+84 123 456 789');
$contactEmail = $this->params->get('contactEmail', '<EMAIL>');

// Load CSS and JS
$wa->useStyle('template.electronics-shop.main');
$wa->useScript('template.electronics-shop.main');

// Add Bootstrap 5
$wa->useStyle('bootstrap.css', ['version' => '5.1.3']);
$wa->useScript('bootstrap.js', ['version' => '5.1.3']);

// Add Font Awesome
$wa->registerAndUseStyle('fontawesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');

// Custom CSS Variables
$customCSS = "
:root {
    --primary-color: {$primaryColor};
    --secondary-color: {$secondaryColor};
}
";

$wa->addInlineStyle($customCSS);

// Add animations if enabled
if ($enableAnimations) {
    $wa->registerAndUseStyle('aos', 'https://unpkg.com/aos@2.3.1/dist/aos.css');
    $wa->registerAndUseScript('aos', 'https://unpkg.com/aos@2.3.1/dist/aos.js');
}

// Check for VirtueMart
$isVirtueMart = ($option === 'com_virtuemart');
?>
<!DOCTYPE html>
<html lang="<?php echo $this->language; ?>" dir="<?php echo $this->direction; ?>">
<head>
    <jdoc:include type="metas" />
    <jdoc:include type="styles" />
    <jdoc:include type="scripts" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="<?php echo $primaryColor; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo $this->baseurl; ?>/templates/<?php echo $this->template; ?>/images/favicon.ico">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $sitename; ?>">
    <meta property="og:description" content="<?php echo $siteTagline; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo Uri::current(); ?>">
    <meta property="og:image" content="<?php echo $this->baseurl; ?>/templates/<?php echo $this->template; ?>/images/og-image.jpg">
</head>

<body class="site <?php echo $option . ' view-' . $view . ($layout ? ' layout-' . $layout : '') . ($task ? ' task-' . $task : '') . ($pageclass ? ' ' . $pageclass : '') . ($layoutStyle === 'boxed' ? ' boxed-layout' : ' fluid-layout'); ?>">

    <!-- Skip to main content -->
    <a class="visually-hidden-focusable" href="#main-content"><?php echo Text::_('TPL_ELECTRONICS_SHOP_SKIP_TO_MAIN_CONTENT'); ?></a>

    <!-- Header -->
    <header class="site-header">
        <!-- Top Bar -->
        <div class="top-bar bg-dark text-white py-2">
            <div class="<?php echo $layoutStyle === 'boxed' ? 'container' : 'container-fluid'; ?>">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="contact-info">
                            <span class="me-3">
                                <i class="fas fa-phone"></i>
                                <a href="tel:<?php echo str_replace(' ', '', $contactPhone); ?>" class="text-white text-decoration-none">
                                    <?php echo $contactPhone; ?>
                                </a>
                            </span>
                            <span>
                                <i class="fas fa-envelope"></i>
                                <a href="mailto:<?php echo $contactEmail; ?>" class="text-white text-decoration-none">
                                    <?php echo $contactEmail; ?>
                                </a>
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="user-actions">
                            <?php if ($this->countModules('user-menu')) : ?>
                                <jdoc:include type="modules" name="user-menu" style="none" />
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Header -->
        <div class="main-header bg-white shadow-sm">
            <div class="<?php echo $layoutStyle === 'boxed' ? 'container' : 'container-fluid'; ?>">
                <div class="row align-items-center py-3">
                    <!-- Logo -->
                    <div class="col-lg-3 col-md-4">
                        <div class="site-logo">
                            <a href="<?php echo $this->baseurl; ?>/" class="text-decoration-none">
                                <?php if ($logoFile) : ?>
                                    <img src="<?php echo $logoFile; ?>" alt="<?php echo $sitename; ?>" class="img-fluid">
                                <?php else : ?>
                                    <h1 class="site-title mb-0" style="color: var(--primary-color);">
                                        <?php echo $siteTitle; ?>
                                    </h1>
                                    <p class="site-tagline mb-0 text-muted small">
                                        <?php echo $siteTagline; ?>
                                    </p>
                                <?php endif; ?>
                            </a>
                        </div>
                    </div>

                    <!-- Search -->
                    <?php if ($showSearch && $this->countModules('search')) : ?>
                    <div class="col-lg-6 col-md-4">
                        <div class="header-search">
                            <jdoc:include type="modules" name="search" style="none" />
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Cart & Actions -->
                    <div class="col-lg-3 col-md-4">
                        <div class="header-actions d-flex justify-content-end align-items-center">
                            <?php if ($showCart && $this->countModules('cart')) : ?>
                                <div class="header-cart me-3">
                                    <jdoc:include type="modules" name="cart" style="none" />
                                </div>
                            <?php endif; ?>
                            
                            <!-- Mobile Menu Toggle -->
                            <button class="btn btn-outline-primary d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#main-navigation">
                                <i class="fas fa-bars"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <?php if ($this->countModules('menu')) : ?>
        <nav class="main-navigation bg-primary">
            <div class="<?php echo $layoutStyle === 'boxed' ? 'container' : 'container-fluid'; ?>">
                <div class="collapse navbar-collapse" id="main-navigation">
                    <jdoc:include type="modules" name="menu" style="none" />
                </div>
            </div>
        </nav>
        <?php endif; ?>
    </header>

    <!-- Banner -->
    <?php if ($this->countModules('banner')) : ?>
    <section class="banner-section">
        <jdoc:include type="modules" name="banner" style="none" />
    </section>
    <?php endif; ?>

    <!-- Breadcrumbs -->
    <?php if ($showBreadcrumbs && $this->countModules('breadcrumbs')) : ?>
    <section class="breadcrumbs-section bg-light py-2">
        <div class="<?php echo $layoutStyle === 'boxed' ? 'container' : 'container-fluid'; ?>">
            <jdoc:include type="modules" name="breadcrumbs" style="none" />
        </div>
    </section>
    <?php endif; ?>

    <!-- Main Content -->
    <main id="main-content" class="site-main">
        <div class="<?php echo $layoutStyle === 'boxed' ? 'container' : 'container-fluid'; ?>">
            
            <!-- Top Content -->
            <?php if ($this->countModules('main-top')) : ?>
            <section class="main-top py-4">
                <jdoc:include type="modules" name="main-top" style="html5" />
            </section>
            <?php endif; ?>

            <div class="row">
                <!-- Sidebar A -->
                <?php if ($this->countModules('sidebar-a')) : ?>
                <aside class="col-lg-3 col-md-4 sidebar-a">
                    <jdoc:include type="modules" name="sidebar-a" style="html5" />
                </aside>
                <?php endif; ?>

                <!-- Content -->
                <div class="<?php echo $this->countModules('sidebar-a') || $this->countModules('sidebar-b') ? 'col-lg-9 col-md-8' : 'col-12'; ?> main-content">
                    
                    <!-- Content Top -->
                    <?php if ($this->countModules('content-top')) : ?>
                    <section class="content-top mb-4">
                        <jdoc:include type="modules" name="content-top" style="html5" />
                    </section>
                    <?php endif; ?>

                    <!-- Component Output -->
                    <div class="component-content">
                        <jdoc:include type="component" />
                    </div>

                    <!-- Content Bottom -->
                    <?php if ($this->countModules('content-bottom')) : ?>
                    <section class="content-bottom mt-4">
                        <jdoc:include type="modules" name="content-bottom" style="html5" />
                    </section>
                    <?php endif; ?>
                </div>

                <!-- Sidebar B -->
                <?php if ($this->countModules('sidebar-b')) : ?>
                <aside class="col-lg-3 col-md-4 sidebar-b">
                    <jdoc:include type="modules" name="sidebar-b" style="html5" />
                </aside>
                <?php endif; ?>
            </div>

            <!-- Bottom Content -->
            <?php if ($this->countModules('main-bottom')) : ?>
            <section class="main-bottom py-4">
                <jdoc:include type="modules" name="main-bottom" style="html5" />
            </section>
            <?php endif; ?>
        </div>
    </main>

    <!-- Footer -->
    <footer class="site-footer bg-dark text-white mt-5">
        <div class="<?php echo $layoutStyle === 'boxed' ? 'container' : 'container-fluid'; ?>">
            <!-- Footer Content -->
            <?php if ($this->countModules('footer')) : ?>
            <div class="footer-content py-5">
                <jdoc:include type="modules" name="footer" style="html5" />
            </div>
            <?php endif; ?>

            <!-- Copyright -->
            <div class="footer-bottom border-top border-secondary py-3">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="mb-0">
                            &copy; <?php echo date('Y'); ?> <?php echo $sitename; ?>.
                            <?php echo Text::_('TPL_ELECTRONICS_SHOP_COPYRIGHT'); ?>
                        </p>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="social-links">
                            <?php if ($this->params->get('socialFacebook')) : ?>
                                <a href="<?php echo $this->params->get('socialFacebook'); ?>" class="text-white me-3" target="_blank">
                                    <i class="fab fa-facebook"></i>
                                </a>
                            <?php endif; ?>
                            <?php if ($this->params->get('socialInstagram')) : ?>
                                <a href="<?php echo $this->params->get('socialInstagram'); ?>" class="text-white me-3" target="_blank">
                                    <i class="fab fa-instagram"></i>
                                </a>
                            <?php endif; ?>
                            <?php if ($this->params->get('socialYoutube')) : ?>
                                <a href="<?php echo $this->params->get('socialYoutube'); ?>" class="text-white" target="_blank">
                                    <i class="fab fa-youtube"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Initialize AOS if enabled -->
    <?php if ($enableAnimations) : ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof AOS !== 'undefined') {
                AOS.init({
                    duration: 800,
                    once: true
                });
            }
        });
    </script>
    <?php endif; ?>

    <jdoc:include type="modules" name="debug" style="none" />
</body>
</html>
