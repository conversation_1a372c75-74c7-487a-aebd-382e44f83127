#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ứng Dụng Quản <PERSON> - <PERSON><PERSON>ch <PERSON>t Đơn
Phiên bản GUI với Python Tkinter

Sinh viên thực hiện: <PERSON><PERSON><PERSON> sinh: 28/04/1994
Email: <EMAIL>
Điện thoại: 0355771075
Lớp: DX23TT11
Mã sinh viên: 170123488
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import json
from datetime import datetime

class HocSinh:
    """Lớp đại diện cho một học sinh"""
    def __init__(self, ma_so, ho_ten, nam_sinh, khoi, lop, diem_tb):
        self.ma_so = ma_so
        self.ho_ten = ho_ten
        self.nam_sinh = nam_sinh
        self.khoi = khoi
        self.lop = lop
        self.diem_tb = diem_tb
    
    def to_dict(self):
        """Chuyển đổi thành dictionary"""
        return {
            'ma_so': self.ma_so,
            'ho_ten': self.ho_ten,
            'nam_sinh': self.nam_sinh,
            'khoi': self.khoi,
            'lop': self.lop,
            'diem_tb': self.diem_tb
        }
    
    @classmethod
    def from_dict(cls, data):
        """Tạo đối tượng từ dictionary"""
        return cls(
            data['ma_so'],
            data['ho_ten'],
            data['nam_sinh'],
            data['khoi'],
            data['lop'],
            data['diem_tb']
        )

class Node:
    """Nút trong danh sách liên kết đơn"""
    def __init__(self, hoc_sinh):
        self.data = hoc_sinh
        self.next = None

class LinkedList:
    """Cấu trúc dữ liệu Danh Sách Liên Kết Đơn"""
    def __init__(self):
        self.head = None
        self.size = 0
    
    def append(self, hoc_sinh):
        """Thêm học sinh vào cuối danh sách"""
        new_node = Node(hoc_sinh)
        if not self.head:
            self.head = new_node
        else:
            current = self.head
            while current.next:
                current = current.next
            current.next = new_node
        self.size += 1
    
    def delete_by_ma_so(self, ma_so):
        """Xóa học sinh theo mã số"""
        if not self.head:
            return False
        
        if self.head.data.ma_so == ma_so:
            self.head = self.head.next
            self.size -= 1
            return True
        
        current = self.head
        while current.next and current.next.data.ma_so != ma_so:
            current = current.next
        
        if current.next:
            current.next = current.next.next
            self.size -= 1
            return True
        return False
    
    def find_by_ma_so(self, ma_so):
        """Tìm học sinh theo mã số"""
        current = self.head
        while current:
            if current.data.ma_so == ma_so:
                return current.data
            current = current.next
        return None
    
    def to_list(self):
        """Chuyển đổi thành danh sách Python"""
        result = []
        current = self.head
        while current:
            result.append(current.data)
            current = current.next
        return result
    
    def clear(self):
        """Xóa tất cả học sinh"""
        self.head = None
        self.size = 0
    
    def update_student(self, ma_so, new_data):
        """Cập nhật thông tin học sinh"""
        student = self.find_by_ma_so(ma_so)
        if student:
            student.ho_ten = new_data.ho_ten
            student.nam_sinh = new_data.nam_sinh
            student.khoi = new_data.khoi
            student.lop = new_data.lop
            student.diem_tb = new_data.diem_tb
            return True
        return False

class StudentManagementGUI:
    """Giao diện chính của ứng dụng"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Quản Lý Học Sinh - Danh Sách Liên Kết Đơn")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # Khởi tạo danh sách liên kết
        self.student_list = LinkedList()
        
        # Biến để lưu trạng thái chỉnh sửa
        self.editing_student = None
        
        # Tạo giao diện
        self.create_widgets()
        
        # Thêm dữ liệu mẫu
        self.load_sample_data()
        
        # Cập nhật hiển thị
        self.refresh_display()
    
    def create_widgets(self):
        """Tạo các widget cho giao diện"""
        
        # Header
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=100)
        header_frame.pack(fill='x', padx=10, pady=(10, 5))
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(
            header_frame,
            text="🎓 QUẢN LÝ HỌC SINH",
            font=('Arial', 20, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(pady=10)
        
        subtitle_label = tk.Label(
            header_frame,
            text="Ứng dụng demo sử dụng cấu trúc dữ liệu Danh Sách Liên Kết Đơn",
            font=('Arial', 12),
            fg='#ecf0f1',
            bg='#2c3e50'
        )
        subtitle_label.pack()
        
        # Student Info
        info_frame = tk.LabelFrame(
            self.root,
            text="👨‍🎓 Thông tin sinh viên thực hiện",
            font=('Arial', 12, 'bold'),
            fg='#2c3e50',
            bg='#ecf0f1',
            padx=10,
            pady=10
        )
        info_frame.pack(fill='x', padx=10, pady=5)
        
        info_text = """Họ tên: Huỳnh Thái Bảo  |  Ngày sinh: 28/04/1994  |  Email: <EMAIL>
Điện thoại: 0355771075  |  Tài khoản: baoht280494  |  Lớp: DX23TT11  |  MSSV: 170123488"""
        
        info_label = tk.Label(
            info_frame,
            text=info_text,
            font=('Arial', 10),
            fg='#34495e',
            bg='#ecf0f1',
            justify='center'
        )
        info_label.pack()
        
        # Main content frame
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Left panel - Form
        self.create_form_panel(main_frame)
        
        # Center panel - Controls
        self.create_control_panel(main_frame)
        
        # Right panel - Visualization
        self.create_visualization_panel(main_frame)
        
        # Bottom panel - Table
        self.create_table_panel(main_frame)
    
    def create_form_panel(self, parent):
        """Tạo panel form thêm/sửa học sinh"""
        form_frame = tk.LabelFrame(
            parent,
            text="➕ Thêm/Sửa Học Sinh",
            font=('Arial', 12, 'bold'),
            fg='#27ae60',
            bg='white',
            padx=15,
            pady=15
        )
        form_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 5), pady=(0, 5))
        
        # Form fields
        fields = [
            ("Mã số:", "ma_so"),
            ("Họ tên:", "ho_ten"),
            ("Năm sinh:", "nam_sinh"),
            ("Khối:", "khoi"),
            ("Lớp:", "lop"),
            ("Điểm TB:", "diem_tb")
        ]
        
        self.form_vars = {}
        
        for i, (label_text, var_name) in enumerate(fields):
            tk.Label(
                form_frame,
                text=label_text,
                font=('Arial', 10, 'bold'),
                bg='white'
            ).grid(row=i, column=0, sticky='w', pady=5)
            
            if var_name == "khoi":
                var = tk.StringVar()
                combo = ttk.Combobox(
                    form_frame,
                    textvariable=var,
                    values=["10", "11", "12"],
                    state="readonly",
                    width=25
                )
                combo.grid(row=i, column=1, sticky='ew', pady=5, padx=(10, 0))
            else:
                var = tk.StringVar()
                entry = tk.Entry(
                    form_frame,
                    textvariable=var,
                    font=('Arial', 10),
                    width=25
                )
                entry.grid(row=i, column=1, sticky='ew', pady=5, padx=(10, 0))
            
            self.form_vars[var_name] = var
        
        # Buttons
        button_frame = tk.Frame(form_frame, bg='white')
        button_frame.grid(row=len(fields), column=0, columnspan=2, pady=15)
        
        self.add_button = tk.Button(
            button_frame,
            text="➕ Thêm học sinh",
            command=self.add_student,
            bg='#27ae60',
            fg='white',
            font=('Arial', 10, 'bold'),
            padx=20,
            pady=8,
            cursor='hand2'
        )
        self.add_button.pack(side='left', padx=5)
        
        tk.Button(
            button_frame,
            text="🔄 Reset",
            command=self.reset_form,
            bg='#95a5a6',
            fg='white',
            font=('Arial', 10, 'bold'),
            padx=20,
            pady=8,
            cursor='hand2'
        ).pack(side='left', padx=5)
        
        form_frame.columnconfigure(1, weight=1)
    
    def create_control_panel(self, parent):
        """Tạo panel điều khiển"""
        control_frame = tk.LabelFrame(
            parent,
            text="🔍 Tìm Kiếm & Điều Khiển",
            font=('Arial', 12, 'bold'),
            fg='#3498db',
            bg='white',
            padx=15,
            pady=15
        )
        control_frame.grid(row=0, column=1, sticky='nsew', padx=5, pady=(0, 5))
        
        # Search section
        search_frame = tk.Frame(control_frame, bg='white')
        search_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(
            search_frame,
            text="Tìm theo mã số:",
            font=('Arial', 10, 'bold'),
            bg='white'
        ).pack(anchor='w')
        
        search_entry_frame = tk.Frame(search_frame, bg='white')
        search_entry_frame.pack(fill='x', pady=5)
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(
            search_entry_frame,
            textvariable=self.search_var,
            font=('Arial', 10),
            width=20
        )
        search_entry.pack(side='left', fill='x', expand=True)
        
        tk.Button(
            search_entry_frame,
            text="🔍",
            command=self.search_student,
            bg='#3498db',
            fg='white',
            font=('Arial', 10, 'bold'),
            padx=10,
            cursor='hand2'
        ).pack(side='right', padx=(5, 0))
        
        # Filter section
        filter_frame = tk.Frame(control_frame, bg='white')
        filter_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(
            filter_frame,
            text="Lọc theo khối:",
            font=('Arial', 10, 'bold'),
            bg='white'
        ).pack(anchor='w')
        
        self.filter_var = tk.StringVar()
        filter_combo = ttk.Combobox(
            filter_frame,
            textvariable=self.filter_var,
            values=["Tất cả", "10", "11", "12"],
            state="readonly",
            width=25
        )
        filter_combo.pack(fill='x', pady=5)
        filter_combo.set("Tất cả")
        filter_combo.bind('<<ComboboxSelected>>', self.filter_by_khoi)
        
        # Action buttons
        action_frame = tk.Frame(control_frame, bg='white')
        action_frame.pack(fill='x')
        
        buttons = [
            ("📊 Thống kê", self.show_statistics, '#f39c12'),
            ("🔄 Sắp xếp mã số", self.sort_by_ma_so, '#9b59b6'),
            ("📈 Sắp xếp điểm", self.sort_by_diem, '#e74c3c'),
            ("🗑️ Xóa tất cả", self.clear_all, '#e74c3c')
        ]
        
        for i, (text, command, color) in enumerate(buttons):
            btn = tk.Button(
                action_frame,
                text=text,
                command=command,
                bg=color,
                fg='white',
                font=('Arial', 9, 'bold'),
                padx=10,
                pady=5,
                cursor='hand2'
            )
            btn.grid(row=i//2, column=i%2, sticky='ew', padx=2, pady=2)
        
        action_frame.columnconfigure(0, weight=1)
        action_frame.columnconfigure(1, weight=1)
    
    def create_visualization_panel(self, parent):
        """Tạo panel minh họa cấu trúc liên kết"""
        viz_frame = tk.LabelFrame(
            parent,
            text="🔗 Minh Họa Danh Sách Liên Kết Đơn",
            font=('Arial', 12, 'bold'),
            fg='#e67e22',
            bg='white',
            padx=15,
            pady=15
        )
        viz_frame.grid(row=0, column=2, sticky='nsew', padx=(5, 0), pady=(0, 5))
        
        # Canvas for visualization
        self.viz_canvas = tk.Canvas(
            viz_frame,
            bg='#f8f9fa',
            height=200,
            relief='sunken',
            bd=2
        )
        self.viz_canvas.pack(fill='both', expand=True)
        
        # Scrollbar for canvas
        viz_scrollbar = ttk.Scrollbar(
            viz_frame,
            orient='horizontal',
            command=self.viz_canvas.xview
        )
        viz_scrollbar.pack(fill='x')
        self.viz_canvas.configure(xscrollcommand=viz_scrollbar.set)
    
    def create_table_panel(self, parent):
        """Tạo panel bảng dữ liệu"""
        table_frame = tk.LabelFrame(
            parent,
            text="📋 Danh Sách Học Sinh",
            font=('Arial', 12, 'bold'),
            fg='#8e44ad',
            bg='white',
            padx=15,
            pady=15
        )
        table_frame.grid(row=1, column=0, columnspan=3, sticky='nsew', pady=(5, 0))
        
        # Treeview for student table
        columns = ('STT', 'Mã số', 'Họ tên', 'Năm sinh', 'Khối', 'Lớp', 'Điểm TB')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)
        
        # Define headings
        for col in columns:
            self.tree.heading(col, text=col)
            if col == 'STT':
                self.tree.column(col, width=50, anchor='center')
            elif col == 'Mã số':
                self.tree.column(col, width=80, anchor='center')
            elif col == 'Họ tên':
                self.tree.column(col, width=200, anchor='w')
            elif col in ['Năm sinh', 'Khối', 'Lớp']:
                self.tree.column(col, width=80, anchor='center')
            else:
                self.tree.column(col, width=100, anchor='center')
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack widgets
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # Context menu
        self.create_context_menu()
        
        # Bind events
        self.tree.bind('<Double-1>', self.on_item_double_click)
        self.tree.bind('<Button-3>', self.show_context_menu)
        
        # Configure grid weights
        table_frame.rowconfigure(0, weight=1)
        table_frame.columnconfigure(0, weight=1)
        
        # Stats label
        self.stats_label = tk.Label(
            table_frame,
            text="Tổng: 0 học sinh",
            font=('Arial', 10, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        self.stats_label.grid(row=2, column=0, columnspan=2, pady=5)
        
        # Configure main grid weights
        parent.rowconfigure(0, weight=1)
        parent.rowconfigure(1, weight=2)
        parent.columnconfigure(0, weight=1)
        parent.columnconfigure(1, weight=1)
        parent.columnconfigure(2, weight=1)

    def create_context_menu(self):
        """Tạo context menu cho bảng"""
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="✏️ Chỉnh sửa", command=self.edit_selected_student)
        self.context_menu.add_command(label="🗑️ Xóa", command=self.delete_selected_student)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📋 Sao chép", command=self.copy_student_info)

    def load_sample_data(self):
        """Thêm dữ liệu mẫu"""
        sample_students = [
            HocSinh("HS001", "Nguyễn Văn An", 2005, "12", "A1", 8.5),
            HocSinh("HS002", "Trần Thị Bình", 2006, "11", "B2", 7.2),
            HocSinh("HS003", "Lê Văn Cường", 2007, "10", "C3", 6.8),
            HocSinh("HS004", "Phạm Thị Dung", 2005, "12", "A2", 9.1),
            HocSinh("HS005", "Hoàng Văn Em", 2006, "11", "B1", 5.5)
        ]

        for student in sample_students:
            self.student_list.append(student)

    def validate_input(self):
        """Kiểm tra tính hợp lệ của dữ liệu đầu vào"""
        try:
            ma_so = self.form_vars['ma_so'].get().strip()
            ho_ten = self.form_vars['ho_ten'].get().strip()
            nam_sinh = int(self.form_vars['nam_sinh'].get())
            khoi = self.form_vars['khoi'].get()
            lop = self.form_vars['lop'].get().strip()
            diem_tb = float(self.form_vars['diem_tb'].get())

            if not ma_so or len(ma_so) < 3:
                raise ValueError("Mã số phải có ít nhất 3 ký tự")

            if not ho_ten or len(ho_ten) < 2:
                raise ValueError("Họ tên phải có ít nhất 2 ký tự")

            if nam_sinh < 1990 or nam_sinh > 2010:
                raise ValueError("Năm sinh phải từ 1990 đến 2010")

            if not khoi:
                raise ValueError("Vui lòng chọn khối")

            if not lop:
                raise ValueError("Lớp không được để trống")

            if diem_tb < 0 or diem_tb > 10:
                raise ValueError("Điểm trung bình phải từ 0 đến 10")

            return HocSinh(ma_so, ho_ten, nam_sinh, khoi, lop, diem_tb)

        except ValueError as e:
            messagebox.showerror("Lỗi nhập liệu", str(e))
            return None

    def add_student(self):
        """Thêm hoặc cập nhật học sinh"""
        student = self.validate_input()
        if not student:
            return

        if self.editing_student:
            # Cập nhật học sinh
            if self.student_list.update_student(self.editing_student, student):
                messagebox.showinfo("Thành công", "Cập nhật học sinh thành công!")
                self.editing_student = None
                self.add_button.config(text="➕ Thêm học sinh")
            else:
                messagebox.showerror("Lỗi", "Không thể cập nhật học sinh!")
        else:
            # Kiểm tra trùng mã số
            if self.student_list.find_by_ma_so(student.ma_so):
                messagebox.showerror("Lỗi", "Mã số học sinh đã tồn tại!")
                return

            # Thêm học sinh mới
            self.student_list.append(student)
            messagebox.showinfo("Thành công", "Thêm học sinh thành công!")

        self.reset_form()
        self.refresh_display()

    def reset_form(self):
        """Reset form về trạng thái ban đầu"""
        for var in self.form_vars.values():
            var.set("")
        self.editing_student = None
        self.add_button.config(text="➕ Thêm học sinh")

    def search_student(self):
        """Tìm kiếm học sinh theo mã số"""
        ma_so = self.search_var.get().strip()
        if not ma_so:
            messagebox.showwarning("Cảnh báo", "Vui lòng nhập mã số cần tìm!")
            return

        student = self.student_list.find_by_ma_so(ma_so)
        if student:
            # Highlight trong bảng
            for item in self.tree.get_children():
                if self.tree.item(item)['values'][1] == ma_so:
                    self.tree.selection_set(item)
                    self.tree.focus(item)
                    self.tree.see(item)
                    break
            messagebox.showinfo("Tìm thấy", f"Tìm thấy học sinh: {student.ho_ten}")
        else:
            messagebox.showinfo("Không tìm thấy", "Không tìm thấy học sinh với mã số này!")

    def filter_by_khoi(self, event=None):
        """Lọc học sinh theo khối"""
        self.refresh_display()

    def sort_by_ma_so(self):
        """Sắp xếp theo mã số"""
        students = self.student_list.to_list()
        students.sort(key=lambda x: x.ma_so)
        self.display_students(students)
        messagebox.showinfo("Thành công", "Đã sắp xếp theo mã số!")

    def sort_by_diem(self):
        """Sắp xếp theo điểm trung bình (giảm dần)"""
        students = self.student_list.to_list()
        students.sort(key=lambda x: x.diem_tb, reverse=True)
        self.display_students(students)
        messagebox.showinfo("Thành công", "Đã sắp xếp theo điểm (giảm dần)!")

    def clear_all(self):
        """Xóa tất cả học sinh"""
        if messagebox.askyesno("Xác nhận", f"Bạn có chắc chắn muốn xóa tất cả {self.student_list.size} học sinh?"):
            self.student_list.clear()
            self.refresh_display()
            messagebox.showinfo("Thành công", "Đã xóa tất cả học sinh!")

    def show_statistics(self):
        """Hiển thị thống kê"""
        students = self.student_list.to_list()
        if not students:
            messagebox.showwarning("Cảnh báo", "Không có dữ liệu để thống kê!")
            return

        # Thống kê theo khối
        khoi_stats = {"10": 0, "11": 0, "12": 0}
        for student in students:
            khoi_stats[student.khoi] += 1

        # Thống kê điểm
        diem_list = [s.diem_tb for s in students]
        max_diem = max(diem_list)
        min_diem = min(diem_list)
        avg_diem = sum(diem_list) / len(diem_list)

        # Học sinh xuất sắc và cần hỗ trợ
        top_student = next(s for s in students if s.diem_tb == max_diem)
        low_student = next(s for s in students if s.diem_tb == min_diem)

        # Tỉ lệ đạt/không đạt
        passed = len([s for s in students if s.diem_tb >= 5.0])
        failed = len(students) - passed
        pass_rate = (passed / len(students)) * 100

        stats_text = f"""
📊 THỐNG KÊ DỮ LIỆU HỌC SINH

📈 Tổng quan:
• Tổng số học sinh: {len(students)}
• Điểm trung bình chung: {avg_diem:.2f}
• Tỉ lệ đạt yêu cầu: {pass_rate:.1f}%

📚 Theo khối:
• Khối 10: {khoi_stats['10']} học sinh
• Khối 11: {khoi_stats['11']} học sinh
• Khối 12: {khoi_stats['12']} học sinh

🏆 Học sinh xuất sắc:
• Điểm cao nhất: {max_diem:.1f}
• {top_student.ho_ten} ({top_student.ma_so}) - Lớp {top_student.lop}

⚠️ Học sinh cần hỗ trợ:
• Điểm thấp nhất: {min_diem:.1f}
• {low_student.ho_ten} ({low_student.ma_so}) - Lớp {low_student.lop}

📊 Kết quả học tập:
• Học sinh đạt (≥5.0): {passed} ({pass_rate:.1f}%)
• Học sinh không đạt (<5.0): {failed} ({100-pass_rate:.1f}%)
        """

        # Tạo cửa sổ thống kê
        stats_window = tk.Toplevel(self.root)
        stats_window.title("📊 Thống Kê Dữ Liệu")
        stats_window.geometry("500x600")
        stats_window.configure(bg='white')
        stats_window.resizable(False, False)

        # Center the window
        stats_window.transient(self.root)
        stats_window.grab_set()

        text_widget = tk.Text(
            stats_window,
            font=('Consolas', 11),
            bg='#f8f9fa',
            fg='#2c3e50',
            padx=20,
            pady=20,
            wrap='word',
            state='normal'
        )
        text_widget.pack(fill='both', expand=True, padx=10, pady=10)

        text_widget.insert('1.0', stats_text)
        text_widget.config(state='disabled')

        # Close button
        tk.Button(
            stats_window,
            text="Đóng",
            command=stats_window.destroy,
            bg='#3498db',
            fg='white',
            font=('Arial', 12, 'bold'),
            padx=30,
            pady=10,
            cursor='hand2'
        ).pack(pady=10)

    def on_item_double_click(self, event):
        """Xử lý double click trên item trong bảng"""
        self.edit_selected_student()

    def show_context_menu(self, event):
        """Hiển thị context menu"""
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def edit_selected_student(self):
        """Chỉnh sửa học sinh được chọn"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn học sinh cần chỉnh sửa!")
            return

        item = selected[0]
        values = self.tree.item(item)['values']
        ma_so = values[1]

        student = self.student_list.find_by_ma_so(ma_so)
        if student:
            # Điền dữ liệu vào form
            self.form_vars['ma_so'].set(student.ma_so)
            self.form_vars['ho_ten'].set(student.ho_ten)
            self.form_vars['nam_sinh'].set(str(student.nam_sinh))
            self.form_vars['khoi'].set(student.khoi)
            self.form_vars['lop'].set(student.lop)
            self.form_vars['diem_tb'].set(str(student.diem_tb))

            # Chuyển sang chế độ chỉnh sửa
            self.editing_student = ma_so
            self.add_button.config(text="💾 Cập nhật học sinh")

            messagebox.showinfo("Chỉnh sửa", f"Đang chỉnh sửa học sinh: {student.ho_ten}")

    def delete_selected_student(self):
        """Xóa học sinh được chọn"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn học sinh cần xóa!")
            return

        item = selected[0]
        values = self.tree.item(item)['values']
        ma_so = values[1]
        ho_ten = values[2]

        if messagebox.askyesno("Xác nhận xóa", f"Bạn có chắc chắn muốn xóa học sinh {ho_ten} ({ma_so})?"):
            if self.student_list.delete_by_ma_so(ma_so):
                self.refresh_display()
                messagebox.showinfo("Thành công", f"Đã xóa học sinh: {ho_ten}")
            else:
                messagebox.showerror("Lỗi", "Không thể xóa học sinh!")

    def copy_student_info(self):
        """Sao chép thông tin học sinh"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn học sinh!")
            return

        item = selected[0]
        values = self.tree.item(item)['values']

        info_text = f"""Mã số: {values[1]}
Họ tên: {values[2]}
Năm sinh: {values[3]}
Khối: {values[4]}
Lớp: {values[5]}
Điểm TB: {values[6]}"""

        self.root.clipboard_clear()
        self.root.clipboard_append(info_text)
        messagebox.showinfo("Thành công", "Đã sao chép thông tin học sinh!")

    def display_students(self, students=None):
        """Hiển thị danh sách học sinh trong bảng"""
        # Xóa dữ liệu cũ
        for item in self.tree.get_children():
            self.tree.delete(item)

        if students is None:
            students = self.student_list.to_list()

        # Lọc theo khối nếu cần
        filter_khoi = self.filter_var.get()
        if filter_khoi and filter_khoi != "Tất cả":
            students = [s for s in students if s.khoi == filter_khoi]

        # Thêm dữ liệu mới
        for i, student in enumerate(students, 1):
            self.tree.insert('', 'end', values=(
                i,
                student.ma_so,
                student.ho_ten,
                student.nam_sinh,
                f"Khối {student.khoi}",
                student.lop,
                f"{student.diem_tb:.1f}"
            ))

        # Cập nhật thống kê
        self.stats_label.config(text=f"Tổng: {len(students)} học sinh")

        # Cập nhật visualization
        self.update_visualization(students)

    def update_visualization(self, students):
        """Cập nhật minh họa cấu trúc liên kết"""
        self.viz_canvas.delete("all")

        if not students:
            self.viz_canvas.create_text(
                200, 100,
                text="Danh sách rỗng\nHãy thêm học sinh để xem cấu trúc liên kết",
                font=('Arial', 12, 'italic'),
                fill='#6c757d',
                justify='center'
            )
            return

        # Vẽ các node
        x_start = 20
        y_center = 100
        node_width = 120
        node_height = 60
        spacing = 40

        total_width = len(students) * (node_width + spacing) - spacing + 40
        self.viz_canvas.configure(scrollregion=(0, 0, total_width, 200))

        for i, student in enumerate(students):
            x = x_start + i * (node_width + spacing)

            # Vẽ node
            rect = self.viz_canvas.create_rectangle(
                x, y_center - node_height//2,
                x + node_width, y_center + node_height//2,
                fill='#3498db',
                outline='#2980b9',
                width=2
            )

            # Thêm text
            self.viz_canvas.create_text(
                x + node_width//2, y_center - 15,
                text=student.ma_so,
                font=('Arial', 10, 'bold'),
                fill='white'
            )

            self.viz_canvas.create_text(
                x + node_width//2, y_center + 5,
                text=student.ho_ten[:15] + ("..." if len(student.ho_ten) > 15 else ""),
                font=('Arial', 8),
                fill='white'
            )

            # Vẽ mũi tên next
            if i < len(students) - 1:
                arrow_start_x = x + node_width
                arrow_end_x = x + node_width + spacing

                self.viz_canvas.create_line(
                    arrow_start_x, y_center,
                    arrow_end_x, y_center,
                    arrow=tk.LAST,
                    fill='#e74c3c',
                    width=3
                )
            else:
                # NULL pointer
                self.viz_canvas.create_text(
                    x + node_width + 20, y_center,
                    text="NULL",
                    font=('Arial', 10, 'bold'),
                    fill='#e74c3c'
                )

    def refresh_display(self):
        """Làm mới hiển thị"""
        self.display_students()

    def save_data(self):
        """Lưu dữ liệu ra file JSON"""
        try:
            students = self.student_list.to_list()
            data = [student.to_dict() for student in students]

            filename = f"students_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("Thành công", f"Đã lưu dữ liệu vào file: {filename}")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể lưu dữ liệu: {str(e)}")

    def load_data(self):
        """Tải dữ liệu từ file JSON"""
        try:
            from tkinter import filedialog
            filename = filedialog.askopenfilename(
                title="Chọn file dữ liệu",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.student_list.clear()
                for item in data:
                    student = HocSinh.from_dict(item)
                    self.student_list.append(student)

                self.refresh_display()
                messagebox.showinfo("Thành công", f"Đã tải {len(data)} học sinh từ file!")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể tải dữ liệu: {str(e)}")

def main():
    """Hàm chính"""
    root = tk.Tk()

    # Set icon (nếu có)
    try:
        root.iconbitmap('icon.ico')
    except:
        pass

    # Tạo ứng dụng
    app = StudentManagementGUI(root)

    # Thêm menu bar
    menubar = tk.Menu(root)
    root.config(menu=menubar)

    # File menu
    file_menu = tk.Menu(menubar, tearoff=0)
    menubar.add_cascade(label="📁 File", menu=file_menu)
    file_menu.add_command(label="💾 Lưu dữ liệu", command=app.save_data)
    file_menu.add_command(label="📂 Tải dữ liệu", command=app.load_data)
    file_menu.add_separator()
    file_menu.add_command(label="🚪 Thoát", command=root.quit)

    # Help menu
    help_menu = tk.Menu(menubar, tearoff=0)
    menubar.add_cascade(label="❓ Trợ giúp", menu=help_menu)
    help_menu.add_command(label="ℹ️ Về chương trình", command=lambda: messagebox.showinfo(
        "Về chương trình",
        """🎓 Quản Lý Học Sinh - Danh Sách Liên Kết Đơn

Phiên bản: 1.0
Ngôn ngữ: Python + Tkinter

👨‍🎓 Sinh viên thực hiện:
Huỳnh Thái Bảo
Lớp: DX23TT11
MSSV: 170123488

📧 Liên hệ: <EMAIL>
📱 Điện thoại: 0355771075

© 2024 - Đồ án Cấu trúc dữ liệu và Giải thuật"""
    ))

    # Chạy ứng dụng
    root.mainloop()

if __name__ == "__main__":
    main()
