# Hướng Dẫn Khởi Động Nhanh - Website B<PERSON> Thiết Bị Điện Tử Cũ

## Tổng quan dự án
Website thương mại điện tử chuyên bán thiết bị điện tử cũ được xây dựng trên nền tảng Joomla 4 với VirtueMart extension.

## Chuẩn bị môi trường

### Y<PERSON>u cầu hệ thống
- **Web Server**: Apache 2.4+
- **PHP**: 8.0+ (khuyến nghị 8.1)
- **Database**: MySQL 5.7+ hoặc MariaDB 10.3+
- **RAM**: Tố<PERSON> thiểu 4GB
- **Dung lượng**: T<PERSON><PERSON> thiểu 2GB trống

### Cài đặt nhanh (Windows)
```bash
# 1. Cài đặt XAMPP
.\install-xampp.bat

# 2. Tải và cài đặt Joomla
.\download-joomla.ps1

# 3. Tả<PERSON> VirtueMart
.\download-virtuemart.ps1
```

## Cài đặt từng bước

### Bước 1: T<PERSON><PERSON><PERSON> lập XAMPP
1. Chạy `install-xampp.bat` vớ<PERSON> quyền Administrator
2. Khởi động Apache và MySQL services
3. Truy cập http://localhost/phpmyadmin
4. Tạo database `electronics_shop`

### Bước 2: Cài đặt Joomla
1. Chạy `download-joomla.ps1` với quyền Administrator
2. Truy cập http://localhost/electronics-shop
3. Làm theo wizard cài đặt:
   - Site Name: `Electronics Shop - Thiết Bị Điện Tử Cũ`
   - Admin Username: `admin`
   - Database: `electronics_shop`

### Bước 3: Cài đặt VirtueMart
1. Chạy `download-virtuemart.ps1`
2. Đăng nhập Joomla Admin Panel
3. Vào `System > Install > Extensions`
4. Upload và cài đặt theo thứ tự:
   - `com_virtuemart_4.x.x.zip`
   - `plg_vmpayment_standard.zip`
   - `plg_vmshipment_standard.zip`
   - `plg_vminvoice_standard.zip`
   - `virtuemart_vi-VN.zip`

### Bước 4: Import Database Design
1. Mở phpMyAdmin
2. Chọn database `electronics_shop`
3. Import file `Database/database-design.sql`
4. Import file `Sample-Data/sample-products.sql`

### Bước 5: Cài đặt Template
1. Nén thư mục `Templates/electronics-shop` thành file ZIP
2. Vào `System > Install > Extensions`
3. Upload và cài đặt template
4. Vào `System > Site Templates`
5. Set `Electronics Shop` làm default template

### Bước 6: Cài đặt Module tùy chỉnh
1. Nén thư mục `Custom-Extensions/mod_product_condition` thành ZIP
2. Upload và cài đặt module
3. Vào `Content > Site Modules`
4. Tạo module mới với type `Product Condition`

## Cấu hình cơ bản

### VirtueMart Configuration
1. Vào `Components > VirtueMart > Configuration`
2. Cấu hình:
   - Shop Name: `Electronics Shop`
   - Currency: `Vietnamese Dong (VND)`
   - Country: `Vietnam`
   - Language: `Vietnamese`

### Tạo Menu Items
1. Vào `Menus > Main Menu`
2. Tạo các menu items:
   - `Trang chủ` (Home)
   - `Cửa hàng` (VirtueMart Category Layout)
   - `Laptop cũ` (VirtueMart Category)
   - `Điện thoại cũ` (VirtueMart Category)
   - `Giỏ hàng` (VirtueMart Cart)
   - `Liên hệ` (Contact)

### Cấu hình Payment Methods
1. Vào `VirtueMart > Payment Methods`
2. Tạo các phương thức:
   - **COD**: Thanh toán khi nhận hàng
   - **Bank Transfer**: Chuyển khoản ngân hàng
   - **E-Wallet**: Ví điện tử (MoMo, ZaloPay)

### Cấu hình Shipping Methods
1. Vào `VirtueMart > Shipment Methods`
2. Tạo các phương thức:
   - **Standard**: 30,000 VND
   - **Express**: 50,000 VND
   - **Free**: Miễn phí (đơn hàng > 5 triệu)

## Tạo nội dung mẫu

### Danh mục sản phẩm
1. Vào `VirtueMart > Categories`
2. Tạo các danh mục:
   - Laptop cũ
     - Laptop văn phòng
     - Laptop gaming
     - Laptop đồ họa
   - Điện thoại cũ
     - iPhone cũ
     - Samsung cũ
     - Xiaomi cũ
   - Máy tính bảng
   - Phụ kiện
   - Linh kiện

### Sản phẩm mẫu
1. Vào `VirtueMart > Products`
2. Tạo sản phẩm với thông tin:
   - Tên sản phẩm
   - Mô tả chi tiết
   - Giá cả
   - Hình ảnh
   - Tình trạng máy
   - Thông số kỹ thuật
   - Bảo hành

### Upload hình ảnh
1. Tạo thư mục `images/stories/virtuemart/product/`
2. Upload hình ảnh sản phẩm
3. Liên kết với sản phẩm trong VirtueMart

## Cấu hình Module Positions

### Header Modules
- **search**: Module tìm kiếm VirtueMart
- **cart**: Module giỏ hàng VirtueMart
- **user-menu**: Module đăng nhập/đăng ký

### Sidebar Modules
- **sidebar-a**: 
  - VirtueMart Categories
  - Product Condition Filter
  - VirtueMart Featured Products
- **sidebar-b**: 
  - VirtueMart Latest Products
  - Newsletter Signup

### Content Modules
- **content-top**: Banner quảng cáo
- **content-bottom**: Testimonials

### Footer Modules
- **footer**: 
  - Thông tin liên hệ
  - Links hữu ích
  - Social media

## Testing Website

### Checklist chức năng
- [ ] Hiển thị trang chủ
- [ ] Duyệt danh mục sản phẩm
- [ ] Xem chi tiết sản phẩm
- [ ] Lọc theo tình trạng sản phẩm
- [ ] Thêm sản phẩm vào giỏ hàng
- [ ] Quy trình checkout
- [ ] Thanh toán COD
- [ ] Gửi email xác nhận đơn hàng
- [ ] Responsive design (mobile/tablet)

### Test URLs
- Trang chủ: http://localhost/electronics-shop
- Cửa hàng: http://localhost/electronics-shop/shop
- Admin: http://localhost/electronics-shop/administrator

## Tối ưu hóa

### SEO Settings
1. Bật SEF URLs trong Joomla
2. Cấu hình meta descriptions
3. Tối ưu hình ảnh (alt tags)
4. Tạo sitemap.xml

### Performance
1. Bật Joomla caching
2. Tối ưu hình ảnh (WebP format)
3. Minify CSS/JS
4. Sử dụng CDN cho static files

### Security
1. Cập nhật Joomla và extensions
2. Thay đổi mật khẩu admin mặc định
3. Cấu hình .htaccess security rules
4. Backup database định kỳ

## Troubleshooting

### Lỗi thường gặp
1. **Joomla không hiển thị**: Kiểm tra Apache service
2. **Database connection error**: Kiểm tra MySQL service
3. **VirtueMart không hoạt động**: Kiểm tra extensions đã enable
4. **Template không áp dụng**: Kiểm tra template assignment
5. **Hình ảnh không hiển thị**: Kiểm tra file permissions

### Log Files
- Joomla Error Log: `logs/joomla_error.php`
- Apache Error Log: `C:\xampp\apache\logs\error.log`
- MySQL Error Log: `C:\xampp\mysql\data\mysql_error.log`

## Hỗ trợ

### Tài liệu tham khảo
- [Joomla Documentation](https://docs.joomla.org/)
- [VirtueMart Documentation](https://docs.virtuemart.net/)
- [Bootstrap 5 Documentation](https://getbootstrap.com/docs/5.1/)

### Files quan trọng
- `Documentation/INSTALLATION-GUIDE.md` - Hướng dẫn cài đặt chi tiết
- `Documentation/VIRTUEMART-SETUP.md` - Cấu hình VirtueMart
- `Database/database-design.sql` - Thiết kế database
- `Sample-Data/sample-products.sql` - Dữ liệu mẫu

## Kết luận
Sau khi hoàn thành các bước trên, website bán thiết bị điện tử cũ đã sẵn sàng hoạt động với đầy đủ chức năng e-commerce và giao diện chuyên nghiệp.
