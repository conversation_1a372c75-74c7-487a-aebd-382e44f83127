/* Reset và Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: white;
    padding: 2rem 0;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Student Info */
.student-info {
    background: rgba(255, 255, 255, 0.95);
    margin: 2rem 0;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.info-card {
    padding: 2rem;
}

.info-card h3 {
    color: #4a5568;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 0.5rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.info-item {
    background: #f7fafc;
    padding: 0.8rem;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.info-item strong {
    color: #2d3748;
}

/* Main Content */
.main-content {
    background: rgba(255, 255, 255, 0.95);
    margin: 2rem 0;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Control Panel */
.control-panel {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 3rem;
    align-items: start;
}

.panel-section {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    height: fit-content;
}

.panel-section h3 {
    color: #495057;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f8f9fa;
}

/* Form Styles */
.student-form {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    align-items: end;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #374151;
    font-size: 0.9rem;
}

.form-group input,
.form-group select {
    padding: 0.8rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #fafafa;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

.form-group input::placeholder {
    color: #9ca3af;
    font-style: italic;
}

.input-group {
    display: flex;
    gap: 0.5rem;
    align-items: stretch;
}

.input-group input {
    flex: 1;
}

.input-group select {
    min-width: 60px;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
    transform: translateY(-1px);
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
    transform: translateY(-1px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.btn-edit {
    background: #28a745;
    color: white;
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

.btn-delete {
    background: #dc3545;
    color: white;
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

/* Search Controls */
.search-controls {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
}

/* Action Buttons */
.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.8rem;
}

.action-buttons .btn {
    justify-content: center;
    padding: 0.7rem 1rem;
    font-size: 0.9rem;
}

/* Visualization Section */
.visualization-section {
    margin: 3rem 0;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

.visualization-section h3 {
    color: #495057;
    margin-bottom: 1.5rem;
    text-align: center;
}

.linked-list-viz {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    overflow-x: auto;
    padding: 1rem;
}

.empty-list {
    text-align: center;
    color: #6c757d;
    font-style: italic;
}

.node {
    display: flex;
    align-items: center;
    margin: 0 0.5rem;
}

.node-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    min-width: 120px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.5s ease-out;
}

.node-box .student-id {
    font-weight: bold;
    font-size: 0.9rem;
}

.node-box .student-name {
    font-size: 0.8rem;
    opacity: 0.9;
}

.arrow {
    color: #667eea;
    font-size: 1.5rem;
    margin: 0 0.5rem;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Student List Section */
.student-list-section {
    margin-top: 3rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.section-header h3 {
    color: #495057;
    font-size: 1.3rem;
}

.list-stats {
    background: #e3f2fd;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    color: #1976d2;
    font-weight: 600;
}

/* Table Styles */
.table-container {
    overflow-x: auto;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.student-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.student-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
}

.student-table td {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.3s ease;
}

.student-table tbody tr:hover {
    background-color: #f8f9fa;
}

.student-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.student-table tbody tr:nth-child(even):hover {
    background-color: #e9ecef;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
}

.close {
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.close:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 2rem;
}

/* Statistics Styles */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid #667eea;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.stat-label {
    color: #6c757d;
    margin-top: 0.5rem;
}

/* Footer */
.footer {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .control-panel {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .panel-section:last-child {
        grid-column: 1 / -1;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .control-panel {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        grid-template-columns: 1fr;
    }

    .linked-list-viz {
        flex-direction: column;
        gap: 1rem;
    }

    .arrow {
        transform: rotate(90deg);
    }

    .student-table {
        font-size: 0.85rem;
    }

    .student-table th,
    .student-table td {
        padding: 0.5rem 0.3rem;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-success { color: #28a745; }
.text-danger { color: #dc3545; }
.text-warning { color: #ffc107; }
.text-info { color: #17a2b8; }

.bg-success { background-color: #d4edda; }
.bg-danger { background-color: #f8d7da; }
.bg-warning { background-color: #fff3cd; }
.bg-info { background-color: #d1ecf1; }

/* Enhanced Panel Styling */
.panel-section:nth-child(1) {
    border-top: 4px solid #28a745;
}

.panel-section:nth-child(2) {
    border-top: 4px solid #17a2b8;
}

.panel-section:nth-child(3) {
    border-top: 4px solid #ffc107;
}

/* Form Submit Button Enhancement */
.student-form .btn {
    margin-top: 0.5rem;
    padding: 0.9rem 2rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    font-size: 0.9rem;
}

/* Search and Filter Enhancements */
.search-controls .form-group:last-child .input-group {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 0.2rem;
}

.search-controls .form-group:last-child .input-group input {
    border: none;
    background: transparent;
    margin: 0;
}

.search-controls .form-group:last-child .input-group select {
    border: none;
    background: transparent;
    margin: 0;
    border-left: 1px solid #dee2e6;
}

.search-controls .form-group:last-child .input-group .btn {
    border-radius: 6px;
    margin: 0;
}

/* Button Group Styling */
.btn-group {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* Enhanced Table Actions */
.student-table .btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    border-radius: 6px;
    margin: 0 0.2rem;
}

/* Panel Section Icons */
.panel-section h3 i {
    color: #667eea;
    font-size: 1.1rem;
}

/* Improved Spacing */
.main-content {
    padding: 2.5rem;
}

.control-panel {
    margin-bottom: 2.5rem;
}

/* Enhanced Visualization */
.visualization-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
    position: relative;
    overflow: hidden;
}

.visualization-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

/* Card Hover Effects */
.panel-section {
    transition: all 0.3s ease;
}

.panel-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

/* Input Focus Enhancements */
.form-group input:focus,
.form-group select:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

/* Better Button Spacing in Action Section */
.action-buttons {
    gap: 0.6rem;
}

.action-buttons .btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.action-buttons .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.action-buttons .btn:hover::before {
    left: 100%;
}

/* Enhanced Modal */
.modal {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* Improved Stats Display */
.list-stats {
    font-size: 0.9rem;
    padding: 0.6rem 1.2rem;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Form Validation */
.form-group input.error,
.form-group select.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-group input.success,
.form-group select.success {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

/* Tooltip Styles */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: #555;
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.8rem;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Enhanced Table Styling */
.student-table tbody tr {
    transition: all 0.3s ease;
}

.student-table tbody tr.highlight {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    transform: scale(1.02);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Improved Empty State */
.empty-list {
    padding: 3rem;
    text-align: center;
    color: #6c757d;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

.empty-list i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Progress Bar for Statistics */
.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
    transition: width 0.8s ease;
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .panel-section {
        background: #2d3748;
        color: #e2e8f0;
        border-color: #4a5568;
    }

    .form-group input,
    .form-group select {
        background: #4a5568;
        color: #e2e8f0;
        border-color: #718096;
    }

    .student-table {
        background: #2d3748;
        color: #e2e8f0;
    }

    .student-table th {
        background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    }
}

/* Print Styles */
@media print {
    .control-panel,
    .visualization-section,
    .fab {
        display: none;
    }

    .main-content {
        background: white;
        box-shadow: none;
        margin: 0;
        padding: 1rem;
    }

    .student-table {
        font-size: 0.8rem;
    }

    .header {
        background: white;
        color: black;
        box-shadow: none;
    }
}
