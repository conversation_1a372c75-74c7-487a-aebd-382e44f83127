@echo off
title Check Joomla Files
color 0C

echo ========================================
echo    CHECKING JOOMLA INSTALLATION
echo ========================================
echo.

set JOOMLA_PATH=C:\xampp\htdocs\electronics-shop

echo Checking Joomla files in: %JOOMLA_PATH%
echo.

if not exist "%JOOMLA_PATH%" (
    echo [ERROR] Electronics-shop folder not found!
    echo Creating folder...
    mkdir "%JOOMLA_PATH%"
    echo [OK] Folder created: %JO<PERSON>LA_PATH%
    echo.
)

echo Checking essential Joom<PERSON> files:
echo.

if exist "%JOOMLA_PATH%\index.php" (
    echo [OK] index.php found
) else (
    echo [MISSING] index.php - MAIN FILE MISSING!
)

if exist "%JOOMLA_PATH%\configuration.php-dist" (
    echo [OK] configuration.php-dist found
) else (
    echo [MISSING] configuration.php-dist
)

if exist "%JO<PERSON>LA_PATH%\administrator" (
    echo [OK] administrator folder found
) else (
    echo [MISSING] administrator folder
)

if exist "%J<PERSON><PERSON><PERSON>_PATH%\components" (
    echo [OK] components folder found
) else (
    echo [MISSING] components folder
)

if exist "%JOOMLA_PATH%\modules" (
    echo [OK] modules folder found
) else (
    echo [MISSING] modules folder
)

if exist "%JOOMLA_PATH%\templates" (
    echo [OK] templates folder found
) else (
    echo [MISSING] templates folder
)

echo.
echo ========================================
echo    DIAGNOSIS
echo ========================================
echo.

if exist "%JOOMLA_PATH%\index.php" (
    echo [DIAGNOSIS] Joomla files are present
    echo [ACTION] Try refreshing: http://localhost/electronics-shop
    echo [EXPECTED] You should see Joomla installation wizard
) else (
    echo [DIAGNOSIS] Joomla files are MISSING
    echo [ACTION] You need to extract Joomla files properly
    echo.
    echo MANUAL EXTRACTION STEPS:
    echo 1. Download Joomla from: https://www.joomla.org/download.html
    echo 2. Extract ZIP file to: %JOOMLA_PATH%
    echo 3. Make sure index.php is directly in electronics-shop folder
    echo 4. NOT in a subfolder like electronics-shop\Joomla_4.x.x\
)

echo.
echo Current folder contents:
dir "%JOOMLA_PATH%" /b

echo.
echo Press any key to open the folder...
pause
start "" "%JOOMLA_PATH%"

echo.
echo Press any key to test the website...
pause
start "" "http://localhost/electronics-shop"
