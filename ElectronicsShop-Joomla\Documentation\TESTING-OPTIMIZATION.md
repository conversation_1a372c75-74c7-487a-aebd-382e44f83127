# Hướng Dẫn Kiểm Thử và Tối Ưu Hóa Website

## Kiểm Thử Chức Năng (Functional Testing)

### 1. Kiểm thử Frontend

#### Trang chủ
- [ ] Logo và tiêu đề hiển thị đúng
- [ ] Menu navigation hoạt động
- [ ] Banner/slider hiển thị
- [ ] Sản phẩm nổi bật hiển thị
- [ ] Footer links hoạt động
- [ ] Responsive design (mobile/tablet)

#### Danh mục sản phẩm
- [ ] Hiển thị danh sách sản phẩm
- [ ] Phân trang hoạt động
- [ ] Sắp xếp theo giá, tên, ngày
- [ ] Lọc theo tình trạng sản phẩm
- [ ] Lọc theo thương hiệu
- [ ] Lọc theo khoảng giá

#### Chi tiết sản phẩm
- [ ] Hình ảnh gallery hoạt động
- [ ] Thông tin sản phẩm đầy đủ
- [ ] Tình trạng máy hiển thị
- [ ] Thông số kỹ thuật
- [ ] Thông tin bảo hành
- [ ] <PERSON><PERSON> kiện đi kèm
- [ ] Đánh giá sản phẩm
- [ ] Sản phẩm liên quan

#### Giỏ hàng và Checkout
- [ ] Thêm sản phẩm vào giỏ hàng
- [ ] Cập nhật số lượng
- [ ] Xóa sản phẩm khỏi giỏ
- [ ] Tính toán tổng tiền đúng
- [ ] Áp dụng mã giảm giá
- [ ] Chọn phương thức vận chuyển
- [ ] Chọn phương thức thanh toán
- [ ] Điền thông tin giao hàng
- [ ] Xác nhận đơn hàng

#### Tài khoản người dùng
- [ ] Đăng ký tài khoản mới
- [ ] Đăng nhập/đăng xuất
- [ ] Quên mật khẩu
- [ ] Cập nhật thông tin cá nhân
- [ ] Xem lịch sử đơn hàng
- [ ] Danh sách yêu thích

### 2. Kiểm thử Backend (Admin)

#### VirtueMart Admin
- [ ] Quản lý danh mục sản phẩm
- [ ] Thêm/sửa/xóa sản phẩm
- [ ] Upload hình ảnh sản phẩm
- [ ] Quản lý đơn hàng
- [ ] Quản lý khách hàng
- [ ] Cấu hình thanh toán
- [ ] Cấu hình vận chuyển
- [ ] Báo cáo doanh thu

#### Joomla Admin
- [ ] Quản lý bài viết
- [ ] Quản lý menu
- [ ] Quản lý modules
- [ ] Quản lý users
- [ ] Cấu hình template
- [ ] Cấu hình SEO

### 3. Kiểm thử Email

#### Email tự động
- [ ] Email xác nhận đăng ký
- [ ] Email xác nhận đơn hàng
- [ ] Email thông báo trạng thái đơn hàng
- [ ] Email quên mật khẩu
- [ ] Email newsletter

## Kiểm Thử Hiệu Suất (Performance Testing)

### 1. Tốc độ tải trang

#### Tools kiểm tra
- Google PageSpeed Insights
- GTmetrix
- Pingdom
- WebPageTest

#### Mục tiêu
- [ ] Trang chủ: < 3 giây
- [ ] Danh mục sản phẩm: < 4 giây
- [ ] Chi tiết sản phẩm: < 3 giây
- [ ] Checkout: < 2 giây

#### Tối ưu hóa
```php
// Bật Joomla caching
// System > Global Configuration > System
$cache = 1;
$cache_handler = 'file';
$cachetime = 15; // minutes

// Bật Gzip compression
$gzip = 1;
```

### 2. Tối ưu hóa Database

#### Kiểm tra queries chậm
```sql
-- Bật slow query log
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- Kiểm tra indexes
SHOW INDEX FROM jos_virtuemart_products;
SHOW INDEX FROM jos_virtuemart_product_prices;
```

#### Tối ưu hóa tables
```sql
-- Optimize tables
OPTIMIZE TABLE jos_virtuemart_products;
OPTIMIZE TABLE jos_virtuemart_categories;
OPTIMIZE TABLE jos_virtuemart_orders;
```

### 3. Tối ưu hóa Images

#### Compression tools
- TinyPNG
- ImageOptim
- WebP Converter

#### Responsive images
```html
<picture>
  <source srcset="image.webp" type="image/webp">
  <source srcset="image.jpg" type="image/jpeg">
  <img src="image.jpg" alt="Product image" loading="lazy">
</picture>
```

## Kiểm Thử Bảo Mật (Security Testing)

### 1. Joomla Security

#### Cập nhật hệ thống
- [ ] Joomla core version mới nhất
- [ ] VirtueMart version mới nhất
- [ ] PHP version được hỗ trợ
- [ ] Extensions được cập nhật

#### Cấu hình bảo mật
```apache
# .htaccess security rules
RewriteEngine On

# Block access to configuration.php
<Files "configuration.php">
    Order Allow,Deny
    Deny from all
</Files>

# Block access to sensitive directories
RewriteRule ^administrator/components/com_virtuemart/logs/ - [F,L]
RewriteRule ^logs/ - [F,L]
RewriteRule ^tmp/ - [F,L]

# Prevent SQL injection
RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} base64_encode.*\(.*\) [NC,OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC]
RewriteRule ^(.*)$ - [F,L]
```

### 2. Database Security

#### User permissions
```sql
-- Tạo user riêng cho website
CREATE USER 'electronics_user'@'localhost' IDENTIFIED BY 'strong_password_123!';
GRANT SELECT, INSERT, UPDATE, DELETE ON electronics_shop.* TO 'electronics_user'@'localhost';
FLUSH PRIVILEGES;

-- Xóa user mặc định nếu không cần
DROP USER 'root'@'localhost';
```

### 3. SSL/HTTPS

#### Cấu hình SSL
```apache
# Force HTTPS
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# HSTS Header
Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
```

## Kiểm Thử SEO

### 1. On-page SEO

#### Meta tags
- [ ] Title tags unique cho mỗi trang
- [ ] Meta descriptions hấp dẫn
- [ ] Meta keywords relevant
- [ ] Open Graph tags
- [ ] Schema markup

#### URL Structure
- [ ] SEF URLs enabled
- [ ] Clean URLs (no index.php)
- [ ] Breadcrumbs
- [ ] Sitemap.xml

### 2. Technical SEO

#### Page speed
- [ ] Core Web Vitals đạt chuẩn
- [ ] Mobile-friendly
- [ ] HTTPS enabled
- [ ] Structured data

#### Content optimization
```html
<!-- Product schema markup -->
<script type="application/ld+json">
{
  "@context": "https://schema.org/",
  "@type": "Product",
  "name": "Dell Latitude E7450",
  "image": "https://example.com/dell-e7450.jpg",
  "description": "Laptop Dell Latitude E7450 cũ, Core i5 thế hệ 5",
  "brand": {
    "@type": "Brand",
    "name": "Dell"
  },
  "offers": {
    "@type": "Offer",
    "url": "https://example.com/dell-e7450",
    "priceCurrency": "VND",
    "price": "8500000",
    "availability": "https://schema.org/InStock",
    "condition": "https://schema.org/UsedCondition"
  }
}
</script>
```

## Kiểm Thử Tương Thích (Compatibility Testing)

### 1. Browser Testing

#### Desktop browsers
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

#### Mobile browsers
- [ ] Chrome Mobile
- [ ] Safari Mobile
- [ ] Samsung Internet
- [ ] Firefox Mobile

### 2. Device Testing

#### Screen resolutions
- [ ] 1920x1080 (Desktop)
- [ ] 1366x768 (Laptop)
- [ ] 768x1024 (Tablet)
- [ ] 375x667 (Mobile)

#### Operating systems
- [ ] Windows 10/11
- [ ] macOS
- [ ] iOS
- [ ] Android

## Monitoring và Maintenance

### 1. Monitoring Tools

#### Uptime monitoring
- UptimeRobot
- Pingdom
- StatusCake

#### Error monitoring
- Sentry
- Rollbar
- Bugsnag

### 2. Backup Strategy

#### Automated backups
```bash
#!/bin/bash
# Daily backup script
DATE=$(date +%Y%m%d)
mysqldump -u electronics_user -p electronics_shop > backup_$DATE.sql
tar -czf website_backup_$DATE.tar.gz /path/to/website/
```

#### Backup checklist
- [ ] Database backup daily
- [ ] Files backup weekly
- [ ] Test restore procedure monthly
- [ ] Offsite backup storage

### 3. Performance Monitoring

#### Key metrics
- Page load time
- Database query time
- Server response time
- Error rate
- Conversion rate

#### Tools
- Google Analytics
- Google Search Console
- New Relic
- Pingdom RUM

## Checklist Tổng Thể

### Pre-launch
- [ ] Tất cả chức năng hoạt động
- [ ] Performance đạt yêu cầu
- [ ] Security được cấu hình
- [ ] SEO được tối ưu
- [ ] Backup system hoạt động
- [ ] Monitoring được thiết lập

### Post-launch
- [ ] Monitor uptime 24/7
- [ ] Kiểm tra logs hàng ngày
- [ ] Update security patches
- [ ] Optimize performance định kỳ
- [ ] Backup và test restore
- [ ] SEO monitoring và improvement

## Kết luận
Việc kiểm thử và tối ưu hóa là quá trình liên tục. Cần thực hiện định kỳ để đảm bảo website hoạt động ổn định và hiệu quả.
