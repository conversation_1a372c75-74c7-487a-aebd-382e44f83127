@echo off
R<PERSON> Batch script to run Python GUI Demo
REM Ứng Dụng Quản Lý <PERSON> - <PERSON>h Sách Liên Kết Đơn

title Quan Ly Hoc Sinh - Python GUI Demo

echo.
echo ========================================
echo    QUAN LY HOC SINH - PYTHON GUI
echo ========================================
echo.
echo Sinh vien thuc hien: Huynh Thai Bao
echo Lop: DX23TT11 - MSSV: 170123488
echo.
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python khong duoc cai dat hoac khong co trong PATH!
    echo.
    echo Vui long:
    echo 1. Cai dat Python tu https://python.org
    echo 2. Them Python vao PATH
    echo.
    pause
    exit /b 1
)

REM Display Python version
echo [INFO] Kiem tra Python...
python --version

REM Check if tkinter is available
echo [INFO] Kiem tra Tkinter...
python -c "import tkinter; print('Tkinter: OK')" 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Tkinter khong kha dung!
    echo.
    echo Tren Windows: Tkinter thuong co san voi Python
    echo Tren Linux: sudo apt-get install python3-tk
    echo.
    pause
    exit /b 1
)

echo [INFO] Tat ca dependencies da san sang!
echo.

REM Check if the Python file exists
if not exist "QuanLyHocSinh_GUI.py" (
    echo [ERROR] Khong tim thay file QuanLyHocSinh_GUI.py!
    echo Vui long dam bao file nam trong cung thu muc voi script nay.
    echo.
    pause
    exit /b 1
)

echo [INFO] Dang khoi dong ung dung...
echo.

REM Run the Python GUI application
python QuanLyHocSinh_GUI.py

REM Check if the application ran successfully
if %errorlevel% neq 0 (
    echo.
    echo [ERROR] Co loi xay ra khi chay ung dung!
    echo Error code: %errorlevel%
    echo.
    echo Cac nguyen nhan co the:
    echo - Loi syntax trong code Python
    echo - Thieu dependencies
    echo - Loi he thong
    echo.
    pause
    exit /b %errorlevel%
)

echo.
echo [INFO] Ung dung da dong thanh cong!
echo Cam on ban da su dung!
echo.
pause
