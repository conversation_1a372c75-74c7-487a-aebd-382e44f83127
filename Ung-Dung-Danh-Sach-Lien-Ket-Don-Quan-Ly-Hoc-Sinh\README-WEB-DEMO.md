# 🌐 Web Demo - Ứng Dụng Quản Lý Học Sinh

## 📋 Mô tả

Đây là phiên bản web demo của ứng dụng "Quản Lý Học Sinh sử dụng Danh Sách Liên Kết Đơn". Ứng dụng được xây dựng bằng **HTML5**, **CSS3** và **JavaScript** thuần túy, minh họa trực quan cách hoạt động của cấu trúc dữ liệu Danh Sách Liên Kết Đơn.

## 👨‍🎓 Thông tin sinh viên thực hiện

- **Họ tên**: Hu<PERSON>nh Thái Bảo
- **Ngày sinh**: 28/04/1994
- **Email**: <EMAIL>
- **Điện thoại**: 0355771075
- **Tài khoản**: baoht280494
- **Lớp**: DX23TT11
- **<PERSON><PERSON> sinh viên**: 170123488

## 🚀 T<PERSON>h năng chính

### ✨ Giao diện trực quan
- 🎨 Thiết kế responsive, thân thiện với người dùng
- 🌈 Gradient màu sắc hiện đại
- 📱 Tương thích với mọi thiết bị (desktop, tablet, mobile)
- 🎭 Animations và transitions mượt mà

### 🔧 Quản lý học sinh
- ➕ **Thêm học sinh**: Form nhập liệu với validation đầy đủ
- ✏️ **Chỉnh sửa**: Cập nhật thông tin học sinh trực tiếp
- 🗑️ **Xóa học sinh**: Xóa từng học sinh hoặc xóa tất cả
- 👁️ **Hiển thị**: Bảng dữ liệu đẹp mắt với phân trang

### 🔍 Tìm kiếm và lọc dữ liệu
- 🔎 **Tìm kiếm**: Theo mã số học sinh với highlight kết quả
- 🎯 **Lọc theo khối**: Hiển thị học sinh theo khối 10, 11, 12
- 📊 **Lọc theo điểm**: Lọc học sinh có điểm >= hoặc < ngưỡng
- 🔄 **Reset filter**: Khôi phục hiển thị toàn bộ danh sách

### 📈 Thống kê và báo cáo
- 📊 **Thống kê tổng quan**: Tổng số học sinh, điểm TB chung
- 🏆 **Học sinh xuất sắc**: Hiển thị học sinh có điểm cao nhất
- ⚠️ **Học sinh cần hỗ trợ**: Hiển thị học sinh có điểm thấp nhất
- 📈 **Phân tích kết quả**: Tỉ lệ đạt/không đạt, phân bố theo khối

### 🔄 Sắp xếp dữ liệu
- 🔤 **Sắp xếp theo mã số**: Thứ tự alphabet
- 📊 **Sắp xếp theo điểm**: Từ cao đến thấp

### 🎯 Minh họa cấu trúc dữ liệu
- 🔗 **Visualization**: Hiển thị trực quan cấu trúc Linked List
- ➡️ **Mũi tên liên kết**: Thể hiện con trỏ next giữa các node
- 🎨 **Animation**: Hiệu ứng khi thêm/xóa node
- 🔍 **Highlight**: Làm nổi bật node khi tìm kiếm

## 📁 Cấu trúc files

```
📂 Ung-Dung-Danh-Sach-Lien-Ket-Don-Quan-Ly-Hoc-Sinh/
├── 🌐 index.html          # Giao diện chính
├── 🎨 style.css           # Stylesheet với thiết kế hiện đại
├── ⚡ script.js           # Logic xử lý và cấu trúc dữ liệu
├── 📖 README-WEB-DEMO.md  # Hướng dẫn sử dụng web demo
├── 📄 BaoCaoDoAn.md       # Báo cáo đồ án hoàn chỉnh
├── 💻 QuanLyHocSinh.cpp   # Phiên bản C++
├── 📖 README.md           # Hướng dẫn tổng quan
└── ⚙️ Makefile            # Build script cho C++
```

## 🚀 Hướng dẫn chạy ứng dụng

### Phương pháp 1: Mở trực tiếp
1. Tải về hoặc clone repository
2. Mở file `index.html` bằng trình duyệt web
3. Ứng dụng sẽ chạy ngay lập tức

### Phương pháp 2: Sử dụng Live Server (khuyến nghị)
1. Cài đặt extension "Live Server" trong VS Code
2. Mở thư mục dự án trong VS Code
3. Click chuột phải vào `index.html` → "Open with Live Server"
4. Ứng dụng sẽ mở tại `http://localhost:5500`

### Phương pháp 3: Sử dụng Python HTTP Server
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000

# Truy cập: http://localhost:8000
```

## 🎮 Hướng dẫn sử dụng

### 1. Thêm học sinh mới
- Điền đầy đủ thông tin vào form
- Click "Thêm học sinh" hoặc nhấn Enter
- Học sinh sẽ xuất hiện trong bảng và visualization

### 2. Tìm kiếm học sinh
- Nhập mã số vào ô "Tìm theo mã số"
- Click nút tìm kiếm hoặc nhấn Enter
- Kết quả sẽ được highlight trong visualization

### 3. Lọc dữ liệu
- **Theo khối**: Chọn khối từ dropdown
- **Theo điểm**: Nhập điểm ngưỡng và chọn toán tử (>= hoặc <)

### 4. Sắp xếp
- Click "Sắp xếp theo mã số" hoặc "Sắp xếp theo điểm"
- Dữ liệu sẽ được sắp xếp ngay lập tức

### 5. Xem thống kê
- Click nút "Thống kê" để mở modal
- Xem các thông tin chi tiết về dữ liệu

### 6. Chỉnh sửa/Xóa
- Click nút "Edit" để chỉnh sửa học sinh
- Click nút "Delete" để xóa học sinh (có xác nhận)

## ⌨️ Phím tắt

- **Ctrl + S**: Focus vào form thêm học sinh
- **Ctrl + F**: Focus vào ô tìm kiếm
- **Escape**: Đóng modal thống kê

## 🎨 Đặc điểm thiết kế

### 🌈 Màu sắc
- **Primary**: Gradient xanh tím (#667eea → #764ba2)
- **Success**: Xanh lá (#28a745)
- **Warning**: Vàng cam (#ffc107)
- **Danger**: Đỏ (#dc3545)
- **Info**: Xanh dương (#17a2b8)

### 📱 Responsive Design
- **Desktop**: Layout 3 cột cho control panel
- **Tablet**: Layout 2 cột
- **Mobile**: Layout 1 cột, stack vertical

### ✨ Animations
- **Fade in**: Khi thêm dữ liệu mới
- **Slide in**: Cho notifications
- **Scale**: Khi hover buttons
- **Highlight**: Khi tìm kiếm thành công

## 🔧 Cấu trúc kỹ thuật

### 📊 Cấu trúc dữ liệu
```javascript
class Node {
    constructor(student) {
        this.data = student;    // Dữ liệu học sinh
        this.next = null;       // Con trỏ đến node tiếp theo
    }
}

class LinkedList {
    constructor() {
        this.head = null;       // Node đầu tiên
        this.size = 0;          // Kích thước danh sách
    }
}
```

### 🎯 Các thuật toán chính
- **Thêm**: O(n) - thêm vào cuối danh sách
- **Xóa**: O(n) - tìm và xóa node
- **Tìm kiếm**: O(n) - duyệt tuần tự
- **Sắp xếp**: O(n²) - bubble sort trên array

## 🌟 Tính năng nâng cao

### 📋 Dữ liệu mẫu
Ứng dụng có sẵn 5 học sinh mẫu để demo:
- HS001: Nguyễn Văn An (Khối 12, Điểm 8.5)
- HS002: Trần Thị Bình (Khối 11, Điểm 7.2)
- HS003: Lê Văn Cường (Khối 10, Điểm 6.8)
- HS004: Phạm Thị Dung (Khối 12, Điểm 9.1)
- HS005: Hoàng Văn Em (Khối 11, Điểm 5.5)

### 🔔 Hệ thống thông báo
- Thông báo thành công (xanh)
- Thông báo lỗi (đỏ)
- Thông báo cảnh báo (vàng)
- Thông báo thông tin (xanh dương)
- Tự động ẩn sau 5 giây

### ✅ Validation dữ liệu
- Mã số: Tối thiểu 3 ký tự, không trùng
- Họ tên: Tối thiểu 2 ký tự
- Năm sinh: 1990-2010
- Khối: 10, 11, hoặc 12
- Lớp: Không được trống
- Điểm TB: 0.0-10.0

## 🔮 Hướng phát triển

### 📈 Tính năng mới
- 💾 Lưu dữ liệu vào LocalStorage
- 📤 Export/Import CSV, JSON
- 🔍 Tìm kiếm nâng cao (theo tên, lớp)
- 📊 Biểu đồ thống kê (Chart.js)
- 🖨️ In báo cáo

### 🎨 Cải thiện giao diện
- 🌙 Dark mode
- 🎨 Theme customization
- 📱 PWA (Progressive Web App)
- 🔔 Push notifications

### ⚡ Tối ưu hiệu suất
- 🔄 Virtual scrolling cho danh sách lớn
- 🗂️ Pagination
- 🔍 Debounced search
- 💾 Caching

## 🐛 Troubleshooting

### ❌ Lỗi thường gặp
1. **Không hiển thị được**: Kiểm tra console browser (F12)
2. **Dữ liệu mất**: Refresh trang sẽ reset về dữ liệu mẫu
3. **Responsive không hoạt động**: Kiểm tra viewport meta tag

### 🔧 Debug
- Mở Developer Tools (F12)
- Kiểm tra Console tab để xem lỗi JavaScript
- Kiểm tra Network tab để xem tài nguyên load

## 📞 Liên hệ

**Sinh viên thực hiện**: Huỳnh Thái Bảo
- 📧 Email: <EMAIL>
- 📱 Phone: 0355771075
- 🎓 Lớp: DX23TT11
- 🆔 MSSV: 170123488

---

🎉 **Cảm ơn bạn đã sử dụng ứng dụng demo!** 

⭐ Nếu thấy hữu ích, hãy cho một star để ủng hộ!
