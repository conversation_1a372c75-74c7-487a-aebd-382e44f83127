# Makefile cho chương trình Quản Lý <PERSON>ọ<PERSON>
# Sử dụng cấu trúc dữ liệu Danh Sách Liên Kết Đơn
# Sinh viên: <PERSON><PERSON>nh Thái Bảo - DX23TT11 - 170123488

# Compiler và flags
CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra -O2
TARGET = QuanLyHocSinh
TARGET_ENHANCED = QuanLyHocSinh_Enhanced
SOURCE = QuanLyHocSinh.cpp
SOURCE_ENHANCED = QuanLyHocSinh_Enhanced.cpp

# Phát hiện hệ điều hành
ifeq ($(OS),Windows_NT)
    TARGET_EXT = .exe
    RM = del /Q
    MKDIR = mkdir
    CLEAR = cls
else
    TARGET_EXT = 
    RM = rm -f
    MKDIR = mkdir -p
    CLEAR = clear
endif

# Colors for output
GREEN = \033[32m
BLUE = \033[34m
YELLOW = \033[33m
RED = \033[31m
RESET = \033[0m

# Target chính - build phiên bản enhanced
all: $(TARGET_ENHANCED)$(TARGET_EXT)
	@echo "$(GREEN)✅ Build hoàn tất!$(RESET)"
	@echo "$(BLUE)🚀 Chạy: ./$(TARGET_ENHANCED)$(TARGET_EXT)$(RESET)"

# Biên dịch phiên bản enhanced
$(TARGET_ENHANCED)$(TARGET_EXT): $(SOURCE_ENHANCED)
	@echo "$(BLUE)🔨 Đang biên dịch phiên bản Enhanced...$(RESET)"
	$(CXX) $(CXXFLAGS) -o $(TARGET_ENHANCED)$(TARGET_EXT) $(SOURCE_ENHANCED)
	@echo "$(GREEN)✅ Biên dịch Enhanced thành công!$(RESET)"

# Biên dịch phiên bản cơ bản
basic: $(TARGET)$(TARGET_EXT)

$(TARGET)$(TARGET_EXT): $(SOURCE)
	@echo "$(BLUE)🔨 Đang biên dịch phiên bản cơ bản...$(RESET)"
	$(CXX) $(CXXFLAGS) -o $(TARGET)$(TARGET_EXT) $(SOURCE)
	@echo "$(GREEN)✅ Biên dịch cơ bản thành công!$(RESET)"

# Chạy phiên bản enhanced
run: $(TARGET_ENHANCED)$(TARGET_EXT)
	@echo "$(YELLOW)🚀 Khởi động ứng dụng Enhanced...$(RESET)"
	./$(TARGET_ENHANCED)$(TARGET_EXT)

# Chạy phiên bản cơ bản
run-basic: $(TARGET)$(TARGET_EXT)
	@echo "$(YELLOW)🚀 Khởi động ứng dụng cơ bản...$(RESET)"
	./$(TARGET)$(TARGET_EXT)

# Debug build
debug: CXXFLAGS += -g -DDEBUG -fsanitize=address
debug: $(TARGET_ENHANCED)$(TARGET_EXT)
	@echo "$(YELLOW)🐛 Debug build hoàn tất!$(RESET)"

# Release build (tối ưu hóa)
release: CXXFLAGS += -O3 -DNDEBUG -march=native
release: $(TARGET_ENHANCED)$(TARGET_EXT)
	@echo "$(GREEN)🚀 Release build hoàn tất!$(RESET)"

# Build cả hai phiên bản
both: $(TARGET)$(TARGET_EXT) $(TARGET_ENHANCED)$(TARGET_EXT)
	@echo "$(GREEN)✅ Đã build cả hai phiên bản!$(RESET)"

# Dọn dẹp files được tạo
clean:
	$(RM) $(TARGET)$(TARGET_EXT) $(TARGET_ENHANCED)$(TARGET_EXT)
	@echo "$(RED)🗑️ Đã dọn dẹp files!$(RESET)"

# Dọn dẹp và build lại
rebuild: clean all
	@echo "$(GREEN)🔄 Rebuild hoàn tất!$(RESET)"

# Cài đặt (copy vào thư mục bin)
install: $(TARGET_ENHANCED)$(TARGET_EXT)
	$(MKDIR) bin
	cp $(TARGET_ENHANCED)$(TARGET_EXT) bin/
	@echo "$(GREEN)📦 Đã cài đặt vào thư mục bin/$(RESET)"

# Test chương trình với valgrind (Linux only)
test:
ifeq ($(OS),Windows_NT)
	@echo "$(YELLOW)⚠️ Test với valgrind chỉ hỗ trợ trên Linux$(RESET)"
else
	@echo "$(BLUE)🧪 Chạy test với valgrind...$(RESET)"
	valgrind --leak-check=full --show-leak-kinds=all ./$(TARGET_ENHANCED)$(TARGET_EXT)
endif

# Hiển thị thông tin
info:
	@echo "$(BLUE)======================================$(RESET)"
	@echo "$(YELLOW)📋 THÔNG TIN DỰ ÁN$(RESET)"
	@echo "$(BLUE)======================================$(RESET)"
	@echo "Tên: $(GREEN)Quản Lý Học Sinh$(RESET)"
	@echo "Cấu trúc dữ liệu: $(GREEN)Danh Sách Liên Kết Đơn$(RESET)"
	@echo "Ngôn ngữ: $(GREEN)C++$(RESET)"
	@echo "Compiler: $(GREEN)$(CXX)$(RESET)"
	@echo "Flags: $(GREEN)$(CXXFLAGS)$(RESET)"
	@echo "Target Enhanced: $(GREEN)$(TARGET_ENHANCED)$(TARGET_EXT)$(RESET)"
	@echo "Target Basic: $(GREEN)$(TARGET)$(TARGET_EXT)$(RESET)"
	@echo "$(BLUE)======================================$(RESET)"
	@echo "👨‍🎓 Sinh viên: $(YELLOW)Huỳnh Thái Bảo$(RESET)"
	@echo "🎓 Lớp: $(YELLOW)DX23TT11$(RESET)"
	@echo "🆔 MSSV: $(YELLOW)170123488$(RESET)"
	@echo "$(BLUE)======================================$(RESET)"

# Hiển thị hướng dẫn
help:
	@echo "$(BLUE)======================================$(RESET)"
	@echo "$(YELLOW)📖 HƯỚNG DẪN SỬ DỤNG MAKEFILE$(RESET)"
	@echo "$(BLUE)======================================$(RESET)"
	@echo "$(GREEN)make$(RESET)          - Biên dịch phiên bản Enhanced"
	@echo "$(GREEN)make basic$(RESET)    - Biên dịch phiên bản cơ bản"
	@echo "$(GREEN)make both$(RESET)     - Biên dịch cả hai phiên bản"
	@echo "$(GREEN)make run$(RESET)      - Chạy phiên bản Enhanced"
	@echo "$(GREEN)make run-basic$(RESET) - Chạy phiên bản cơ bản"
	@echo "$(GREEN)make debug$(RESET)    - Build với debug info"
	@echo "$(GREEN)make release$(RESET)  - Build với tối ưu hóa"
	@echo "$(GREEN)make test$(RESET)     - Test với valgrind (Linux)"
	@echo "$(GREEN)make clean$(RESET)    - Xóa files đã biên dịch"
	@echo "$(GREEN)make rebuild$(RESET)  - Dọn dẹp và build lại"
	@echo "$(GREEN)make install$(RESET)  - Cài đặt vào thư mục bin"
	@echo "$(GREEN)make info$(RESET)     - Hiển thị thông tin dự án"
	@echo "$(GREEN)make help$(RESET)     - Hiển thị hướng dẫn này"
	@echo "$(BLUE)======================================$(RESET)"

# Demo - chạy với dữ liệu mẫu
demo: $(TARGET_ENHANCED)$(TARGET_EXT)
	@echo "$(YELLOW)🎬 Khởi động demo với dữ liệu mẫu...$(RESET)"
	@echo "$(BLUE)📚 Chương trình đã có sẵn 5 học sinh mẫu!$(RESET)"
	./$(TARGET_ENHANCED)$(TARGET_EXT)

# Phony targets
.PHONY: all basic run run-basic debug release both clean rebuild install test info help demo
