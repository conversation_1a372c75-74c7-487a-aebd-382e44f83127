@echo off
title Create Joomla Folder
color 0B

echo ========================================
echo    CREATE JOOMLA FOLDER
echo ========================================
echo.

echo Creating electronics-shop folder...
if not exist "C:\xampp\htdocs" (
    echo [ERROR] XAMPP htdocs folder not found!
    echo Please make sure XAMPP is installed at C:\xampp\
    pause
    exit
)

echo Creating directory: C:\xampp\htdocs\electronics-shop
mkdir "C:\xampp\htdocs\electronics-shop" 2>nul

if exist "C:\xampp\htdocs\electronics-shop" (
    echo [OK] Folder created successfully!
    echo Location: C:\xampp\htdocs\electronics-shop
) else (
    echo [ERROR] Could not create folder!
    echo Please create manually: C:\xampp\htdocs\electronics-shop
)

echo.
echo ========================================
echo    MANUAL JOOMLA INSTALLATION
echo ========================================
echo.
echo Now please follow these steps:
echo.
echo 1. Download Joomla from the opened browser tab
echo    - Click "Download Joomla 4.x"
echo    - Choose "Full Package" (ZIP file)
echo    - Save to Downloads folder
echo.
echo 2. Extract Joomla files:
echo    - Go to Downloads folder
echo    - Find Joomla_4.x.x-Stable-Full_Package.zip
echo    - Right-click and "Extract All"
echo    - Extract to: C:\xampp\htdocs\electronics-shop
echo.
echo 3. Verify extraction:
echo    - Open: C:\xampp\htdocs\electronics-shop
echo    - Should see files: index.php, configuration.php-dist, etc.
echo.
echo Press any key when Joomla files are extracted...
pause

echo.
echo Testing Joomla installation...
if exist "C:\xampp\htdocs\electronics-shop\index.php" (
    echo [OK] Joomla files found!
    echo Opening Joomla setup...
    start "" "http://localhost/electronics-shop"
) else (
    echo [ERROR] Joomla files not found!
    echo Please make sure files are extracted to:
    echo C:\xampp\htdocs\electronics-shop\
)

echo.
echo Press any key to continue...
pause
