"""
Test cases cho các gi<PERSON>i thuật tìm đường đi ngắn nhất
Author: Student
Date: 2025
"""

import unittest
import sys
import os

# Thêm thư mục src vào path để import các module
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from dijkstra import Graph as DijkstraGraph
from bellman_ford import Graph as BellmanFordGraph
from floyd_warshall import Graph as FloydWarshallGraph

class TestDijkstra(unittest.TestCase):
    """Test cases cho giải thuật Dijkstra"""
    
    def setUp(self):
        """Thiết lập dữ liệu test"""
        self.graph = DijkstraGraph(6)
        self.graph.add_edge(0, 1, 4)
        self.graph.add_edge(0, 2, 2)
        self.graph.add_edge(1, 2, 1)
        self.graph.add_edge(1, 3, 5)
        self.graph.add_edge(2, 3, 8)
        self.graph.add_edge(2, 4, 10)
        self.graph.add_edge(3, 4, 2)
        self.graph.add_edge(3, 5, 6)
        self.graph.add_edge(4, 5, 3)
    
    def test_dijkstra_basic(self):
        """Test cơ bản cho Dijkstra"""
        distances, parents = self.graph.dijkstra(0)
        
        # Kiểm tra khoảng cách từ đỉnh 0
        expected_distances = [0, 3, 2, 8, 10, 13]
        self.assertEqual(distances, expected_distances)
    
    def test_dijkstra_path(self):
        """Test đường đi trong Dijkstra"""
        distances, parents = self.graph.dijkstra(0)
        
        # Kiểm tra đường đi từ 0 đến 5
        path = self.graph.get_path(parents, 5)
        expected_path = [0, 2, 1, 3, 4, 5]
        self.assertEqual(path, expected_path)
    
    def test_dijkstra_single_vertex(self):
        """Test với đồ thị chỉ có 1 đỉnh"""
        single_graph = DijkstraGraph(1)
        distances, parents = single_graph.dijkstra(0)
        
        self.assertEqual(distances, [0])
        self.assertEqual(parents, [-1])

class TestBellmanFord(unittest.TestCase):
    """Test cases cho giải thuật Bellman-Ford"""
    
    def setUp(self):
        """Thiết lập dữ liệu test"""
        self.graph = BellmanFordGraph(5)
        self.graph.add_edge(0, 1, -1)
        self.graph.add_edge(0, 2, 4)
        self.graph.add_edge(1, 2, 3)
        self.graph.add_edge(1, 3, 2)
        self.graph.add_edge(1, 4, 2)
        self.graph.add_edge(3, 2, 5)
        self.graph.add_edge(3, 1, 1)
        self.graph.add_edge(4, 3, -3)
    
    def test_bellman_ford_basic(self):
        """Test cơ bản cho Bellman-Ford"""
        distances, parents, has_negative_cycle = self.graph.bellman_ford(0)
        
        # Kiểm tra không có chu trình âm
        self.assertFalse(has_negative_cycle)
        
        # Kiểm tra khoảng cách từ đỉnh 0
        expected_distances = [0, -1, 2, -2, 1]
        self.assertEqual(distances, expected_distances)
    
    def test_bellman_ford_negative_cycle(self):
        """Test phát hiện chu trình âm"""
        neg_cycle_graph = BellmanFordGraph(3)
        neg_cycle_graph.add_edge(0, 1, 1)
        neg_cycle_graph.add_edge(1, 2, -3)
        neg_cycle_graph.add_edge(2, 0, 1)
        
        distances, parents, has_negative_cycle = neg_cycle_graph.bellman_ford(0)
        
        # Phải phát hiện chu trình âm
        self.assertTrue(has_negative_cycle)
    
    def test_bellman_ford_path(self):
        """Test đường đi trong Bellman-Ford"""
        distances, parents, has_negative_cycle = self.graph.bellman_ford(0)
        
        # Kiểm tra đường đi từ 0 đến 3
        path = self.graph.get_path(parents, 3)
        expected_path = [0, 1, 4, 3]
        self.assertEqual(path, expected_path)

class TestFloydWarshall(unittest.TestCase):
    """Test cases cho giải thuật Floyd-Warshall"""
    
    def setUp(self):
        """Thiết lập dữ liệu test"""
        self.graph = FloydWarshallGraph(4)
        self.graph.add_edge(0, 1, 5)
        self.graph.add_edge(0, 3, 10)
        self.graph.add_edge(1, 2, 3)
        self.graph.add_edge(2, 3, 1)
    
    def test_floyd_warshall_basic(self):
        """Test cơ bản cho Floyd-Warshall"""
        result = self.graph.floyd_warshall()
        
        # Kiểm tra một số khoảng cách cụ thể
        self.assertEqual(result[0][0], 0)  # Từ 0 đến 0
        self.assertEqual(result[0][1], 5)  # Từ 0 đến 1
        self.assertEqual(result[0][2], 8)  # Từ 0 đến 2 (qua 1)
        self.assertEqual(result[0][3], 9)  # Từ 0 đến 3 (qua 1, 2)
    
    def test_floyd_warshall_no_negative_cycle(self):
        """Test không có chu trình âm"""
        result = self.graph.floyd_warshall()
        has_negative_cycle = self.graph.has_negative_cycle(result)
        
        self.assertFalse(has_negative_cycle)
    
    def test_floyd_warshall_with_negative_weights(self):
        """Test với trọng số âm"""
        neg_graph = FloydWarshallGraph(3)
        neg_graph.add_edge(0, 1, 1)
        neg_graph.add_edge(1, 2, -2)
        neg_graph.add_edge(0, 2, 3)
        
        result = neg_graph.floyd_warshall()
        
        # Đường đi từ 0 đến 2 qua 1 ngắn hơn đường đi trực tiếp
        self.assertEqual(result[0][2], -1)  # 0->1->2: 1+(-2) = -1 < 3

class TestAlgorithmComparison(unittest.TestCase):
    """Test so sánh kết quả giữa các giải thuật"""
    
    def test_dijkstra_vs_bellman_ford(self):
        """So sánh kết quả Dijkstra và Bellman-Ford với đồ thị không có trọng số âm"""
        # Tạo đồ thị cho Dijkstra
        dijkstra_graph = DijkstraGraph(4)
        dijkstra_graph.add_edge(0, 1, 1)
        dijkstra_graph.add_edge(0, 2, 4)
        dijkstra_graph.add_edge(1, 2, 2)
        dijkstra_graph.add_edge(1, 3, 5)
        dijkstra_graph.add_edge(2, 3, 1)
        
        # Tạo đồ thị tương tự cho Bellman-Ford
        bellman_graph = BellmanFordGraph(4)
        bellman_graph.add_edge(0, 1, 1)
        bellman_graph.add_edge(0, 2, 4)
        bellman_graph.add_edge(1, 2, 2)
        bellman_graph.add_edge(1, 3, 5)
        bellman_graph.add_edge(2, 3, 1)
        
        # Chạy cả hai giải thuật
        dijkstra_dist, _ = dijkstra_graph.dijkstra(0)
        bellman_dist, _, has_neg_cycle = bellman_graph.bellman_ford(0)
        
        # Kết quả phải giống nhau
        self.assertFalse(has_neg_cycle)
        self.assertEqual(dijkstra_dist, bellman_dist)
    
    def test_performance_comparison(self):
        """Test so sánh hiệu suất (đơn giản)"""
        import time
        
        # Tạo đồ thị nhỏ để test
        size = 10
        
        # Dijkstra
        dijkstra_graph = DijkstraGraph(size)
        for i in range(size - 1):
            dijkstra_graph.add_edge(i, i + 1, 1)
        
        start_time = time.time()
        dijkstra_graph.dijkstra(0)
        dijkstra_time = time.time() - start_time
        
        # Bellman-Ford
        bellman_graph = BellmanFordGraph(size)
        for i in range(size - 1):
            bellman_graph.add_edge(i, i + 1, 1)
        
        start_time = time.time()
        bellman_graph.bellman_ford(0)
        bellman_time = time.time() - start_time
        
        # Floyd-Warshall
        floyd_graph = FloydWarshallGraph(size)
        for i in range(size - 1):
            floyd_graph.add_edge(i, i + 1, 1)
        
        start_time = time.time()
        floyd_graph.floyd_warshall()
        floyd_time = time.time() - start_time
        
        # In kết quả so sánh (không assert vì thời gian có thể biến động)
        print(f"\nSo sánh thời gian thực thi (đồ thị {size} đỉnh):")
        print(f"Dijkstra: {dijkstra_time:.6f}s")
        print(f"Bellman-Ford: {bellman_time:.6f}s")
        print(f"Floyd-Warshall: {floyd_time:.6f}s")

def run_all_tests():
    """Chạy tất cả các test"""
    print("=" * 60)
    print("CHẠY TEST CHO CÁC GIẢI THUẬT TÌM ĐƯỜNG ĐI NGẮN NHẤT")
    print("=" * 60)
    
    # Tạo test suite
    test_suite = unittest.TestSuite()
    
    # Thêm các test classes
    test_classes = [TestDijkstra, TestBellmanFord, TestFloydWarshall, TestAlgorithmComparison]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Chạy tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # In kết quả tổng kết
    print("\n" + "=" * 60)
    print("KẾT QUẢ TỔNG KẾT")
    print("=" * 60)
    print(f"Tổng số test: {result.testsRun}")
    print(f"Thành công: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"Thất bại: {len(result.failures)}")
    print(f"Lỗi: {len(result.errors)}")
    
    if result.wasSuccessful():
        print("✅ TẤT CẢ TEST ĐỀU THÀNH CÔNG!")
    else:
        print("❌ CÓ TEST THẤT BẠI HOẶC LỖI!")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    run_all_tests()
