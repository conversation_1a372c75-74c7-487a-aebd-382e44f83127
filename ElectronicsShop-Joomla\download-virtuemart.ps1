# PowerShell script to download VirtueMart and plugins
Write-Host "========================================" -ForegroundColor Green
Write-Host "   VirtueMart Download Script" -ForegroundColor Green
Write-Host "   Electronics Shop Project" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Variables
$downloadPath = ".\Downloads\VirtueMart"
$virtueMartVersion = "4.2.4"

# VirtueMart download URLs
$downloads = @{
    "VirtueMart Core" = @{
        "url" = "https://virtuemart.net/downloads/vm4/com_virtuemart_$virtueMartVersion.zip"
        "filename" = "com_virtuemart_$virtueMartVersion.zip"
        "description" = "VirtueMart core component"
    }
    "Payment Plugins" = @{
        "url" = "https://virtuemart.net/downloads/vm4/plg_vmpayment_standard.zip"
        "filename" = "plg_vmpayment_standard.zip"
        "description" = "Standard payment methods (COD, Bank Transfer, etc.)"
    }
    "Shipment Plugins" = @{
        "url" = "https://virtuemart.net/downloads/vm4/plg_vmshipment_standard.zip"
        "filename" = "plg_vmshipment_standard.zip"
        "description" = "Standard shipment methods"
    }
    "Invoice Plugin" = @{
        "url" = "https://virtuemart.net/downloads/vm4/plg_vminvoice_standard.zip"
        "filename" = "plg_vminvoice_standard.zip"
        "description" = "Invoice generation plugin"
    }
    "Vietnamese Language" = @{
        "url" = "https://virtuemart.net/downloads/vm4/virtuemart_vi-VN.zip"
        "filename" = "virtuemart_vi-VN.zip"
        "description" = "Vietnamese language pack for VirtueMart"
    }
}

# Create download directory
if (-not (Test-Path $downloadPath)) {
    New-Item -ItemType Directory -Path $downloadPath -Force | Out-Null
    Write-Host "Created download directory: $downloadPath" -ForegroundColor Green
}

Write-Host "Starting VirtueMart downloads..." -ForegroundColor Yellow
Write-Host ""

$successCount = 0
$totalCount = $downloads.Count

foreach ($item in $downloads.GetEnumerator()) {
    $name = $item.Key
    $url = $item.Value.url
    $filename = $item.Value.filename
    $description = $item.Value.description
    $filePath = Join-Path $downloadPath $filename
    
    Write-Host "Downloading: $name" -ForegroundColor Cyan
    Write-Host "Description: $description" -ForegroundColor Gray
    Write-Host "URL: $url" -ForegroundColor Gray
    
    try {
        # Check if file already exists
        if (Test-Path $filePath) {
            Write-Host "File already exists: $filename" -ForegroundColor Yellow
            $overwrite = Read-Host "Do you want to overwrite? (y/n)"
            if ($overwrite -ne "y" -and $overwrite -ne "Y") {
                Write-Host "Skipped: $filename" -ForegroundColor Yellow
                $successCount++
                Write-Host ""
                continue
            }
        }
        
        # Download file
        Invoke-WebRequest -Uri $url -OutFile $filePath -UseBasicParsing
        
        if (Test-Path $filePath) {
            $fileSize = (Get-Item $filePath).Length
            $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
            Write-Host "Downloaded successfully: $filename ($fileSizeMB MB)" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "Download failed: $filename" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "Error downloading $filename : $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host ""
}

Write-Host "========================================" -ForegroundColor Green
Write-Host "   Download Summary" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "Successfully downloaded: $successCount/$totalCount files" -ForegroundColor Green
Write-Host "Download location: $downloadPath" -ForegroundColor Cyan
Write-Host ""

if ($successCount -eq $totalCount) {
    Write-Host "All files downloaded successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Open Joomla Administrator panel" -ForegroundColor White
    Write-Host "2. Go to System > Install > Extensions" -ForegroundColor White
    Write-Host "3. Upload and install files in this order:" -ForegroundColor White
    Write-Host "   a) com_virtuemart_$virtueMartVersion.zip (Core)" -ForegroundColor White
    Write-Host "   b) plg_vmpayment_standard.zip (Payment)" -ForegroundColor White
    Write-Host "   c) plg_vmshipment_standard.zip (Shipment)" -ForegroundColor White
    Write-Host "   d) plg_vminvoice_standard.zip (Invoice)" -ForegroundColor White
    Write-Host "   e) virtuemart_vi-VN.zip (Vietnamese Language)" -ForegroundColor White
    Write-Host ""
    Write-Host "4. Follow the VirtueMart setup guide in Documentation/VIRTUEMART-SETUP.md" -ForegroundColor White
} else {
    Write-Host "Some downloads failed. Please check your internet connection and try again." -ForegroundColor Red
}

Write-Host ""

# Create installation checklist
$checklistPath = Join-Path $downloadPath "installation-checklist.txt"
$checklist = @"
VirtueMart Installation Checklist for Electronics Shop
=====================================================

Downloaded Files:
[ ] com_virtuemart_$virtueMartVersion.zip - VirtueMart Core Component
[ ] plg_vmpayment_standard.zip - Payment Methods Plugin
[ ] plg_vmshipment_standard.zip - Shipment Methods Plugin  
[ ] plg_vminvoice_standard.zip - Invoice Plugin
[ ] virtuemart_vi-VN.zip - Vietnamese Language Pack

Installation Order:
[ ] 1. Install VirtueMart Core Component first
[ ] 2. Install Payment Methods Plugin
[ ] 3. Install Shipment Methods Plugin
[ ] 4. Install Invoice Plugin
[ ] 5. Install Vietnamese Language Pack

Post-Installation Setup:
[ ] Run VirtueMart Installation Tool
[ ] Configure Shop Information (Vietnamese)
[ ] Set Currency to VND (Vietnamese Dong)
[ ] Create Product Categories:
    [ ] Laptop cũ
    [ ] Điện thoại cũ  
    [ ] Máy tính bảng
    [ ] Phụ kiện
    [ ] Linh kiện
[ ] Configure Payment Methods:
    [ ] COD (Cash on Delivery)
    [ ] Bank Transfer
    [ ] E-Wallet (MoMo, ZaloPay)
[ ] Configure Shipping Methods:
    [ ] Standard Shipping (30,000 VND)
    [ ] Express Shipping (50,000 VND)
    [ ] Free Shipping (orders > 5,000,000 VND)
[ ] Import Sample Data
[ ] Test all functionality

For detailed instructions, see: Documentation/VIRTUEMART-SETUP.md

Generated on: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
"@

$checklist | Out-File -FilePath $checklistPath -Encoding UTF8
Write-Host "Installation checklist created: $checklistPath" -ForegroundColor Green

# Ask if user wants to open the download folder
$openFolder = Read-Host "Do you want to open the download folder now? (y/n)"
if ($openFolder -eq "y" -or $openFolder -eq "Y") {
    Start-Process $downloadPath
}

Write-Host ""
Write-Host "VirtueMart download script completed!" -ForegroundColor Green
Read-Host "Press Enter to exit"
