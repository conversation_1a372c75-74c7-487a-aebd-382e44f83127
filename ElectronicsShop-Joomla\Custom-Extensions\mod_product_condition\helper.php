<?php
/**
 * @package     Product Condition Module
 * @subpackage  mod_product_condition
 * @copyright   Copyright (C) 2025 Electronics Shop. All rights reserved.
 * @license     GNU General Public License version 2 or later
 */

defined('_JEXEC') or die;

use <PERSON><PERSON><PERSON>\CMS\Factory;
use Joom<PERSON>\CMS\Language\Text;

/**
 * Helper class for Product Condition Module
 */
class ModProductConditionHelper
{
    /**
     * Module parameters
     *
     * @var    \Joomla\Registry\Registry
     */
    protected $params;

    /**
     * Constructor
     *
     * @param   \Joomla\Registry\Registry  $params  Module parameters
     */
    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * Get product conditions from database
     *
     * @return  array  Array of condition objects
     */
    public function getProductConditions()
    {
        $db = Factory::getDbo();
        $query = $db->getQuery(true);

        $query->select([
            'pc.condition_id',
            'pc.condition_name',
            'pc.condition_name_en',
            'pc.condition_description',
            'pc.condition_percentage',
            'pc.condition_color',
            'pc.ordering'
        ])
        ->from($db->quoteName('#__vm_product_condition', 'pc'))
        ->where($db->quoteName('pc.published') . ' = 1')
        ->order($db->quoteName('pc.ordering') . ' ASC');

        $db->setQuery($query);

        try {
            $conditions = $db->loadObjectList();
            
            // Add product count for each condition
            foreach ($conditions as &$condition) {
                $condition->product_count = $this->getProductCountByCondition($condition->condition_id);
            }
            
            return $conditions;
        } catch (Exception $e) {
            // Fallback to default conditions if custom table doesn't exist
            return $this->getDefaultConditions();
        }
    }

    /**
     * Get product count by condition
     *
     * @param   int  $conditionId  Condition ID
     *
     * @return  int  Number of products
     */
    protected function getProductCountByCondition($conditionId)
    {
        $db = Factory::getDbo();
        $query = $db->getQuery(true);

        $query->select('COUNT(*)')
            ->from($db->quoteName('#__virtuemart_products', 'p'))
            ->where($db->quoteName('p.condition_id') . ' = ' . (int) $conditionId)
            ->where($db->quoteName('p.published') . ' = 1');

        $db->setQuery($query);

        try {
            return (int) $db->loadResult();
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Get default conditions (fallback)
     *
     * @return  array  Array of default condition objects
     */
    protected function getDefaultConditions()
    {
        return [
            (object) [
                'condition_id' => 1,
                'condition_name' => Text::_('MOD_PRODUCT_CONDITION_LIKE_NEW'),
                'condition_name_en' => 'Like New',
                'condition_description' => Text::_('MOD_PRODUCT_CONDITION_LIKE_NEW_DESC'),
                'condition_percentage' => 95,
                'condition_color' => '#28a745',
                'ordering' => 1,
                'product_count' => 0
            ],
            (object) [
                'condition_id' => 2,
                'condition_name' => Text::_('MOD_PRODUCT_CONDITION_GOOD'),
                'condition_name_en' => 'Good',
                'condition_description' => Text::_('MOD_PRODUCT_CONDITION_GOOD_DESC'),
                'condition_percentage' => 85,
                'condition_color' => '#17a2b8',
                'ordering' => 2,
                'product_count' => 0
            ],
            (object) [
                'condition_id' => 3,
                'condition_name' => Text::_('MOD_PRODUCT_CONDITION_FAIR'),
                'condition_name_en' => 'Fair',
                'condition_description' => Text::_('MOD_PRODUCT_CONDITION_FAIR_DESC'),
                'condition_percentage' => 75,
                'condition_color' => '#ffc107',
                'ordering' => 3,
                'product_count' => 0
            ],
            (object) [
                'condition_id' => 4,
                'condition_name' => Text::_('MOD_PRODUCT_CONDITION_NEEDS_REPAIR'),
                'condition_name_en' => 'Needs Repair',
                'condition_description' => Text::_('MOD_PRODUCT_CONDITION_NEEDS_REPAIR_DESC'),
                'condition_percentage' => 60,
                'condition_color' => '#dc3545',
                'ordering' => 4,
                'product_count' => 0
            ]
        ];
    }

    /**
     * Get condition by ID
     *
     * @param   int  $conditionId  Condition ID
     *
     * @return  object|null  Condition object or null if not found
     */
    public function getConditionById($conditionId)
    {
        $conditions = $this->getProductConditions();
        
        foreach ($conditions as $condition) {
            if ($condition->condition_id == $conditionId) {
                return $condition;
            }
        }
        
        return null;
    }

    /**
     * Get condition badge HTML
     *
     * @param   object  $condition  Condition object
     * @param   bool    $showPercentage  Show percentage
     *
     * @return  string  HTML for condition badge
     */
    public function getConditionBadge($condition, $showPercentage = true)
    {
        $html = '<span class="condition-badge" style="background-color: ' . $condition->condition_color . ';">';
        
        if ($this->params->get('show_color_indicator', 1)) {
            $html .= '<span class="condition-color" style="background-color: ' . $condition->condition_color . ';"></span>';
        }
        
        $html .= htmlspecialchars($condition->condition_name, ENT_QUOTES, 'UTF-8');
        
        if ($showPercentage && $this->params->get('show_percentage', 1)) {
            $html .= ' (' . $condition->condition_percentage . '%)';
        }
        
        $html .= '</span>';
        
        return $html;
    }

    /**
     * Get filter URL for condition
     *
     * @param   int  $conditionId  Condition ID (0 for all)
     *
     * @return  string  Filter URL
     */
    public function getFilterUrl($conditionId = 0)
    {
        $app = Factory::getApplication();
        $currentUrl = $app->input->server->getString('REQUEST_URI', '');
        
        // Parse current URL
        $urlParts = parse_url($currentUrl);
        $queryParams = [];
        
        if (isset($urlParts['query'])) {
            parse_str($urlParts['query'], $queryParams);
        }
        
        // Update condition parameter
        if ($conditionId > 0) {
            $queryParams['condition_id'] = $conditionId;
        } else {
            unset($queryParams['condition_id']);
        }
        
        // Rebuild URL
        $newUrl = $urlParts['path'];
        if (!empty($queryParams)) {
            $newUrl .= '?' . http_build_query($queryParams);
        }
        
        return $newUrl;
    }

    /**
     * Check if condition is currently active
     *
     * @param   int  $conditionId  Condition ID
     *
     * @return  bool  True if active
     */
    public function isConditionActive($conditionId)
    {
        $app = Factory::getApplication();
        $currentCondition = $app->input->getInt('condition_id', 0);
        
        return ($currentCondition == $conditionId);
    }

    /**
     * Get condition statistics
     *
     * @return  array  Statistics array
     */
    public function getConditionStatistics()
    {
        $conditions = $this->getProductConditions();
        $stats = [
            'total_products' => 0,
            'conditions' => []
        ];
        
        foreach ($conditions as $condition) {
            $stats['total_products'] += $condition->product_count;
            $stats['conditions'][$condition->condition_id] = [
                'name' => $condition->condition_name,
                'count' => $condition->product_count,
                'percentage' => $condition->condition_percentage,
                'color' => $condition->condition_color
            ];
        }
        
        return $stats;
    }
}
