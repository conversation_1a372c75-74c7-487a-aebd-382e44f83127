// Product Detail Page JavaScript

// Sample detailed product data
const detailedProducts = {
    1: {
        id: 1,
        name: "Dell Latitude E7450",
        category: "laptop",
        brand: "Dell",
        price: 12500000,
        originalPrice: 15000000,
        condition: 95,
        images: [
            "https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=600&h=400&fit=crop",
            "https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=600&h=400&fit=crop",
            "https://images.unsplash.com/photo-1593642632823-8f785ba67e45?w=600&h=400&fit=crop",
            "https://images.unsplash.com/photo-1484788984921-03950022c9ef?w=600&h=400&fit=crop"
        ],
        description: "Laptop Dell Latitude E7450 là dòng laptop doanh nghiệp cao cấp với thiết kế mỏng nhẹ, hiệu suất mạnh mẽ và độ bền cao. <PERSON><PERSON> hợp cho công việc văn phòng, lập trình và thiết kế đồ họa nhẹ.",
        specifications: {
            "Bộ xử lý": "Intel Core i5-5300U (2.3GHz, up to 2.9GHz)",
            "RAM": "8GB DDR3L 1600MHz (có thể nâng cấp lên 16GB)",
            "Ổ cứng": "SSD 256GB SATA III",
            "Card đồ họa": "Intel HD Graphics 5500",
            "Màn hình": "14 inch Full HD (1920x1080) IPS",
            "Cổng kết nối": "USB 3.0 x2, USB 2.0 x1, HDMI, VGA, LAN, Audio",
            "Kết nối không dây": "WiFi 802.11ac, Bluetooth 4.0",
            "Pin": "4-cell 51Wh (sử dụng 6-8 tiếng)",
            "Trọng lượng": "1.6kg",
            "Hệ điều hành": "Windows 11 Pro bản quyền"
        },
        conditionDetails: "Máy hoạt động hoàn hảo, vỏ có vài vết xước nhỏ ở góc máy do sử dụng. Bàn phím và touchpad hoạt động tốt. Pin còn giữ được 85% dung lượng ban đầu. Màn hình sáng đẹp, không có điểm chết.",
        warranty: 6,
        inStock: true,
        stockQuantity: 3,
        rating: 4.5,
        reviewCount: 23,
        reviews: [
            {
                id: 1,
                userName: "Nguyễn Văn A",
                avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
                rating: 5,
                date: "2025-01-15",
                title: "Laptop tuyệt vời cho công việc",
                content: "Mình đã sử dụng laptop này được 2 tháng rồi, rất hài lòng. Máy chạy mượt, pin trâu, thiết kế đẹp. Rất phù hợp cho công việc văn phòng và lập trình.",
                pros: "Pin trâu, hiệu suất tốt, thiết kế đẹp",
                cons: "Có vài vết xước nhỏ",
                helpful: 12,
                images: [
                    "https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=200&h=150&fit=crop"
                ]
            },
            {
                id: 2,
                userName: "Trần Thị B",
                avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
                rating: 4,
                date: "2025-01-10",
                title: "Chất lượng tốt, giá hợp lý",
                content: "Laptop chạy ổn định, phù hợp cho công việc văn phòng. Màn hình đẹp, bàn phím gõ êm. Chỉ có điều pin hơi yếu so với mong đợi.",
                pros: "Màn hình đẹp, bàn phím tốt, giá hợp lý",
                cons: "Pin hơi yếu",
                helpful: 8,
                images: []
            }
        ]
    },
    2: {
        id: 2,
        name: "iPhone 12 Pro Max",
        category: "dien-thoai",
        brand: "Apple",
        price: 22500000,
        originalPrice: 28000000,
        condition: 90,
        images: [
            "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=600&h=400&fit=crop",
            "https://images.unsplash.com/photo-1605236453806-b25e5d5cce04?w=600&h=400&fit=crop",
            "https://images.unsplash.com/photo-1611472173362-3f53dbd65d80?w=600&h=400&fit=crop"
        ],
        description: "iPhone 12 Pro Max với màn hình Super Retina XDR 6.7 inch, chip A14 Bionic mạnh mẽ và hệ thống camera Pro tiên tiến. Thiết kế cao cấp với khung viền thép không gỉ và mặt lưng kính.",
        specifications: {
            "Màn hình": "6.7 inch Super Retina XDR OLED (2778x1284)",
            "Chip": "A14 Bionic với Neural Engine 16-core",
            "Dung lượng": "128GB",
            "Camera sau": "Hệ thống camera Pro 12MP (Wide, Ultra Wide, Telephoto)",
            "Camera trước": "12MP TrueDepth với Face ID",
            "Video": "Quay video Dolby Vision HDR 4K",
            "Kết nối": "5G, WiFi 6, Bluetooth 5.0, NFC",
            "Chống nước": "IP68 (sâu 6m trong 30 phút)",
            "Pin": "Video: 20 giờ, Audio: 80 giờ",
            "Hệ điều hành": "iOS 18 (có thể cập nhật)"
        },
        conditionDetails: "Máy hoạt động hoàn hảo, vỏ có vài vết xước nhỏ ở khung viền. Màn hình nguyên zin, không có vết nứt hay điểm chết. Pin health 87%. Face ID hoạt động tốt. Fullbox với cáp Lightning.",
        warranty: 12,
        inStock: true,
        stockQuantity: 2,
        rating: 4.8,
        reviewCount: 45,
        reviews: [
            {
                id: 1,
                userName: "Lê Văn C",
                avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
                rating: 5,
                date: "2025-01-18",
                title: "iPhone tuyệt vời, chất lượng như mới",
                content: "Mình rất hài lòng với chiếc iPhone này. Máy chạy mượt mà, camera chụp đẹp, pin cũng ổn. Shop tư vấn nhiệt tình, giao hàng nhanh.",
                pros: "Hiệu suất cao, camera đẹp, thiết kế premium",
                cons: "Giá hơi cao",
                helpful: 25,
                images: [
                    "https://images.unsplash.com/photo-1605236453806-b25e5d5cce04?w=200&h=150&fit=crop"
                ]
            }
        ]
    }
};

// Current product data
let currentProduct = null;
let currentImageIndex = 0;
let selectedQuantity = 1;

// Initialize product page
document.addEventListener('DOMContentLoaded', function() {
    loadProductDetail();
    initializeProductPage();
});

function loadProductDetail() {
    const urlParams = new URLSearchParams(window.location.search);
    const productId = parseInt(urlParams.get('id'));
    
    if (productId && detailedProducts[productId]) {
        currentProduct = detailedProducts[productId];
        renderProductDetail();
        loadRelatedProducts();
        updateBreadcrumb();
    } else {
        showNotFound();
    }
}

function renderProductDetail() {
    const container = document.getElementById('productDetailContainer');
    const discountPercent = Math.round(((currentProduct.originalPrice - currentProduct.price) / currentProduct.originalPrice) * 100);
    
    container.innerHTML = `
        <div class="col-lg-6">
            <div class="product-images" data-aos="fade-right">
                <img src="${currentProduct.images[0]}" alt="${currentProduct.name}" class="main-image" id="mainImage" onclick="openImageModal(this.src)">
                <div class="thumbnail-images">
                    ${currentProduct.images.map((img, index) => `
                        <img src="${img}" alt="${currentProduct.name}" class="thumbnail-image ${index === 0 ? 'active' : ''}" onclick="changeMainImage(${index})">
                    `).join('')}
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="product-info" data-aos="fade-left">
                <div class="product-brand">${currentProduct.brand}</div>
                <h1 class="product-title">${currentProduct.name}</h1>
                
                <div class="product-rating">
                    <div class="rating-stars">
                        ${generateStarRating(currentProduct.rating)}
                    </div>
                    <span class="rating-text">(${currentProduct.reviewCount} đánh giá)</span>
                </div>
                
                <div class="product-price">
                    <span class="current-price">${formatCurrency(currentProduct.price)}</span>
                    ${currentProduct.originalPrice > currentProduct.price ? `
                        <span class="original-price">${formatCurrency(currentProduct.originalPrice)}</span>
                        <span class="discount-percent">-${discountPercent}%</span>
                    ` : ''}
                </div>
                
                <div class="condition-info">
                    <div class="condition-header">
                        <h5 class="condition-title">Tình trạng máy</h5>
                        <span class="badge condition-badge-large ${getConditionClass(currentProduct.condition)}">
                            ${getConditionText(currentProduct.condition)}
                        </span>
                    </div>
                    <div class="condition-progress">
                        <div class="progress">
                            <div class="progress-bar ${getConditionProgressClass(currentProduct.condition)}" 
                                 style="width: ${currentProduct.condition}%"></div>
                        </div>
                    </div>
                    <p class="condition-description">${currentProduct.conditionDetails}</p>
                </div>
                
                <div class="warranty-info">
                    <h6 class="warranty-title">
                        <i class="fas fa-shield-alt"></i>
                        Chính sách bảo hành
                    </h6>
                    <ul class="warranty-features">
                        <li><i class="fas fa-check"></i> Bảo hành ${currentProduct.warranty} tháng</li>
                        <li><i class="fas fa-check"></i> Đổi trả trong 7 ngày</li>
                        <li><i class="fas fa-check"></i> Hỗ trợ kỹ thuật miễn phí</li>
                        <li><i class="fas fa-check"></i> Bảo hành tại cửa hàng</li>
                    </ul>
                </div>
                
                <div class="stock-status">
                    <div class="stock-indicator ${getStockClass(currentProduct.stockQuantity)}"></div>
                    <span class="stock-text ${getStockClass(currentProduct.stockQuantity)}">
                        ${getStockText(currentProduct.stockQuantity)}
                    </span>
                </div>
                
                <div class="product-actions">
                    <div class="quantity-selector">
                        <span>Số lượng:</span>
                        <button class="quantity-btn" onclick="decreaseQuantity()">-</button>
                        <input type="number" class="quantity-input" id="quantityInput" value="1" min="1" max="${currentProduct.stockQuantity}" onchange="updateQuantity(this.value)">
                        <button class="quantity-btn" onclick="increaseQuantity()">+</button>
                    </div>
                    
                    <div class="action-buttons">
                        <button class="btn btn-add-cart" onclick="addToCartFromDetail()" ${!currentProduct.inStock ? 'disabled' : ''}>
                            <i class="fas fa-cart-plus me-2"></i>Thêm vào giỏ hàng
                        </button>
                        <button class="btn btn-buy-now" onclick="buyNow()" ${!currentProduct.inStock ? 'disabled' : ''}>
                            <i class="fas fa-bolt me-2"></i>Mua ngay
                        </button>
                        <button class="btn btn-wishlist" onclick="toggleWishlistFromDetail()" id="wishlistBtn">
                            <i class="fas fa-heart me-2"></i>Yêu thích
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-12">
            <div class="product-tabs mt-5" data-aos="fade-up">
                <ul class="nav nav-tabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#description">Mô tả sản phẩm</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#specifications">Thông số kỹ thuật</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#reviews">Đánh giá (${currentProduct.reviewCount})</a>
                    </li>
                </ul>
                
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="description">
                        <p>${currentProduct.description}</p>
                    </div>
                    
                    <div class="tab-pane fade" id="specifications">
                        <table class="specifications-table">
                            ${Object.entries(currentProduct.specifications).map(([key, value]) => `
                                <tr>
                                    <th>${key}</th>
                                    <td>${value}</td>
                                </tr>
                            `).join('')}
                        </table>
                    </div>
                    
                    <div class="tab-pane fade" id="reviews">
                        ${renderReviewsSection()}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    updateWishlistButton();
}

function renderReviewsSection() {
    const avgRating = currentProduct.rating;
    const reviews = currentProduct.reviews || [];
    
    // Calculate rating distribution
    const ratingDistribution = [5, 4, 3, 2, 1].map(rating => {
        const count = reviews.filter(r => r.rating === rating).length;
        const percentage = reviews.length > 0 ? (count / reviews.length) * 100 : 0;
        return { rating, count, percentage };
    });
    
    return `
        <div class="review-summary">
            <div class="row">
                <div class="col-md-4">
                    <div class="review-overview">
                        <div class="avg-rating-number">${avgRating}</div>
                        <div class="rating-stars mb-2">
                            ${generateStarRating(avgRating)}
                        </div>
                        <p class="text-muted">${currentProduct.reviewCount} đánh giá</p>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="rating-distribution">
                        ${ratingDistribution.map(item => `
                            <div class="rating-bar">
                                <span class="rating-label">${item.rating} sao</span>
                                <div class="rating-progress">
                                    <div class="progress">
                                        <div class="progress-bar bg-warning" style="width: ${item.percentage}%"></div>
                                    </div>
                                </div>
                                <span class="rating-count">${item.count}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="reviews-list">
            ${reviews.map(review => `
                <div class="review-item">
                    <div class="review-header">
                        <div class="reviewer-info">
                            <img src="${review.avatar}" alt="${review.userName}" class="reviewer-avatar">
                            <div>
                                <div class="reviewer-name">${review.userName}</div>
                                <div class="rating-stars">
                                    ${generateStarRating(review.rating)}
                                </div>
                            </div>
                        </div>
                        <div class="review-date">${formatDate(review.date)}</div>
                    </div>
                    
                    ${review.title ? `<h6 class="review-title">${review.title}</h6>` : ''}
                    
                    <div class="review-content">${review.content}</div>
                    
                    ${review.pros || review.cons ? `
                        <div class="row mt-3">
                            ${review.pros ? `
                                <div class="col-md-6">
                                    <div class="text-success">
                                        <strong><i class="fas fa-thumbs-up me-1"></i> Ưu điểm:</strong>
                                        <p class="mb-0">${review.pros}</p>
                                    </div>
                                </div>
                            ` : ''}
                            ${review.cons ? `
                                <div class="col-md-6">
                                    <div class="text-danger">
                                        <strong><i class="fas fa-thumbs-down me-1"></i> Nhược điểm:</strong>
                                        <p class="mb-0">${review.cons}</p>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    ` : ''}
                    
                    ${review.images && review.images.length > 0 ? `
                        <div class="review-images mt-3">
                            ${review.images.map(img => `
                                <img src="${img}" alt="Review image" class="review-image" onclick="openImageModal('${img}')">
                            `).join('')}
                        </div>
                    ` : ''}
                    
                    <div class="review-actions mt-3">
                        <button class="btn btn-sm btn-outline-primary" onclick="markHelpful(${review.id})">
                            <i class="fas fa-thumbs-up me-1"></i> Hữu ích (${review.helpful})
                        </button>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

function initializeProductPage() {
    AOS.init({
        duration: 1000,
        once: true
    });
}

// Image functions
function changeMainImage(index) {
    const mainImage = document.getElementById('mainImage');
    const thumbnails = document.querySelectorAll('.thumbnail-image');
    
    mainImage.src = currentProduct.images[index];
    currentImageIndex = index;
    
    thumbnails.forEach((thumb, i) => {
        thumb.classList.toggle('active', i === index);
    });
}

function openImageModal(src) {
    const modal = new bootstrap.Modal(document.getElementById('imageModal'));
    document.getElementById('modalImage').src = src;
    modal.show();
}

// Quantity functions
function increaseQuantity() {
    const input = document.getElementById('quantityInput');
    const currentValue = parseInt(input.value);
    const maxValue = parseInt(input.max);
    
    if (currentValue < maxValue) {
        input.value = currentValue + 1;
        selectedQuantity = currentValue + 1;
    }
}

function decreaseQuantity() {
    const input = document.getElementById('quantityInput');
    const currentValue = parseInt(input.value);
    
    if (currentValue > 1) {
        input.value = currentValue - 1;
        selectedQuantity = currentValue - 1;
    }
}

function updateQuantity(value) {
    selectedQuantity = parseInt(value);
}

// Action functions
function addToCartFromDetail() {
    if (currentProduct && currentProduct.inStock) {
        addToCart(currentProduct.id, selectedQuantity);
    }
}

function buyNow() {
    if (currentProduct && currentProduct.inStock) {
        addToCart(currentProduct.id, selectedQuantity);
        window.location.href = 'checkout.html';
    }
}

function toggleWishlistFromDetail() {
    if (currentProduct) {
        toggleWishlist(currentProduct.id);
        updateWishlistButton();
    }
}

function updateWishlistButton() {
    const btn = document.getElementById('wishlistBtn');
    if (btn && currentProduct) {
        const isInWishlist = wishlist.some(item => item.id === currentProduct.id);
        btn.classList.toggle('active', isInWishlist);
        btn.innerHTML = `<i class="fas fa-heart me-2"></i>${isInWishlist ? 'Đã yêu thích' : 'Yêu thích'}`;
    }
}

// Utility functions
function getConditionProgressClass(condition) {
    if (condition >= 95) return 'bg-success';
    if (condition >= 85) return 'bg-primary';
    if (condition >= 70) return 'bg-warning';
    return 'bg-danger';
}

function getStockClass(quantity) {
    if (quantity > 5) return 'in-stock';
    if (quantity > 0) return 'low-stock';
    return 'out-of-stock';
}

function getStockText(quantity) {
    if (quantity > 5) return `Còn hàng (${quantity} sản phẩm)`;
    if (quantity > 0) return `Sắp hết hàng (${quantity} sản phẩm)`;
    return 'Hết hàng';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
}

function markHelpful(reviewId) {
    // Simulate marking review as helpful
    showNotification('Cảm ơn bạn đã đánh giá!', 'success');
}

function loadRelatedProducts() {
    const container = document.getElementById('relatedProductsContainer');
    const relatedProducts = sampleProducts.filter(p => 
        p.category === currentProduct.category && p.id !== currentProduct.id
    ).slice(0, 4);
    
    container.innerHTML = relatedProducts.map(product => createProductCard(product)).join('');
}

function updateBreadcrumb() {
    const categoryBreadcrumb = document.getElementById('categoryBreadcrumb');
    const productBreadcrumb = document.getElementById('productBreadcrumb');
    
    if (categoryBreadcrumb && productBreadcrumb) {
        categoryBreadcrumb.textContent = getCategoryName(currentProduct.category);
        categoryBreadcrumb.href = `category.html?cat=${currentProduct.category}`;
        productBreadcrumb.textContent = currentProduct.name;
    }
}

function getCategoryName(category) {
    const categoryNames = {
        'laptop': 'Laptop',
        'dien-thoai': 'Điện thoại',
        'tablet': 'Tablet',
        'phu-kien': 'Phụ kiện',
        'linh-kien': 'Linh kiện'
    };
    return categoryNames[category] || 'Sản phẩm';
}

function showNotFound() {
    const container = document.getElementById('productDetailContainer');
    container.innerHTML = `
        <div class="col-12 text-center py-5">
            <i class="fas fa-exclamation-triangle fa-5x text-warning mb-4"></i>
            <h2>Không tìm thấy sản phẩm</h2>
            <p class="text-muted mb-4">Sản phẩm bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.</p>
            <a href="index.html" class="btn btn-primary">Về trang chủ</a>
        </div>
    `;
}
