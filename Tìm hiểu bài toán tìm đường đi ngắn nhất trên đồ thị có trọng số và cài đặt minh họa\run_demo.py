"""
Script chính để chạy demo toàn bộ dự án
Author: Student
Date: 2025
"""

import os
import sys
import subprocess

def print_header():
    """In header của chương trình"""
    print("=" * 80)
    print("DEMO DỰ ÁN: TÌM HIỂU BÀI TOÁN TÌM ĐƯỜNG ĐI NGẮN NHẤT")
    print("TRÊN ĐỒ THỊ CÓ TRỌNG SỐ VÀ CÀI ĐẶT MINH HỌA")
    print("=" * 80)
    print("Sinh viên: [Tên sinh viên]")
    print("Lớp: [Tên lớp]")
    print("Môn học: Cấu trúc dữ liệu và giải thuật")
    print("Năm học: 2024-2025")
    print("=" * 80)

def check_dependencies():
    """Kiểm tra và cài đặt dependencies"""
    print("\n📦 Kiểm tra dependencies...")
    
    required_packages = ['numpy', 'matplotlib', 'networkx']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} đã được cài đặt")
        except ImportError:
            print(f"❌ {package} chưa được cài đặt")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Cần cài đặt các package: {', '.join(missing_packages)}")
        print("Chạy lệnh: pip install -r requirements.txt")
        return False
    
    print("✅ Tất cả dependencies đã sẵn sàng!")
    return True

def run_main_demo():
    """Chạy demo chính"""
    print("\n🚀 Chạy demo các giải thuật...")
    print("-" * 50)
    
    try:
        # Thêm thư mục src vào path
        src_path = os.path.join(os.path.dirname(__file__), 'src')
        sys.path.insert(0, src_path)
        
        # Import và chạy main
        from main import main
        main()
        
        return True
    except Exception as e:
        print(f"❌ Lỗi khi chạy demo chính: {e}")
        return False

def run_examples():
    """Chạy các ví dụ minh họa"""
    print("\n📚 Chạy các ví dụ minh họa...")
    print("-" * 50)
    
    try:
        # Thêm thư mục examples vào path
        examples_path = os.path.join(os.path.dirname(__file__), 'examples')
        sys.path.insert(0, examples_path)
        
        # Import và chạy examples
        from example_usage import main
        main()
        
        return True
    except Exception as e:
        print(f"❌ Lỗi khi chạy ví dụ: {e}")
        return False

def run_tests():
    """Chạy test cases"""
    print("\n🧪 Chạy test cases...")
    print("-" * 50)
    
    try:
        # Thêm thư mục tests vào path
        tests_path = os.path.join(os.path.dirname(__file__), 'tests')
        sys.path.insert(0, tests_path)
        
        # Import và chạy tests
        from test_algorithms import run_all_tests
        success = run_all_tests()
        
        return success
    except Exception as e:
        print(f"❌ Lỗi khi chạy tests: {e}")
        return False

def run_visualization():
    """Chạy demo visualization"""
    print("\n📊 Chạy demo visualization...")
    print("-" * 50)
    
    try:
        # Thêm thư mục src vào path
        src_path = os.path.join(os.path.dirname(__file__), 'src')
        sys.path.insert(0, src_path)
        
        # Import và chạy visualization
        from graph_visualization import demo_visualization
        demo_visualization()
        
        return True
    except Exception as e:
        print(f"❌ Lỗi khi chạy visualization: {e}")
        print("Lưu ý: Visualization cần matplotlib và networkx")
        return False

def show_menu():
    """Hiển thị menu lựa chọn"""
    print("\n📋 MENU LỰA CHỌN:")
    print("1. Chạy demo tất cả giải thuật")
    print("2. Chạy ví dụ minh họa thực tế")
    print("3. Chạy test cases")
    print("4. Chạy visualization (cần matplotlib)")
    print("5. Chạy tất cả")
    print("0. Thoát")
    print("-" * 30)

def main():
    """Hàm chính"""
    print_header()
    
    # Kiểm tra dependencies
    if not check_dependencies():
        print("\n⚠️  Vui lòng cài đặt dependencies trước khi tiếp tục!")
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("Nhập lựa chọn của bạn (0-5): ").strip()
            
            if choice == '0':
                print("\n👋 Cảm ơn bạn đã sử dụng chương trình!")
                break
            elif choice == '1':
                success = run_main_demo()
            elif choice == '2':
                success = run_examples()
            elif choice == '3':
                success = run_tests()
            elif choice == '4':
                success = run_visualization()
            elif choice == '5':
                print("\n🎯 Chạy tất cả demo...")
                success1 = run_main_demo()
                success2 = run_examples()
                success3 = run_tests()
                success4 = run_visualization()
                success = success1 and success2 and success3 and success4
            else:
                print("❌ Lựa chọn không hợp lệ! Vui lòng chọn từ 0-5.")
                continue
            
            if success:
                print("\n✅ Hoàn thành thành công!")
            else:
                print("\n⚠️  Có lỗi xảy ra trong quá trình thực thi.")
            
            input("\nNhấn Enter để tiếp tục...")
            
        except KeyboardInterrupt:
            print("\n\n👋 Chương trình bị ngắt. Tạm biệt!")
            break
        except Exception as e:
            print(f"\n❌ Lỗi không mong muốn: {e}")
            input("Nhấn Enter để tiếp tục...")

if __name__ == "__main__":
    main()
