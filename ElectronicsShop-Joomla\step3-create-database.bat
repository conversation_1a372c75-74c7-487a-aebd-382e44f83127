@echo off
title Step 3 - Create Database
color 0C

echo ========================================
echo    STEP 3: CREATE DATABASE
echo ========================================
echo.

echo Opening phpMyAdmin for database creation...
start "" "http://localhost/phpmyadmin"

echo.
echo ========================================
echo    DATABASE CREATION STEPS
echo ========================================
echo.
echo Please follow these steps in phpMyAdmin:
echo.
echo 1. LOGIN TO PHPMYADMIN:
echo    - Username: root
echo    - Password: (leave empty)
echo    - Click "Go"
echo.
echo 2. CREATE NEW DATABASE:
echo    - Click "Databases" tab at the top
echo    - Database name: electronics_shop
echo    - Collation: utf8mb4_unicode_ci
echo    - Click "Create" button
echo.
echo 3. VERIFY DATABASE:
echo    - You should see "electronics_shop" in the left sidebar
echo    - Database should show "0 tables" (empty - this is correct)
echo.
echo Press any key when database is created...
pause

echo ========================================
echo    TESTING DATABASE CONNECTION
echo ========================================
echo.

echo Testing database connection...
echo.
echo The database "electronics_shop" should now exist.
echo We will use these connection details for Joomla:
echo.
echo Database Type: MySQLi
echo Host Name: localhost
echo Username: root
echo Password: (empty)
echo Database Name: electronics_shop
echo Table Prefix: jos_
echo.
echo Press any key to continue to Joomla installation...
pause

echo ========================================
echo    STEP 3 COMPLETE!
echo ========================================
echo.
echo [SUCCESS] Database created successfully!
echo.
echo Database Details:
echo - Name: electronics_shop
echo - Host: localhost
echo - User: root
echo - Password: (empty)
echo - Collation: utf8mb4_unicode_ci
echo.
echo Next step: Download and install Joomla
echo.
echo Press any key to continue to Step 4...
pause
