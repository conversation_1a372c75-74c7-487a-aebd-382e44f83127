using DoublyLinkedListApp.DataStructures;
using DoublyLinkedListApp.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DoublyLinkedListApp.Services
{
    /// <summary>
    /// Service để quản lý các thao tác với hàng hóa
    /// </summary>
    public class HangHoaService
    {
        private readonly DoublyLinkedList danhSachHangHoa;

        public HangHoaService()
        {
            danhSachHangHoa = new DoublyLinkedList();
        }

        /// <summary>
        /// Thêm hàng hóa mới
        /// </summary>
        public bool ThemHangHoa(HangHoa hangHoa)
        {
            if (danhSachHangHoa.Contains(hangHoa.MaSo))
            {
                Console.WriteLine($"Mã số {hangHoa.MaSo} đã tồn tại!");
                return false;
            }

            danhSachHangHoa.AddLast(hangHoa);
            Console.WriteLine("Thêm hàng hóa thành công!");
            return true;
        }

        /// <summary>
        /// Xóa hàng hóa theo mã số
        /// </summary>
        public bool XoaHangHoa(string maSo)
        {
            if (danhSachHangHoa.Remove(maSo))
            {
                Console.WriteLine($"Đã xóa hàng hóa có mã số: {maSo}");
                return true;
            }
            Console.WriteLine($"Không tìm thấy hàng hóa có mã số: {maSo}");
            return false;
        }

        /// <summary>
        /// Tìm kiếm hàng hóa theo mã số
        /// </summary>
        public HangHoa? TimKiemTheoMaSo(string maSo)
        {
            return danhSachHangHoa.Find(maSo);
        }

        /// <summary>
        /// Tìm kiếm hàng hóa theo tên hàng
        /// </summary>
        public List<HangHoa> TimKiemTheoTenHang(string tenHang)
        {
            var result = new List<HangHoa>();
            var danhSach = danhSachHangHoa.ToList();

            foreach (var hangHoa in danhSach)
            {
                if (hangHoa.TenHang.Contains(tenHang, StringComparison.OrdinalIgnoreCase))
                {
                    result.Add(hangHoa);
                }
            }
            return result;
        }

        /// <summary>
        /// Lọc hàng hóa theo đơn vị tính
        /// </summary>
        public List<HangHoa> LocTheoDonViTinh(string donViTinh)
        {
            var danhSach = danhSachHangHoa.ToList();
            return danhSach.Where(h => h.DonViTinh.Equals(donViTinh, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        /// <summary>
        /// Lọc hàng hóa theo khoảng giá
        /// </summary>
        public List<HangHoa> LocTheoKhoangGia(decimal giaMin, decimal giaMax)
        {
            var danhSach = danhSachHangHoa.ToList();
            return danhSach.Where(h => h.DonGia >= giaMin && h.DonGia <= giaMax).ToList();
        }

        /// <summary>
        /// Lọc hàng hóa theo trạng thái
        /// </summary>
        public List<HangHoa> LocTheoTrangThai(string trangThai)
        {
            var danhSach = danhSachHangHoa.ToList();
            return danhSach.Where(h => h.TrangThai.Equals(trangThai, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        /// <summary>
        /// Lọc hàng hóa theo phân loại giá
        /// </summary>
        public List<HangHoa> LocTheoPhanLoaiGia(string phanLoai)
        {
            var danhSach = danhSachHangHoa.ToList();
            return danhSach.Where(h => h.PhanLoaiGia.Equals(phanLoai, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        /// <summary>
        /// Sắp xếp theo mã số
        /// </summary>
        public List<HangHoa> SapXepTheoMaSo(bool tangDan = true)
        {
            var danhSach = danhSachHangHoa.ToList();
            return tangDan ?
                danhSach.OrderBy(h => h.MaSo).ToList() :
                danhSach.OrderByDescending(h => h.MaSo).ToList();
        }

        /// <summary>
        /// Sắp xếp theo tên hàng
        /// </summary>
        public List<HangHoa> SapXepTheoTenHang(bool tangDan = true)
        {
            var danhSach = danhSachHangHoa.ToList();
            return tangDan ?
                danhSach.OrderBy(h => h.TenHang).ToList() :
                danhSach.OrderByDescending(h => h.TenHang).ToList();
        }

        /// <summary>
        /// Sắp xếp theo đơn giá
        /// </summary>
        public List<HangHoa> SapXepTheoDonGia(bool tangDan = true)
        {
            var danhSach = danhSachHangHoa.ToList();
            return tangDan ?
                danhSach.OrderBy(h => h.DonGia).ToList() :
                danhSach.OrderByDescending(h => h.DonGia).ToList();
        }

        /// <summary>
        /// Sắp xếp theo số lượng
        /// </summary>
        public List<HangHoa> SapXepTheoSoLuong(bool tangDan = true)
        {
            var danhSach = danhSachHangHoa.ToList();
            return tangDan ?
                danhSach.OrderBy(h => h.SoLuong).ToList() :
                danhSach.OrderByDescending(h => h.SoLuong).ToList();
        }

        /// <summary>
        /// Thống kê số lượng hàng hóa theo đơn vị tính
        /// </summary>
        public Dictionary<string, int> ThongKeSoLuongTheoDonViTinh()
        {
            var danhSach = danhSachHangHoa.ToList();
            return danhSach.GroupBy(h => h.DonViTinh)
                          .ToDictionary(g => g.Key, g => g.Sum(h => h.SoLuong));
        }

        /// <summary>
        /// Thống kê số mặt hàng theo đơn vị tính
        /// </summary>
        public Dictionary<string, int> ThongKeSoMatHangTheoDonViTinh()
        {
            var danhSach = danhSachHangHoa.ToList();
            return danhSach.GroupBy(h => h.DonViTinh)
                          .ToDictionary(g => g.Key, g => g.Count());
        }

        /// <summary>
        /// Thống kê số lượng theo trạng thái
        /// </summary>
        public Dictionary<string, int> ThongKeSoLuongTheoTrangThai()
        {
            var danhSach = danhSachHangHoa.ToList();
            return danhSach.GroupBy(h => h.TrangThai)
                          .ToDictionary(g => g.Key, g => g.Count());
        }

        /// <summary>
        /// Tính tổng giá trị hàng hóa
        /// </summary>
        public decimal TinhTongGiaTriHangHoa()
        {
            var danhSach = danhSachHangHoa.ToList();
            return danhSach.Sum(h => h.ThanhTien);
        }

        /// <summary>
        /// Tìm hàng hóa có giá cao nhất
        /// </summary>
        public HangHoa? TimHangHoaGiaCaoNhat()
        {
            var danhSach = danhSachHangHoa.ToList();
            return danhSach.Count > 0 ? danhSach.OrderByDescending(h => h.DonGia).First() : null;
        }

        /// <summary>
        /// Tìm hàng hóa có giá thấp nhất
        /// </summary>
        public HangHoa? TimHangHoaGiaThapNhat()
        {
            var danhSach = danhSachHangHoa.ToList();
            return danhSach.Count > 0 ? danhSach.OrderBy(h => h.DonGia).First() : null;
        }

        /// <summary>
        /// Tìm hàng hóa có số lượng nhiều nhất
        /// </summary>
        public HangHoa? TimHangHoaSoLuongNhieuNhat()
        {
            var danhSach = danhSachHangHoa.ToList();
            return danhSach.Count > 0 ? danhSach.OrderByDescending(h => h.SoLuong).First() : null;
        }

        /// <summary>
        /// Tính giá trị trung bình của hàng hóa
        /// </summary>
        public decimal TinhGiaTriTrungBinh()
        {
            var danhSach = danhSachHangHoa.ToList();
            return danhSach.Count > 0 ? danhSach.Average(h => h.DonGia) : 0;
        }

        /// <summary>
        /// Cập nhật thông tin hàng hóa
        /// </summary>
        public bool CapNhatHangHoa(string maSo, HangHoa hangHoaMoi)
        {
            return danhSachHangHoa.Update(maSo, hangHoaMoi);
        }

        /// <summary>
        /// Lấy tất cả hàng hóa
        /// </summary>
        public List<HangHoa> LayTatCaHangHoa()
        {
            return danhSachHangHoa.ToList();
        }

        /// <summary>
        /// Đếm số lượng hàng hóa
        /// </summary>
        public int DemSoLuong()
        {
            return danhSachHangHoa.Count;
        }

        /// <summary>
        /// Xóa tất cả hàng hóa
        /// </summary>
        public void XoaTatCa()
        {
            danhSachHangHoa.Clear();
            Console.WriteLine("Đã xóa tất cả dữ liệu!");
        }
    }
}
