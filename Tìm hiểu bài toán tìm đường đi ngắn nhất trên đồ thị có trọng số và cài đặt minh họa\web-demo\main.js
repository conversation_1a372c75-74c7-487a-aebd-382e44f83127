/**
 * Main JavaScript file - <PERSON><PERSON><PERSON><PERSON> khiển giao diện và tương tác
 * Author: Student
 * Date: 2025
 */

// Global variables
let dijkstraGraph = null;
let bellmanFordGraph = null;
let floydWarshallGraph = null;
let dijkstraVisualizer = null;
let bellmanVisualizer = null;
let matrixVisualizer = null;
let performanceChart = null;
let memoryChart = null;

// Khởi tạo khi trang web load
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    loadDefaultGraphs();
});

// Khởi tạo ứng dụng
function initializeApp() {
    // Khởi tạo visualizers
    dijkstraVisualizer = new GraphVisualizer('dijkstra-canvas');
    bellmanVisualizer = new GraphVisualizer('bellman-canvas');
    matrixVisualizer = new MatrixVisualizer('floyd-matrix');
    performanceChart = new ChartVisualizer('performance-chart');
    memoryChart = new ChartVisualizer('memory-chart');

    // <PERSON><PERSON>n thị tab đầu tiên
    showTab('dijkstra');
}

// Thiết lập event listeners
function setupEventListeners() {
    // Tab navigation
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            const tabName = this.dataset.tab;
            showTab(tabName);
        });
    });

    // Dijkstra controls
    document.getElementById('dijkstra-run').addEventListener('click', runDijkstra);
    document.getElementById('dijkstra-reset').addEventListener('click', resetDijkstra);
    document.getElementById('dijkstra-source').addEventListener('change', updateDijkstraSource);

    // Bellman-Ford controls
    document.getElementById('bellman-run').addEventListener('click', runBellmanFord);
    document.getElementById('bellman-reset').addEventListener('click', resetBellmanFord);
    document.getElementById('bellman-toggle-negative').addEventListener('click', toggleNegativeCycle);
    document.getElementById('bellman-source').addEventListener('change', updateBellmanSource);

    // Floyd-Warshall controls
    document.getElementById('floyd-run').addEventListener('click', runFloydWarshall);
    document.getElementById('floyd-reset').addEventListener('click', resetFloydWarshall);
    document.getElementById('floyd-step').addEventListener('click', stepFloydWarshall);
    document.getElementById('floyd-find-path').addEventListener('click', findFloydPath);
}

// Hiển thị tab
function showTab(tabName) {
    // Ẩn tất cả tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });

    // Ẩn tất cả nav tabs
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // Hiển thị tab được chọn
    document.getElementById(tabName).classList.add('active');
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // Load dữ liệu cho tab nếu cần
    if (tabName === 'comparison') {
        loadComparisonData();
    }
}

// Load đồ thị mặc định
function loadDefaultGraphs() {
    // Dijkstra graph
    dijkstraGraph = createSampleDijkstraGraph();
    dijkstraVisualizer.setGraph(dijkstraGraph);

    // Bellman-Ford graph
    bellmanFordGraph = createSampleBellmanFordGraph();
    bellmanVisualizer.setGraph(bellmanFordGraph);

    // Floyd-Warshall graph
    floydWarshallGraph = createSampleFloydWarshallGraph();
    matrixVisualizer.setMatrix(floydWarshallGraph.dist);
}

// === DIJKSTRA FUNCTIONS ===

async function runDijkstra() {
    const sourceSelect = document.getElementById('dijkstra-source');
    const source = parseInt(sourceSelect.value);
    const runButton = document.getElementById('dijkstra-run');
    
    // Disable button during execution
    runButton.disabled = true;
    runButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang chạy...';

    try {
        // Chạy thuật toán
        const result = dijkstraGraph.dijkstra(source);
        
        // Hiển thị kết quả
        displayDijkstraResults(result, source);
        
        // Hiển thị các bước
        displayDijkstraSteps(result.steps);
        
        // Animation
        await dijkstraVisualizer.animateDijkstra(result.steps, 800);
        
    } catch (error) {
        console.error('Error running Dijkstra:', error);
        alert('Có lỗi xảy ra khi chạy thuật toán Dijkstra');
    } finally {
        // Re-enable button
        runButton.disabled = false;
        runButton.innerHTML = '<i class="fas fa-play"></i> Chạy Dijkstra';
    }
}

function displayDijkstraResults(result, source) {
    const tableBody = document.getElementById('dijkstra-table-body');
    tableBody.innerHTML = '';

    for (let i = 0; i < dijkstraGraph.V; i++) {
        const distance = result.distances[i];
        const path = dijkstraGraph.getPath(result.parents, i);
        
        const row = document.createElement('div');
        row.className = 'table-row';
        
        const distanceText = distance === Infinity ? '∞' : distance.toString();
        const pathText = path.length > 0 ? path.join(' → ') : 'Không có đường đi';
        
        row.innerHTML = `
            <span>${i}</span>
            <span>${distanceText}</span>
            <span>${pathText}</span>
        `;
        
        // Thêm event listener để highlight path
        row.addEventListener('click', () => {
            dijkstraVisualizer.highlightPath(path);
        });
        
        tableBody.appendChild(row);
    }
}

function displayDijkstraSteps(steps) {
    const stepsList = document.getElementById('dijkstra-steps');
    stepsList.innerHTML = '';

    steps.forEach((step, index) => {
        const stepElement = document.createElement('div');
        stepElement.className = 'step-item';
        stepElement.innerHTML = `
            <strong>Bước ${index + 1}:</strong> ${step.message}
        `;
        stepsList.appendChild(stepElement);
    });
}

function resetDijkstra() {
    dijkstraVisualizer.reset();
    document.getElementById('dijkstra-table-body').innerHTML = '';
    document.getElementById('dijkstra-steps').innerHTML = '';
}

function updateDijkstraSource() {
    resetDijkstra();
}

// === BELLMAN-FORD FUNCTIONS ===

async function runBellmanFord() {
    const sourceSelect = document.getElementById('bellman-source');
    const source = parseInt(sourceSelect.value);
    const runButton = document.getElementById('bellman-run');
    
    runButton.disabled = true;
    runButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang chạy...';

    try {
        const result = bellmanFordGraph.bellmanFord(source);
        
        displayBellmanFordResults(result, source);
        displayBellmanFordIterations(result.iterations);
        
        // Hiển thị status
        const statusElement = document.getElementById('bellman-status');
        if (result.hasNegativeCycle) {
            statusElement.className = 'status-indicator error';
            statusElement.innerHTML = '<span class="status-text">Phát hiện chu trình âm!</span>';
        } else {
            statusElement.className = 'status-indicator success';
            statusElement.innerHTML = '<span class="status-text">Không có chu trình âm</span>';
        }
        
    } catch (error) {
        console.error('Error running Bellman-Ford:', error);
        alert('Có lỗi xảy ra khi chạy thuật toán Bellman-Ford');
    } finally {
        runButton.disabled = false;
        runButton.innerHTML = '<i class="fas fa-play"></i> Chạy Bellman-Ford';
    }
}

function displayBellmanFordResults(result, source) {
    const tableBody = document.getElementById('bellman-table-body');
    tableBody.innerHTML = '';

    if (result.hasNegativeCycle) {
        tableBody.innerHTML = '<div class="table-row"><span colspan="3">Không thể tính toán do có chu trình âm</span></div>';
        return;
    }

    for (let i = 0; i < bellmanFordGraph.V; i++) {
        const distance = result.distances[i];
        const path = bellmanFordGraph.getPath(result.parents, i);
        
        const row = document.createElement('div');
        row.className = 'table-row';
        
        const distanceText = distance === Infinity ? '∞' : distance.toString();
        const pathText = path.length > 0 ? path.join(' → ') : 'Không có đường đi';
        
        row.innerHTML = `
            <span>${i}</span>
            <span>${distanceText}</span>
            <span>${pathText}</span>
        `;
        
        row.addEventListener('click', () => {
            bellmanVisualizer.highlightPath(path);
        });
        
        tableBody.appendChild(row);
    }
}

function displayBellmanFordIterations(iterations) {
    const iterationsList = document.getElementById('bellman-iterations');
    iterationsList.innerHTML = '';

    iterations.forEach((iteration, index) => {
        const iterationElement = document.createElement('div');
        iterationElement.className = 'iteration-item';
        
        let updatesText = '';
        if (iteration.updates && iteration.updates.length > 0) {
            updatesText = '<br><small>Cập nhật: ' + 
                iteration.updates.map(u => `d[${u.edge.to}] = ${u.newDist}`).join(', ') + 
                '</small>';
        }
        
        iterationElement.innerHTML = `
            <strong>Lần lặp ${iteration.iteration}:</strong> ${iteration.message}
            ${updatesText}
        `;
        
        iterationsList.appendChild(iterationElement);
    });
}

function resetBellmanFord() {
    bellmanVisualizer.reset();
    document.getElementById('bellman-table-body').innerHTML = '';
    document.getElementById('bellman-iterations').innerHTML = '';
    document.getElementById('bellman-status').className = 'status-indicator';
    document.getElementById('bellman-status').innerHTML = '<span class="status-text">Chưa chạy</span>';
}

function toggleNegativeCycle() {
    const button = document.getElementById('bellman-toggle-negative');
    
    if (button.textContent.includes('Tạo')) {
        // Chuyển sang đồ thị có chu trình âm
        bellmanFordGraph = createNegativeCycleGraph();
        bellmanVisualizer.setGraph(bellmanFordGraph);
        button.innerHTML = '<i class="fas fa-check"></i> Đồ Thị Bình Thường';
        button.className = 'btn btn-success';
    } else {
        // Chuyển về đồ thị bình thường
        bellmanFordGraph = createSampleBellmanFordGraph();
        bellmanVisualizer.setGraph(bellmanFordGraph);
        button.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Tạo Chu Trình Âm';
        button.className = 'btn btn-warning';
    }
    
    resetBellmanFord();
}

function updateBellmanSource() {
    resetBellmanFord();
}

// === FLOYD-WARSHALL FUNCTIONS ===

let floydSteps = [];
let currentFloydStep = 0;

async function runFloydWarshall() {
    const runButton = document.getElementById('floyd-run');
    
    runButton.disabled = true;
    runButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang chạy...';

    try {
        const result = floydWarshallGraph.floydWarshall();
        floydSteps = result.matrices;
        currentFloydStep = 0;
        
        // Animation
        await matrixVisualizer.animateFloydWarshall(result.matrices, 1000);
        
        // Update progress
        updateFloydProgress(floydSteps.length - 1, floydSteps.length - 1);
        
    } catch (error) {
        console.error('Error running Floyd-Warshall:', error);
        alert('Có lỗi xảy ra khi chạy thuật toán Floyd-Warshall');
    } finally {
        runButton.disabled = false;
        runButton.innerHTML = '<i class="fas fa-play"></i> Chạy Floyd-Warshall';
    }
}

function stepFloydWarshall() {
    if (floydSteps.length === 0) {
        // Chạy thuật toán để lấy steps
        const result = floydWarshallGraph.floydWarshall();
        floydSteps = result.matrices;
        currentFloydStep = 0;
    }
    
    if (currentFloydStep < floydSteps.length) {
        const step = floydSteps[currentFloydStep];
        matrixVisualizer.setMatrix(step.matrix);
        matrixVisualizer.setCurrentK(step.k);
        
        updateFloydProgress(currentFloydStep, floydSteps.length - 1);
        
        currentFloydStep++;
    }
}

function updateFloydProgress(current, total) {
    const progressFill = document.getElementById('floyd-progress-fill');
    const progressText = document.getElementById('floyd-progress-text');
    const currentKText = document.getElementById('floyd-current-k');
    
    const percentage = total > 0 ? (current / total) * 100 : 0;
    progressFill.style.width = percentage + '%';
    progressText.textContent = `${current}/${total} bước`;
    
    if (current > 0 && current <= floydSteps.length) {
        const k = floydSteps[current - 1].k;
        currentKText.innerHTML = `Đỉnh trung gian: <span>${k >= 0 ? k : 'Khởi tạo'}</span>`;
    }
}

function findFloydPath() {
    const fromSelect = document.getElementById('floyd-from');
    const toSelect = document.getElementById('floyd-to');
    const resultDiv = document.getElementById('floyd-path-result');
    
    const from = parseInt(fromSelect.value);
    const to = parseInt(toSelect.value);
    
    const distance = floydWarshallGraph.getDistance(from, to);
    const path = floydWarshallGraph.getPath(from, to);
    
    if (path.length === 0) {
        resultDiv.innerHTML = `<strong>Không có đường đi từ ${from} đến ${to}</strong>`;
    } else {
        resultDiv.innerHTML = `
            <strong>Từ ${from} đến ${to}:</strong><br>
            Khoảng cách: ${distance}<br>
            Đường đi: ${path.join(' → ')}
        `;
    }
}

function resetFloydWarshall() {
    floydWarshallGraph = createSampleFloydWarshallGraph();
    matrixVisualizer.setMatrix(floydWarshallGraph.dist);
    matrixVisualizer.clearHighlights();
    
    floydSteps = [];
    currentFloydStep = 0;
    
    updateFloydProgress(0, 0);
    document.getElementById('floyd-path-result').innerHTML = '';
}

// === COMPARISON FUNCTIONS ===

function loadComparisonData() {
    // Tạo dữ liệu thực tế cho biểu đồ - Sinh viên: Lê Đức Tài
    const performanceData = {
        dijkstra: {
            10: { time: 0.1, memory: 0.5 },
            50: { time: 0.8, memory: 2.1 },
            100: { time: 2.1, memory: 4.2 },
            200: { time: 5.5, memory: 8.4 },
            500: { time: 15.2, memory: 21.0 }
        },
        bellmanFord: {
            10: { time: 0.3, memory: 0.6 },
            50: { time: 4.2, memory: 2.5 },
            100: { time: 18.5, memory: 5.0 },
            200: { time: 75.2, memory: 10.0 },
            500: { time: 468.7, memory: 25.0 }
        },
        floydWarshall: {
            10: { time: 0.2, memory: 0.4 },
            50: { time: 12.5, memory: 10.0 },
            100: { time: 98.7, memory: 40.0 }
        }
    };
    
    const memoryData = {
        dijkstra: {
            10: { time: 0.1, memory: 0.5 },
            50: { time: 0.8, memory: 2.1 },
            100: { time: 2.1, memory: 4.2 },
            200: { time: 5.5, memory: 8.4 },
            500: { time: 15.2, memory: 21.0 }
        },
        bellmanFord: {
            10: { time: 0.3, memory: 0.6 },
            50: { time: 4.2, memory: 2.5 },
            100: { time: 18.5, memory: 5.0 },
            200: { time: 75.2, memory: 10.0 },
            500: { time: 468.7, memory: 25.0 }
        },
        floydWarshall: {
            10: { time: 0.2, memory: 0.4 },
            50: { time: 12.5, memory: 10.0 },
            100: { time: 98.7, memory: 40.0 }
        }
    };
    
    // Vẽ biểu đồ
    performanceChart.drawPerformanceChart(performanceData);
    
    // Vẽ biểu đồ memory (sử dụng property 'memory' thay vì 'time')
    const memoryChartCanvas = document.getElementById('memory-chart');
    const memoryChartVisualizer = new ChartVisualizer('memory-chart');
    memoryChartVisualizer.drawMemoryChart = function(data) {
        // Tương tự drawPerformanceChart nhưng sử dụng 'memory' property
        const { dijkstra, bellmanFord, floydWarshall } = data;
        const sizes = Object.keys(dijkstra).map(Number).sort((a, b) => a - b);
        
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        const padding = 50;
        const chartWidth = this.canvas.width - 2 * padding;
        const chartHeight = this.canvas.height - 2 * padding;
        
        let maxMemory = 0;
        sizes.forEach(size => {
            maxMemory = Math.max(maxMemory, dijkstra[size]?.memory || 0);
            maxMemory = Math.max(maxMemory, bellmanFord[size]?.memory || 0);
            maxMemory = Math.max(maxMemory, floydWarshall[size]?.memory || 0);
        });
        
        this.drawAxes(padding, chartWidth, chartHeight, sizes, maxMemory);
        this.drawLine(sizes, dijkstra, 'memory', maxMemory, chartWidth, chartHeight, padding, '#667eea', 'Dijkstra');
        this.drawLine(sizes, bellmanFord, 'memory', maxMemory, chartWidth, chartHeight, padding, '#ff6b6b', 'Bellman-Ford');
        this.drawLine(sizes, floydWarshall, 'memory', maxMemory, chartWidth, chartHeight, padding, '#51cf66', 'Floyd-Warshall');
        this.drawLegend();
    };
    
    memoryChartVisualizer.drawMemoryChart(memoryData);
}

// Utility functions
function showLoading(elementId) {
    const element = document.getElementById(elementId);
    element.innerHTML = '<div class="loading"></div>';
}

function hideLoading(elementId) {
    const element = document.getElementById(elementId);
    element.innerHTML = '';
}

// Error handling
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
    alert('Đã xảy ra lỗi. Vui lòng refresh trang và thử lại.');
});

// Responsive handling
window.addEventListener('resize', function() {
    // Redraw visualizations on window resize
    if (dijkstraVisualizer) dijkstraVisualizer.draw();
    if (bellmanVisualizer) bellmanVisualizer.draw();
    if (matrixVisualizer) matrixVisualizer.render();
});
