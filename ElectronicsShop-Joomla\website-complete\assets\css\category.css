/* Category Page Styles */

/* Category Header */
.category-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    position: relative;
    overflow: hidden;
}

.category-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.category-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.category-description {
    font-size: 1.125rem;
    opacity: 0.9;
}

.category-stats {
    text-align: right;
}

.product-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

/* Filters Sidebar */
.filters-sidebar {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    position: sticky;
    top: 120px;
    max-height: calc(100vh - 140px);
    overflow-y: auto;
}

.filter-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--primary-color);
}

.filter-group {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.filter-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.filter-group-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group-title::before {
    content: '';
    width: 4px;
    height: 16px;
    background: var(--primary-color);
    border-radius: 2px;
}

/* Filter Controls */
.form-check {
    margin-bottom: 0.75rem;
    padding-left: 1.75rem;
}

.form-check-input {
    margin-top: 0.125rem;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    font-size: 0.9rem;
    color: var(--dark-color);
    cursor: pointer;
    transition: var(--transition-base);
}

.form-check-label:hover {
    color: var(--primary-color);
}

/* Price Range Slider */
.price-range-slider {
    margin: 1rem 0;
}

.price-inputs {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    margin-top: 1rem;
}

.price-inputs input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
}

/* Filter Actions */
.filter-actions {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #eee;
}

.filter-actions .btn {
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.75rem 1rem;
}

/* Products Toolbar */
.products-toolbar {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.results-info {
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.sort-options select {
    min-width: 200px;
    font-size: 0.875rem;
}

.view-options {
    display: flex;
    gap: 0.25rem;
}

.view-options .btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    transition: var(--transition-base);
}

.view-options .btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Products Container */
.products-container {
    min-height: 600px;
}

/* Grid View */
.products-grid .row {
    margin: 0 -0.75rem;
}

.products-grid .col-lg-4,
.products-grid .col-md-6 {
    padding: 0 0.75rem;
    margin-bottom: 1.5rem;
}

/* List View */
.products-list .product-card {
    display: flex;
    flex-direction: row;
    margin-bottom: 1.5rem;
    max-width: none;
}

.products-list .product-image {
    width: 200px;
    height: 150px;
    flex-shrink: 0;
}

.products-list .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.products-list .product-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.products-list .product-actions .btn {
    flex: 1;
    font-size: 0.875rem;
    padding: 0.5rem;
}

/* No Results */
.no-results {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--secondary-color);
}

.no-results i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.5;
}

.no-results h3 {
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.no-results p {
    font-size: 1.125rem;
    margin-bottom: 2rem;
}

/* Loading State */
.loading-products {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    flex-direction: column;
    gap: 1rem;
}

.loading-products .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Pagination */
.pagination {
    margin-top: 3rem;
}

.pagination .page-link {
    color: var(--primary-color);
    border-color: #dee2e6;
    padding: 0.75rem 1rem;
    font-weight: 500;
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-item.disabled .page-link {
    color: var(--secondary-color);
}

/* Active Filters */
.active-filters {
    margin-bottom: 1.5rem;
}

.active-filters .filter-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.active-filters .filter-tag .remove-filter {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-base);
}

.active-filters .filter-tag .remove-filter:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Mobile Filters */
.mobile-filters-toggle {
    display: none;
    width: 100%;
    margin-bottom: 1rem;
}

.filters-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
}

.filters-overlay.show {
    display: block;
}

.mobile-filters {
    position: fixed;
    top: 0;
    left: -100%;
    width: 300px;
    height: 100%;
    background: white;
    z-index: 1050;
    transition: left 0.3s ease;
    overflow-y: auto;
    padding: 1rem;
}

.mobile-filters.show {
    left: 0;
}

.mobile-filters-header {
    display: flex;
    justify-content: between;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
    margin-bottom: 1rem;
}

.close-mobile-filters {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--dark-color);
}

/* Responsive Design */
@media (max-width: 992px) {
    .filters-sidebar {
        display: none;
    }
    
    .mobile-filters-toggle {
        display: block;
    }
    
    .category-title {
        font-size: 2rem;
    }
    
    .products-toolbar {
        padding: 1rem;
    }
    
    .products-toolbar .row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .sort-options select {
        min-width: auto;
        width: 100%;
    }
    
    .view-options {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .category-header {
        padding: 2rem 0;
    }
    
    .category-title {
        font-size: 1.75rem;
    }
    
    .category-description {
        font-size: 1rem;
    }
    
    .category-stats {
        text-align: center;
        margin-top: 1rem;
    }
    
    .products-list .product-card {
        flex-direction: column;
    }
    
    .products-list .product-image {
        width: 100%;
        height: 200px;
    }
    
    .products-list .product-actions {
        flex-direction: column;
    }
}

@media (max-width: 576px) {
    .category-title {
        font-size: 1.5rem;
    }
    
    .products-toolbar {
        padding: 0.75rem;
    }
    
    .pagination .page-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .mobile-filters {
        width: 280px;
    }
}

/* Print Styles */
@media print {
    .filters-sidebar,
    .products-toolbar,
    .pagination,
    .mobile-filters-toggle {
        display: none !important;
    }
    
    .products-container {
        width: 100% !important;
    }
    
    .product-card {
        break-inside: avoid;
        margin-bottom: 1rem;
    }
}
