<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> m<PERSON><PERSON> sản phẩm - Electronics Shop</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/category.css" rel="stylesheet">
</head>
<body>
    <!-- Header (same as index.html) -->
    <div class="top-bar bg-dark text-white py-2">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <small>
                        <i class="fas fa-phone me-2"></i>Hotline: 0123-456-789
                        <span class="ms-3"><i class="fas fa-envelope me-2"></i><EMAIL></span>
                    </small>
                </div>
                <div class="col-md-6 text-end">
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-youtube"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <header class="main-header bg-white shadow-sm sticky-top">
        <div class="container">
            <nav class="navbar navbar-expand-lg navbar-light py-3">
                <a class="navbar-brand fw-bold fs-3 text-primary" href="index.html">
                    <i class="fas fa-laptop me-2"></i>Electronics Shop
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <div class="mx-auto">
                        <div class="search-container position-relative">
                            <div class="input-group">
                                <input type="text" class="form-control form-control-lg" id="searchInput" placeholder="Tìm kiếm sản phẩm...">
                                <button class="btn btn-primary" type="button" onclick="performSearch()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <div class="search-suggestions position-absolute w-100 bg-white border rounded-bottom shadow-lg" id="searchSuggestions" style="display: none; z-index: 1000;"></div>
                        </div>
                    </div>
                    
                    <div class="d-flex align-items-center ms-3">
                        <div class="dropdown me-3">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <span id="userDisplayName">Tài khoản</span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="login.html" id="loginLink"><i class="fas fa-sign-in-alt me-2"></i>Đăng nhập</a></li>
                                <li><a class="dropdown-item" href="register.html" id="registerLink"><i class="fas fa-user-plus me-2"></i>Đăng ký</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="profile.html" id="profileLink" style="display: none;"><i class="fas fa-user-circle me-2"></i>Hồ sơ</a></li>
                                <li><a class="dropdown-item" href="orders.html" id="ordersLink" style="display: none;"><i class="fas fa-shopping-bag me-2"></i>Đơn hàng</a></li>
                                <li><a class="dropdown-item" href="#" id="logoutLink" style="display: none;" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>Đăng xuất</a></li>
                            </ul>
                        </div>
                        
                        <button class="btn btn-outline-secondary me-3 position-relative" onclick="toggleWishlist()">
                            <i class="fas fa-heart"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="wishlistCount">0</span>
                        </button>
                        
                        <div class="dropdown">
                            <button class="btn btn-primary position-relative" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-shopping-cart"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="cartCount">0</span>
                            </button>
                            <div class="dropdown-menu dropdown-menu-end cart-dropdown p-3" style="min-width: 350px;">
                                <h6 class="dropdown-header">Giỏ hàng của bạn</h6>
                                <div id="cartItems">
                                    <div class="text-center py-3 text-muted">
                                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                        <p>Giỏ hàng trống</p>
                                    </div>
                                </div>
                                <div class="cart-total border-top pt-2" id="cartTotal" style="display: none;">
                                    <div class="d-flex justify-content-between">
                                        <strong>Tổng cộng:</strong>
                                        <strong class="text-danger" id="cartTotalAmount">0₫</strong>
                                    </div>
                                    <div class="d-grid gap-2 mt-2">
                                        <a href="cart.html" class="btn btn-primary btn-sm">Xem giỏ hàng</a>
                                        <a href="checkout.html" class="btn btn-success btn-sm">Thanh toán</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="bg-light py-3">
        <div class="container">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="index.html">Trang chủ</a></li>
                <li class="breadcrumb-item active" aria-current="page" id="categoryBreadcrumb">Danh mục</li>
            </ol>
        </div>
    </nav>

    <!-- Category Header -->
    <section class="category-header py-4 bg-primary text-white">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="category-title mb-2" id="categoryTitle">Tất cả sản phẩm</h1>
                    <p class="category-description mb-0" id="categoryDescription">Khám phá bộ sưu tập thiết bị điện tử cũ chất lượng cao</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="category-stats">
                        <span class="product-count" id="productCount">0 sản phẩm</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Filters and Products -->
    <section class="products-section py-5">
        <div class="container">
            <div class="row">
                <!-- Filters Sidebar -->
                <div class="col-lg-3">
                    <div class="filters-sidebar" data-aos="fade-right">
                        <div class="filter-section">
                            <h5 class="filter-title">Bộ lọc tìm kiếm</h5>
                            
                            <!-- Price Filter -->
                            <div class="filter-group">
                                <h6 class="filter-group-title">Khoảng giá</h6>
                                <div class="price-filter">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="priceRange" id="price-all" value="" checked>
                                        <label class="form-check-label" for="price-all">Tất cả</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="priceRange" id="price-1" value="0-5000000">
                                        <label class="form-check-label" for="price-1">Dưới 5 triệu</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="priceRange" id="price-2" value="5000000-10000000">
                                        <label class="form-check-label" for="price-2">5 - 10 triệu</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="priceRange" id="price-3" value="10000000-20000000">
                                        <label class="form-check-label" for="price-3">10 - 20 triệu</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="priceRange" id="price-4" value="20000000-50000000">
                                        <label class="form-check-label" for="price-4">20 - 50 triệu</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="priceRange" id="price-5" value="50000000-999999999">
                                        <label class="form-check-label" for="price-5">Trên 50 triệu</label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Brand Filter -->
                            <div class="filter-group">
                                <h6 class="filter-group-title">Thương hiệu</h6>
                                <div class="brand-filter">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="brand-apple" value="Apple">
                                        <label class="form-check-label" for="brand-apple">Apple</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="brand-samsung" value="Samsung">
                                        <label class="form-check-label" for="brand-samsung">Samsung</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="brand-dell" value="Dell">
                                        <label class="form-check-label" for="brand-dell">Dell</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="brand-hp" value="HP">
                                        <label class="form-check-label" for="brand-hp">HP</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="brand-lenovo" value="Lenovo">
                                        <label class="form-check-label" for="brand-lenovo">Lenovo</label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Condition Filter -->
                            <div class="filter-group">
                                <h6 class="filter-group-title">Tình trạng</h6>
                                <div class="condition-filter">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="condition-excellent" value="95-100">
                                        <label class="form-check-label" for="condition-excellent">Như mới (95-100%)</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="condition-good" value="85-94">
                                        <label class="form-check-label" for="condition-good">Tốt (85-94%)</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="condition-fair" value="70-84">
                                        <label class="form-check-label" for="condition-fair">Khá tốt (70-84%)</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="condition-poor" value="0-69">
                                        <label class="form-check-label" for="condition-poor">Cần sửa chữa (<70%)</label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Filter Actions -->
                            <div class="filter-actions">
                                <button class="btn btn-primary btn-sm w-100 mb-2" onclick="applyFilters()">
                                    <i class="fas fa-filter me-2"></i>Áp dụng bộ lọc
                                </button>
                                <button class="btn btn-outline-secondary btn-sm w-100" onclick="clearFilters()">
                                    <i class="fas fa-times me-2"></i>Xóa bộ lọc
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Products Grid -->
                <div class="col-lg-9">
                    <!-- Sort and View Options -->
                    <div class="products-toolbar mb-4" data-aos="fade-left">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="results-info">
                                    <span id="resultsInfo">Hiển thị 1-12 trong tổng số 0 sản phẩm</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex justify-content-end align-items-center gap-3">
                                    <div class="sort-options">
                                        <select class="form-select form-select-sm" id="sortSelect" onchange="sortProducts()">
                                            <option value="default">Sắp xếp mặc định</option>
                                            <option value="price-asc">Giá thấp đến cao</option>
                                            <option value="price-desc">Giá cao đến thấp</option>
                                            <option value="name-asc">Tên A-Z</option>
                                            <option value="name-desc">Tên Z-A</option>
                                            <option value="condition-desc">Tình trạng tốt nhất</option>
                                            <option value="rating-desc">Đánh giá cao nhất</option>
                                        </select>
                                    </div>
                                    <div class="view-options">
                                        <button class="btn btn-sm btn-outline-secondary active" data-view="grid" onclick="changeView('grid')">
                                            <i class="fas fa-th"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" data-view="list" onclick="changeView('list')">
                                            <i class="fas fa-list"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Products Container -->
                    <div class="products-container" id="productsContainer">
                        <div class="row" id="productsGrid">
                            <!-- Products will be loaded here -->
                        </div>
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="Products pagination" class="mt-5">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- Pagination will be generated here -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer (same as index.html) -->
    <footer class="footer bg-dark text-white py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-laptop me-2"></i>Electronics Shop
                    </h5>
                    <p class="text-light">Chuyên cung cấp thiết bị điện tử cũ chất lượng cao với giá tốt nhất thị trường.</p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h5 class="text-primary mb-3">Danh Mục</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="category.html?cat=laptop" class="text-light text-decoration-none">Laptop cũ</a></li>
                        <li class="mb-2"><a href="category.html?cat=dien-thoai" class="text-light text-decoration-none">Điện thoại cũ</a></li>
                        <li class="mb-2"><a href="category.html?cat=tablet" class="text-light text-decoration-none">Tablet & iPad</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h5 class="text-primary mb-3">Hỗ Trợ</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#" class="text-light text-decoration-none">Chính sách bảo hành</a></li>
                        <li class="mb-2"><a href="#" class="text-light text-decoration-none">Hướng dẫn mua hàng</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h5 class="text-primary mb-3">Liên Hệ</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-phone me-2 text-primary"></i>0123-456-789</li>
                        <li class="mb-2"><i class="fas fa-envelope me-2 text-primary"></i><EMAIL></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="btn btn-primary btn-floating" id="backToTop" onclick="scrollToTop()">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/category.js"></script>
</body>
</html>
