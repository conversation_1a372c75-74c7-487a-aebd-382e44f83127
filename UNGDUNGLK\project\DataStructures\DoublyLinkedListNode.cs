using DoublyLinkedListApp.Models;

namespace DoublyLinkedListApp.DataStructures
{
    /// <summary>
    /// Node của danh sách liên kết kép
    /// </summary>
    public class DoublyLinkedListNode
    {
        public HangHoa Data { get; set; }
        public DoublyLinkedListNode? Next { get; set; }
        public DoublyLinkedListNode? Previous { get; set; }

        public DoublyLinkedListNode(HangHoa data)
        {
            Data = data;
            Next = null;
            Previous = null;
        }
    }
}
