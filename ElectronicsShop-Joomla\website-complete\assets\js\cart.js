// Cart Page JavaScript

// Global variables
let appliedCoupon = null;
let shippingCost = 0;
let freeShippingThreshold = 5000000; // 5 million VND

// Sample coupons
const availableCoupons = {
    'WELCOME10': { discount: 0.1, minOrder: 1000000, description: '<PERSON><PERSON><PERSON>m 10% cho đơn hàng từ 1 triệu' },
    'SAVE50K': { discount: 50000, minOrder: 2000000, description: 'Giảm 50K cho đơn hàng từ 2 triệu' },
    'FREESHIP': { freeShipping: true, minOrder: 3000000, description: '<PERSON><PERSON><PERSON> phí vận chuyển từ 3 triệu' },
    'ELECTRONICS20': { discount: 0.2, minOrder: 5000000, description: 'Giảm 20% cho đơn hàng từ 5 triệu' }
};

// Initialize cart page
document.addEventListener('DOMContentLoaded', function() {
    initializeCartPage();
});

function initializeCartPage() {
    // Initialize AOS
    AOS.init({
        duration: 1000,
        once: true
    });
    
    // Load cart content
    loadCartContent();
    
    // Load related products
    loadRelatedProducts();
    
    console.log('Cart page initialized');
}

function loadCartContent() {
    const cartContent = document.getElementById('cartContent');
    
    if (cart.length === 0) {
        renderEmptyCart(cartContent);
    } else {
        renderCartItems(cartContent);
    }
}

function renderEmptyCart(container) {
    container.innerHTML = `
        <div class="col-12">
            <div class="empty-cart" data-aos="fade-up">
                <i class="fas fa-shopping-cart"></i>
                <h3>Giỏ hàng của bạn đang trống</h3>
                <p>Hãy khám phá các sản phẩm tuyệt vời của chúng tôi và thêm vào giỏ hàng!</p>
                <a href="index.html" class="btn btn-primary btn-lg">
                    <i class="fas fa-arrow-left me-2"></i>Tiếp tục mua sắm
                </a>
            </div>
        </div>
    `;
}

function renderCartItems(container) {
    const subtotal = calculateSubtotal();
    const discount = calculateDiscount(subtotal);
    const shipping = calculateShipping(subtotal);
    const total = subtotal - discount + shipping;
    
    container.innerHTML = `
        <div class="col-lg-8">
            <!-- Cart Actions Bar -->
            <div class="cart-actions-bar" data-aos="fade-right">
                <div class="row">
                    <div class="col-md-6">
                        <div class="items-count">
                            <i class="fas fa-shopping-cart me-2"></i>
                            ${cart.length} sản phẩm trong giỏ hàng
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="bulk-actions">
                            <label class="select-all">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                <span class="ms-2">Chọn tất cả</span>
                            </label>
                            <button class="clear-cart" onclick="clearCart()">
                                <i class="fas fa-trash me-1"></i>Xóa giỏ hàng
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Cart Items -->
            <div class="cart-items" data-aos="fade-right" data-aos-delay="100">
                <div class="cart-items-header">
                    <div class="row">
                        <div class="col-md-6">Sản phẩm</div>
                        <div class="col-md-2 text-center">Số lượng</div>
                        <div class="col-md-2 text-center">Đơn giá</div>
                        <div class="col-md-2 text-center">Thành tiền</div>
                    </div>
                </div>
                
                ${cart.map((item, index) => renderCartItem(item, index)).join('')}
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Cart Summary -->
            <div class="cart-summary" data-aos="fade-left">
                <h3 class="cart-summary-title">
                    <i class="fas fa-receipt me-2"></i>Tóm tắt đơn hàng
                </h3>
                
                <div class="summary-row">
                    <span class="summary-label">Tạm tính (${cart.length} sản phẩm):</span>
                    <span class="summary-value">${formatCurrency(subtotal)}</span>
                </div>
                
                ${discount > 0 ? `
                    <div class="summary-row">
                        <span class="summary-label">Giảm giá:</span>
                        <span class="summary-value discount">-${formatCurrency(discount)}</span>
                    </div>
                ` : ''}
                
                <div class="summary-row">
                    <span class="summary-label">Phí vận chuyển:</span>
                    <span class="summary-value shipping">
                        ${shipping === 0 ? 'Miễn phí' : formatCurrency(shipping)}
                    </span>
                </div>
                
                ${subtotal < freeShippingThreshold ? `
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-truck me-2"></i>
                        Mua thêm ${formatCurrency(freeShippingThreshold - subtotal)} để được miễn phí vận chuyển!
                    </div>
                ` : ''}
                
                <div class="summary-row total">
                    <span class="summary-label">Tổng cộng:</span>
                    <span class="summary-value">${formatCurrency(total)}</span>
                </div>
                
                <!-- Coupon Section -->
                <div class="coupon-section">
                    <h6><i class="fas fa-tag me-2"></i>Mã giảm giá</h6>
                    ${appliedCoupon ? renderAppliedCoupon() : renderCouponInput()}
                </div>
                
                <!-- Checkout Actions -->
                <div class="checkout-actions">
                    <button class="checkout-btn" onclick="proceedToCheckout()">
                        <i class="fas fa-credit-card me-2"></i>Tiến hành thanh toán
                    </button>
                    <a href="index.html" class="continue-shopping">
                        <i class="fas fa-arrow-left me-2"></i>Tiếp tục mua sắm
                    </a>
                </div>
                
                <!-- Security Info -->
                <div class="security-info">
                    <i class="fas fa-shield-alt"></i>
                    <small>Thanh toán an toàn và bảo mật</small><br>
                    <small>Hỗ trợ 24/7 - Đổi trả trong 7 ngày</small>
                </div>
            </div>
        </div>
    `;
}

function renderCartItem(item, index) {
    const product = sampleProducts.find(p => p.id === item.id);
    const itemTotal = item.price * item.quantity;
    
    return `
        <div class="cart-item" id="cartItem${item.id}">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <input type="checkbox" class="item-checkbox me-3" data-id="${item.id}">
                        <img src="${item.image}" alt="${item.name}" class="cart-item-image me-3">
                        <div class="cart-item-info">
                            <h6 class="cart-item-name">
                                <a href="product.html?id=${item.id}">${item.name}</a>
                            </h6>
                            <p class="cart-item-description">${product ? product.description : ''}</p>
                            <span class="cart-item-condition ${getConditionClass(item.condition)}">
                                ${getConditionText(item.condition)}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="quantity-controls">
                        <button class="quantity-btn" onclick="updateCartItemQuantity(${item.id}, ${item.quantity - 1})" ${item.quantity <= 1 ? 'disabled' : ''}>
                            -
                        </button>
                        <input type="number" class="quantity-input" value="${item.quantity}" min="1" max="10" 
                               onchange="updateCartItemQuantity(${item.id}, this.value)">
                        <button class="quantity-btn" onclick="updateCartItemQuantity(${item.id}, ${item.quantity + 1})" ${item.quantity >= 10 ? 'disabled' : ''}>
                            +
                        </button>
                    </div>
                </div>
                
                <div class="col-md-2 text-center">
                    <div class="cart-item-price">${formatCurrency(item.price)}</div>
                </div>
                
                <div class="col-md-2">
                    <div class="cart-item-actions">
                        <div class="cart-item-price fw-bold">${formatCurrency(itemTotal)}</div>
                        <button class="remove-item" onclick="removeCartItem(${item.id})">
                            <i class="fas fa-trash me-1"></i>Xóa
                        </button>
                        <button class="save-for-later" onclick="saveForLater(${item.id})">
                            <i class="fas fa-heart me-1"></i>Lưu
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderCouponInput() {
    return `
        <div class="coupon-input">
            <input type="text" id="couponCode" placeholder="Nhập mã giảm giá" maxlength="20">
            <button onclick="applyCoupon()">Áp dụng</button>
        </div>
        <small class="text-muted">
            Mã khuyến mãi: WELCOME10, SAVE50K, FREESHIP, ELECTRONICS20
        </small>
    `;
}

function renderAppliedCoupon() {
    return `
        <div class="applied-coupon">
            <span>
                <i class="fas fa-tag me-2"></i>
                ${appliedCoupon.code} - ${appliedCoupon.description}
            </span>
            <button class="remove-coupon" onclick="removeCoupon()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
}

// Cart calculation functions
function calculateSubtotal() {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
}

function calculateDiscount(subtotal) {
    if (!appliedCoupon) return 0;
    
    if (appliedCoupon.discount) {
        if (appliedCoupon.discount < 1) {
            // Percentage discount
            return subtotal * appliedCoupon.discount;
        } else {
            // Fixed amount discount
            return Math.min(appliedCoupon.discount, subtotal);
        }
    }
    
    return 0;
}

function calculateShipping(subtotal) {
    if (appliedCoupon && appliedCoupon.freeShipping) return 0;
    if (subtotal >= freeShippingThreshold) return 0;
    return 30000; // 30K shipping fee
}

// Cart action functions
function updateCartItemQuantity(productId, newQuantity) {
    newQuantity = parseInt(newQuantity);
    
    if (newQuantity < 1) {
        removeCartItem(productId);
        return;
    }
    
    if (newQuantity > 10) {
        showNotification('Số lượng tối đa là 10', 'warning');
        return;
    }
    
    // Add updating class for visual feedback
    const itemElement = document.getElementById(`cartItem${productId}`);
    itemElement.classList.add('updating');
    
    setTimeout(() => {
        updateCartQuantity(productId, newQuantity);
        loadCartContent(); // Refresh cart display
        showNotification('Đã cập nhật số lượng', 'success');
    }, 500);
}

function removeCartItem(productId) {
    const itemElement = document.getElementById(`cartItem${productId}`);
    itemElement.classList.add('removing');
    
    setTimeout(() => {
        removeFromCart(productId);
        loadCartContent(); // Refresh cart display
        
        if (cart.length === 0) {
            // Hide related products section if cart is empty
            document.getElementById('relatedProductsSection').style.display = 'none';
        }
    }, 300);
}

function saveForLater(productId) {
    // Move item from cart to wishlist
    const item = cart.find(item => item.id === productId);
    if (item) {
        toggleWishlist(productId);
        removeFromCart(productId);
        loadCartContent();
        showNotification('Đã lưu sản phẩm vào danh sách yêu thích', 'info');
    }
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    
    itemCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function clearCart() {
    if (cart.length === 0) return;
    
    if (confirm('Bạn có chắc chắn muốn xóa tất cả sản phẩm trong giỏ hàng?')) {
        cart = [];
        localStorage.setItem('cart', JSON.stringify(cart));
        updateCartDisplay();
        loadCartContent();
        showNotification('Đã xóa tất cả sản phẩm trong giỏ hàng', 'info');
    }
}

// Coupon functions
function applyCoupon() {
    const couponCode = document.getElementById('couponCode').value.trim().toUpperCase();
    
    if (!couponCode) {
        showNotification('Vui lòng nhập mã giảm giá', 'warning');
        return;
    }
    
    const coupon = availableCoupons[couponCode];
    if (!coupon) {
        showNotification('Mã giảm giá không hợp lệ', 'error');
        return;
    }
    
    const subtotal = calculateSubtotal();
    if (subtotal < coupon.minOrder) {
        showNotification(`Đơn hàng tối thiểu ${formatCurrency(coupon.minOrder)} để sử dụng mã này`, 'warning');
        return;
    }
    
    appliedCoupon = {
        code: couponCode,
        ...coupon
    };
    
    loadCartContent(); // Refresh to show applied coupon
    showNotification(`Đã áp dụng mã giảm giá ${couponCode}`, 'success');
}

function removeCoupon() {
    appliedCoupon = null;
    loadCartContent(); // Refresh to remove coupon
    showNotification('Đã hủy mã giảm giá', 'info');
}

function proceedToCheckout() {
    if (cart.length === 0) {
        showNotification('Giỏ hàng trống', 'warning');
        return;
    }
    
    // Save applied coupon to localStorage for checkout page
    if (appliedCoupon) {
        localStorage.setItem('appliedCoupon', JSON.stringify(appliedCoupon));
    }
    
    window.location.href = 'checkout.html';
}

function loadRelatedProducts() {
    const container = document.getElementById('relatedProductsContainer');
    const section = document.getElementById('relatedProductsSection');
    
    if (cart.length === 0) {
        section.style.display = 'none';
        return;
    }
    
    // Get categories from cart items
    const cartCategories = [...new Set(cart.map(item => {
        const product = sampleProducts.find(p => p.id === item.id);
        return product ? product.category : null;
    }).filter(Boolean))];
    
    // Find related products
    const relatedProducts = sampleProducts
        .filter(product => 
            cartCategories.includes(product.category) && 
            !cart.some(item => item.id === product.id)
        )
        .slice(0, 4);
    
    if (relatedProducts.length > 0) {
        container.innerHTML = relatedProducts.map(product => createProductCard(product)).join('');
        section.style.display = 'block';
    } else {
        section.style.display = 'none';
    }
}

// Export functions for global access
window.updateCartItemQuantity = updateCartItemQuantity;
window.removeCartItem = removeCartItem;
window.saveForLater = saveForLater;
window.toggleSelectAll = toggleSelectAll;
window.clearCart = clearCart;
window.applyCoupon = applyCoupon;
window.removeCoupon = removeCoupon;
window.proceedToCheckout = proceedToCheckout;
