@echo off
echo ========================================
echo    XAMPP Installation for Electronics Shop
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator - OK
) else (
    echo ERROR: Please run this script as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo.
echo Checking if XAMPP is already installed...
if exist "C:\xampp\xampp-control.exe" (
    echo XAMPP is already installed at C:\xampp\
    echo Starting XAMPP Control Panel...
    start "" "C:\xampp\xampp-control.exe"
    goto :configure
)

echo.
echo XAMPP not found. Starting download...
echo.

REM Create temp directory
if not exist "%TEMP%\xampp-installer" mkdir "%TEMP%\xampp-installer"
cd /d "%TEMP%\xampp-installer"

echo Downloading XAMPP installer...
powershell -Command "& {Invoke-WebRequest -Uri 'https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download' -OutFile 'xampp-installer.exe'}"

if not exist "xampp-installer.exe" (
    echo ERROR: Failed to download XAMPP installer
    echo Please download manually from: https://www.apachefriends.org/
    pause
    exit /b 1
)

echo.
echo Installing XAMPP...
echo Please follow the installation wizard.
echo Recommended components: Apache, MySQL, PHP, phpMyAdmin
echo.
start /wait xampp-installer.exe

:configure
echo.
echo ========================================
echo    Configuring XAMPP for Electronics Shop
echo ========================================
echo.

REM Check if XAMPP was installed successfully
if not exist "C:\xampp\xampp-control.exe" (
    echo ERROR: XAMPP installation failed or not found
    pause
    exit /b 1
)

echo Starting XAMPP services...
cd /d "C:\xampp"

REM Start Apache
echo Starting Apache...
xampp_start.exe

REM Start MySQL
echo Starting MySQL...
mysql\bin\mysqld.exe --defaults-file=mysql\bin\my.ini --standalone --console

echo.
echo ========================================
echo    XAMPP Installation Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Open XAMPP Control Panel
echo 2. Start Apache and MySQL services
echo 3. Open http://localhost in your browser
echo 4. Access phpMyAdmin at http://localhost/phpmyadmin
echo.
echo Press any key to open XAMPP Control Panel...
pause >nul
start "" "C:\xampp\xampp-control.exe"

echo.
echo Installation script completed!
pause
