# 🛒 Electronics Shop - Website Bán Thiết Bị Điện Tử Cũ

[![Jo<PERSON><PERSON>](https://img.shields.io/badge/Joomla-4.x-blue.svg)](https://www.joomla.org/)
[![VirtueMart](https://img.shields.io/badge/VirtueMart-4.x-green.svg)](https://virtuemart.net/)
[![PHP](https://img.shields.io/badge/PHP-8.1-purple.svg)](https://www.php.net/)
[![License](https://img.shields.io/badge/License-GPL%20v2+-orange.svg)](LICENSE)

Website thương mại điện tử chuyên nghiệp cho việc mua bán thiết bị điện tử cũ, đ<PERSON><PERSON><PERSON> xây dựng trên nền tảng Joomla 4 với VirtueMart extension.

## 🌟 Tính Năng Nổi Bật

### 💻 Quản L<PERSON>m Thông Minh
- **<PERSON><PERSON> loại chi tiế<PERSON>**: <PERSON><PERSON><PERSON>, đ<PERSON><PERSON><PERSON> tho<PERSON>, m<PERSON><PERSON><PERSON> b<PERSON>, <PERSON>h<PERSON>, linh kiện
- **Đánh giá tình trạng**: Như mới (95%), Tốt (85%), Khá tốt (75%), Cần sửa chữa (<70%)
- **Thông số kỹ thuật**: Chi tiết đầy đủ cho từng sản phẩm
- **Hình ảnh đa góc**: Gallery hình ảnh chất lượng cao

### 💳 Hệ Thống Thanh Toán Đa Dạng
- **COD**: Thanh toán khi nhận hàng
- **Chuyển khoản**: Ngân hàng truyền thống
- **Ví điện tử**: MoMo, ZaloPay, VNPay

### 🛡️ Quản Lý Bảo Hành & Chất Lượng
- **Bảo hành rõ ràng**: Thời gian và điều kiện cụ thể
- **Đánh giá chi tiết**: Hệ thống review đa chiều
- **Lịch sử sản phẩm**: Thông tin về chủ sở hữu trước

### 📱 Giao Diện Hiện Đại
- **Responsive Design**: Tối ưu cho mọi thiết bị
- **Bootstrap 5**: Framework CSS hiện đại
- **Tiếng Việt**: Hoàn toàn bản địa hóa
- **UX/UI**: Trải nghiệm người dùng mượt mà

## 🚀 Cài Đặt Nhanh

### Tự Động (Khuyến nghị)
```bash
# 1. Cài đặt XAMPP
.\install-xampp.bat

# 2. Tải và setup Joomla
.\download-joomla.ps1

# 3. Tải VirtueMart
.\download-virtuemart.ps1

# 4. Kiểm thử website
.\test-website.ps1
```

### Thủ Công
1. **Cài đặt XAMPP**: Tải từ [apachefriends.org](https://www.apachefriends.org/)
2. **Setup Joomla**: Tạo database và cài đặt Joomla 4.x
3. **Cài VirtueMart**: Upload và cài đặt VirtueMart 4.x
4. **Import Database**: Chạy `Database/database-design.sql`
5. **Import Sample Data**: Chạy `Sample-Data/sample-products.sql`

## 📁 Cấu Trúc Dự Án

```
ElectronicsShop-Joomla/
├── 📄 README.md                      # Tài liệu chính
├── 📄 PROJECT-SUMMARY.md             # Tổng kết dự án
├── 🔧 install-xampp.bat              # Script cài XAMPP
├── 🔧 download-joomla.ps1            # Script tải Joomla
├── 🔧 download-virtuemart.ps1        # Script tải VirtueMart
├── 🧪 test-website.ps1               # Script kiểm thử
│
├── 📚 Documentation/                 # Tài liệu chi tiết
│   ├── INSTALLATION-GUIDE.md        # Hướng dẫn cài đặt
│   ├── VIRTUEMART-SETUP.md         # Cấu hình VirtueMart
│   ├── QUICK-START-GUIDE.md        # Khởi động nhanh
│   └── TESTING-OPTIMIZATION.md     # Kiểm thử & tối ưu
│
├── 🗄️ Database/                     # Thiết kế CSDL
│   └── database-design.sql         # Schema mở rộng
│
├── 🎨 Templates/                    # Giao diện
│   └── electronics-shop/          # Template chính
│       ├── templateDetails.xml    # Cấu hình
│       ├── index.php             # Layout
│       ├── css/template.css      # Styles
│       ├── js/template.js        # Scripts
│       └── language/             # Ngôn ngữ
│
├── 🔌 Custom-Extensions/           # Extensions tùy chỉnh
│   └── mod_product_condition/     # Module tình trạng
│
└── 📊 Sample-Data/                # Dữ liệu mẫu
    ├── sample-products.sql       # Sản phẩm mẫu
    ├── create-sample-images.ps1  # Script hình ảnh
    └── Images/                   # Thư mục hình ảnh
```

## 🛍️ Danh Mục Sản Phẩm

| Danh Mục | Phân Loại | Mô Tả |
|----------|-----------|-------|
| 💻 **Laptop cũ** | Văn phòng, Gaming, Đồ họa | Laptop đã qua sử dụng chất lượng cao |
| 📱 **Điện thoại cũ** | iPhone, Samsung, Xiaomi, Oppo | Smartphone các thương hiệu nổi tiếng |
| 📟 **Máy tính bảng** | iPad, Android Tablet | Tablet cho giải trí và công việc |
| 🎧 **Phụ kiện** | Tai nghe, Sạc, Ốp lưng | Phụ kiện chính hãng và tương thích |
| 🔧 **Linh kiện** | RAM, SSD, VGA | Linh kiện nâng cấp và thay thế |

## 🏷️ Hệ Thống Đánh Giá Tình Trạng

| Tình Trạng | Phần Trăm | Màu Sắc | Mô Tả Chi Tiết |
|------------|-----------|---------|----------------|
| 🟢 **Như mới** | 95-99% | Xanh lá | Ít sử dụng, không vết xước, hoạt động hoàn hảo |
| 🔵 **Tốt** | 85-94% | Xanh dương | Hoạt động bình thường, vết xước nhẹ không ảnh hưởng |
| 🟡 **Khá tốt** | 70-84% | Vàng | Hoạt động ổn định, có dấu hiệu sử dụng rõ ràng |
| 🔴 **Cần sửa chữa** | <70% | Đỏ | Có lỗi nhỏ, cần khắc phục một số vấn đề |

## 💰 Phương Thức Thanh Toán & Vận Chuyển

### Thanh Toán
- 💵 **COD**: Thanh toán khi nhận hàng
- 🏦 **Chuyển khoản**: Ngân hàng truyền thống
- 📱 **Ví điện tử**: MoMo, ZaloPay, VNPay

### Vận Chuyển
- 🚚 **Tiêu chuẩn**: 30,000₫ (3-5 ngày)
- ⚡ **Nhanh**: 50,000₫ (1-2 ngày)
- 🆓 **Miễn phí**: Đơn hàng > 5,000,000₫

## 📋 Yêu Cầu Hệ Thống

### Tối Thiểu
- **OS**: Windows 10/11
- **RAM**: 4GB
- **Storage**: 2GB free
- **Web Server**: Apache 2.4+
- **PHP**: 8.0+
- **Database**: MySQL 5.7+

### Khuyến Nghị
- **RAM**: 8GB+
- **PHP**: 8.1+
- **Database**: MySQL 8.0+
- **SSD**: Để tăng hiệu suất

## 📖 Tài Liệu Hướng Dẫn

| Tài Liệu | Mô Tả | Link |
|----------|-------|------|
| 🚀 **Quick Start** | Hướng dẫn khởi động nhanh | [QUICK-START-GUIDE.md](Documentation/QUICK-START-GUIDE.md) |
| 📥 **Installation** | Cài đặt chi tiết từng bước | [INSTALLATION-GUIDE.md](Documentation/INSTALLATION-GUIDE.md) |
| ⚙️ **VirtueMart Setup** | Cấu hình VirtueMart | [VIRTUEMART-SETUP.md](Documentation/VIRTUEMART-SETUP.md) |
| 🧪 **Testing** | Kiểm thử và tối ưu hóa | [TESTING-OPTIMIZATION.md](Documentation/TESTING-OPTIMIZATION.md) |

## 🤝 Đóng Góp

Chúng tôi hoan nghênh mọi đóng góp! Vui lòng:

1. Fork dự án
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Mở Pull Request

## 📞 Liên Hệ & Hỗ Trợ

- **Email**: <EMAIL>
- **Website**: https://electronicsshop.com
- **Issues**: [GitHub Issues](../../issues)

## 📄 Giấy Phép

Dự án này được phân phối dưới giấy phép GNU General Public License v2+. Xem file [LICENSE](LICENSE) để biết thêm chi tiết.

---

<div align="center">

**🌟 Nếu dự án hữu ích, hãy cho chúng tôi một Star! ⭐**

Made with ❤️ by Electronics Shop Team

</div>
