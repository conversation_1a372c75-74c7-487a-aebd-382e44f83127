@model List<GocPho.Models.Category>
@{
    ViewData["Title"] = "Quản lý danh mục";
    Layout = "_AdminLayout";
}

<div class="row">
    <div class="col-12">
        <div class="card shadow border-0">
            <div class="card-header" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-tags me-2"></i>Quản lý danh mục
                    </h5>
                    <a href="@Url.Action("CreateCategory", "Admin")" class="btn btn-light btn-sm">
                        <i class="fas fa-plus me-1"></i>Thêm danh mục
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                @if (Model.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Tên danh mục</th>
                                    <th>Mô tả</th>
                                    <th>Số sản phẩm</th>
                                    <th class="text-center">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var category in Model)
                                {
                                    <tr>
                                        <td>
                                            <span class="badge bg-primary fs-6">#@category.Id</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="category-icon me-3">
                                                    <i class="fas fa-tag"></i>
                                                </div>
                                                <div>
                                                    <strong>@category.Name</strong>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(category.Description))
                                            {
                                                <span>@category.Description</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">Chưa có mô tả</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-info fs-6">
                                                @{
                                                    var productCounts = ViewBag.ProductCounts as Dictionary<int, int>;
                                                    var count = productCounts?.ContainsKey(category.Id) == true ? productCounts[category.Id] : 0;
                                                }
                                                @count sản phẩm
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="@Url.Action("EditCategory", "Admin", new { id = category.Id })" 
                                                   class="btn btn-sm btn-outline-warning" title="Chỉnh sửa">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form method="post" asp-action="DeleteCategory" asp-route-id="@category.Id" 
                                                      class="d-inline" onsubmit="return confirm('Bạn có chắc muốn xóa danh mục này?')">
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Xóa">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Không có danh mục nào</h5>
                        <p class="text-muted">Chưa có danh mục nào được tạo trong hệ thống.</p>
                        <a href="@Url.Action("CreateCategory", "Admin")" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Tạo danh mục đầu tiên
                        </a>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stats-icon bg-primary text-white rounded-circle mx-auto mb-3">
                    <i class="fas fa-tags"></i>
                </div>
                <h4 class="fw-bold">@Model.Count</h4>
                <p class="text-muted mb-0">Tổng danh mục</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stats-icon bg-success text-white rounded-circle mx-auto mb-3">
                    <i class="fas fa-box"></i>
                </div>
                <h4 class="fw-bold">
                    @{
                        var productCounts2 = ViewBag.ProductCounts as Dictionary<int, int>;
                        var totalProducts = productCounts2?.Values.Sum() ?? 0;
                    }
                    @totalProducts
                </h4>
                <p class="text-muted mb-0">Tổng sản phẩm</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stats-icon bg-info text-white rounded-circle mx-auto mb-3">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <h4 class="fw-bold">
                    @if (Model.Any())
                    {
                        var productCounts3 = ViewBag.ProductCounts as Dictionary<int, int>;
                        var totalProducts3 = productCounts3?.Values.Sum() ?? 0;
                        @Math.Round(totalProducts3 / (double)Model.Count, 1)
                    }
                    else
                    {
                        <text>0</text>
                    }
                </h4>
                <p class="text-muted mb-0">Trung bình/danh mục</p>
            </div>
        </div>
    </div>
</div>

<style>
    .category-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 16px;
    }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }
</style>
